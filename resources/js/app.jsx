import "../css/app.css";
import "./bootstrap";

import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { createRoot, hydrateRoot } from "react-dom/client";
import { store } from "./redux/store";
import { Provider } from "react-redux";
import * as Sentry from "@sentry/react";
import { StrictMode } from "react";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_REACT_DSN,
    sendDefaultPii: true,
    integrations: [Sentry.replayIntegration()],
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
});

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.jsx`,
            import.meta.glob("./Pages/**/*.jsx"),
        ),
    setup({ el, App, props }) {
        const appComponent = (
            // <StrictMode>
            <Provider store={store}>
                <App {...props} />
            </Provider>
            // </StrictMode>
        );

        // Use hydrateRoot for SSR, createRoot for client-side rendering
        if (el.hasChildNodes()) {
            hydrateRoot(el, appComponent);
        } else {
            const root = createRoot(el);
            root.render(appComponent);
        }
    },
    progress: {
        color: "#FFFFFF",
    },
});
