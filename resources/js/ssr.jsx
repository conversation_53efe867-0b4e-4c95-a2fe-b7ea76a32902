import { createInertiaApp } from "@inertiajs/react";
import createServer from "@inertiajs/react/server";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { renderToString } from "react-dom/server";
import { Provider } from "react-redux";
import { store } from "./redux/store";
import * as Sentry from "@sentry/react";
import { route } from "../../vendor/tightenco/ziggy/dist/index.esm.js";
import { Ziggy } from "./ziggy.js";

const appName = import.meta.env.VITE_APP_NAME || "Laravel";

// Create a route function for SSR with proper Ziggy configuration
const ssrRoute = (name, params, absolute, config = Ziggy) => {
    try {
        return route(name, params, absolute, config);
    } catch (error) {
        // Fallback for SSR when route generation fails
        console.warn(
            `SSR Warning: Route '${name}' could not be generated:`,
            error.message,
        );
        return "#"; // Return a safe fallback URL
    }
};

// Make route function globally available for SSR
global.route = ssrRoute;

// Initialize Sentry for SSR
Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_REACT_DSN,
    sendDefaultPii: true,
    integrations: [
        // Note: Replay integration is not available in SSR
    ],
    environment: "ssr",
});

createServer((page) =>
    createInertiaApp({
        page,
        render: renderToString,
        title: (title) => `${title} - ${appName}`,
        resolve: (name) =>
            resolvePageComponent(
                `./Pages/${name}.jsx`,
                import.meta.glob("./Pages/**/*.jsx"),
            ),
        setup: ({ App, props }) => (
            <Provider store={store}>
                <App {...props} />
            </Provider>
        ),
    }),
);
