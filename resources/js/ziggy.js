const Ziggy = {
    url: "http:\/\/ticketgol.test",
    port: null,
    defaults: {},
    routes: {
        "debugbar.openhandler": {
            uri: "_debugbar\/open",
            methods: ["GET", "HEAD"],
        },
        "debugbar.clockwork": {
            uri: "_debugbar\/clockwork\/{id}",
            methods: ["GET", "HEAD"],
            parameters: ["id"],
        },
        "debugbar.telescope": {
            uri: "_debugbar\/telescope\/{id}",
            methods: ["GET", "HEAD"],
            parameters: ["id"],
        },
        "debugbar.assets.css": {
            uri: "_debugbar\/assets\/stylesheets",
            methods: ["GET", "HEAD"],
        },
        "debugbar.assets.js": {
            uri: "_debugbar\/assets\/javascript",
            methods: ["GET", "HEAD"],
        },
        "debugbar.cache.delete": {
            uri: "_debugbar\/cache\/{key}\/{tags?}",
            methods: ["DELETE"],
            parameters: ["key", "tags"],
        },
        "debugbar.queries.explain": {
            uri: "_debugbar\/queries\/explain",
            methods: ["POST"],
        },
        "filament.exports.download": {
            uri: "filament\/exports\/{export}\/download",
            methods: ["GET", "HEAD"],
            parameters: ["export"],
            bindings: { export: "id" },
        },
        "filament.imports.failed-rows.download": {
            uri: "filament\/imports\/{import}\/failed-rows\/download",
            methods: ["GET", "HEAD"],
            parameters: ["import"],
            bindings: { import: "id" },
        },
        "filament.admin.auth.login": {
            uri: "login",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.auth.logout": {
            uri: "logout",
            methods: ["POST"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.pages.dashboard": {
            uri: "\/",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.pages.filament-laravel-pulse": {
            uri: "filament-laravel-pulse",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.pages.settings": {
            uri: "settings",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.pages.health-check-results": {
            uri: "health-check-results",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.admin-users.index": {
            uri: "admin-users",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.clubs.index": {
            uri: "clubs",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.cms-pages.index": {
            uri: "cms-pages",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.countries.index": {
            uri: "countries",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.customers.index": {
            uri: "customers",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.customers.edit": {
            uri: "customers\/{record}\/edit",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        "filament.admin.resources.email-templates.index": {
            uri: "email-templates",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.events.index": {
            uri: "events",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.leagues.index": {
            uri: "leagues",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.orders.index": {
            uri: "orders",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.orders.edit": {
            uri: "orders\/{record}\/edit",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        "filament.admin.resources.orders.view": {
            uri: "orders\/{record}",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        "filament.admin.resources.restrictions.index": {
            uri: "restrictions",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.shield.roles.index": {
            uri: "shield\/roles",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.shield.roles.create": {
            uri: "shield\/roles\/create",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.shield.roles.view": {
            uri: "shield\/roles\/{record}",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        "filament.admin.resources.shield.roles.edit": {
            uri: "shield\/roles\/{record}\/edit",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        "filament.admin.resources.seasons.index": {
            uri: "seasons",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.stadiums.index": {
            uri: "stadiums",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.stadiums.edit": {
            uri: "stadiums\/{record}\/edit",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        "filament.admin.resources.support-requests.index": {
            uri: "support-requests",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.support-requests.create": {
            uri: "support-requests\/create",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.support-requests.edit": {
            uri: "support-requests\/{record}\/edit",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        "filament.admin.resources.tickets.index": {
            uri: "tickets",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.activity-logs.index": {
            uri: "activity-logs",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
        },
        "filament.admin.resources.activity-logs.view": {
            uri: "activity-logs\/{record}",
            methods: ["GET", "HEAD"],
            domain: "admin.ticketgol.test",
            parameters: ["record"],
        },
        pulse: { uri: "pulse", methods: ["GET", "HEAD"] },
        "sanctum.csrf-cookie": {
            uri: "sanctum\/csrf-cookie",
            methods: ["GET", "HEAD"],
        },
        telescope: {
            uri: "telescope\/{view?}",
            methods: ["GET", "HEAD"],
            wheres: { view: "(.*)" },
            parameters: ["view"],
        },
        "livewire.update": { uri: "livewire\/update", methods: ["POST"] },
        "livewire.upload-file": {
            uri: "livewire\/upload-file",
            methods: ["POST"],
        },
        "livewire.preview-file": {
            uri: "livewire\/preview-file\/{filename}",
            methods: ["GET", "HEAD"],
            parameters: ["filename"],
        },
        "log-viewer.hosts": {
            uri: "log-viewer\/api\/hosts",
            methods: ["GET", "HEAD"],
        },
        "log-viewer.folders": {
            uri: "log-viewer\/api\/folders",
            methods: ["GET", "HEAD"],
        },
        "log-viewer.folders.request-download": {
            uri: "log-viewer\/api\/folders\/{folderIdentifier}\/download\/request",
            methods: ["GET", "HEAD"],
            parameters: ["folderIdentifier"],
        },
        "log-viewer.folders.clear-cache": {
            uri: "log-viewer\/api\/folders\/{folderIdentifier}\/clear-cache",
            methods: ["POST"],
            parameters: ["folderIdentifier"],
        },
        "log-viewer.folders.delete": {
            uri: "log-viewer\/api\/folders\/{folderIdentifier}",
            methods: ["DELETE"],
            parameters: ["folderIdentifier"],
        },
        "log-viewer.files": {
            uri: "log-viewer\/api\/files",
            methods: ["GET", "HEAD"],
        },
        "log-viewer.files.request-download": {
            uri: "log-viewer\/api\/files\/{fileIdentifier}\/download\/request",
            methods: ["GET", "HEAD"],
            parameters: ["fileIdentifier"],
        },
        "log-viewer.files.clear-cache": {
            uri: "log-viewer\/api\/files\/{fileIdentifier}\/clear-cache",
            methods: ["POST"],
            parameters: ["fileIdentifier"],
        },
        "log-viewer.files.delete": {
            uri: "log-viewer\/api\/files\/{fileIdentifier}",
            methods: ["DELETE"],
            parameters: ["fileIdentifier"],
        },
        "log-viewer.files.clear-cache-all": {
            uri: "log-viewer\/api\/clear-cache-all",
            methods: ["POST"],
        },
        "log-viewer.files.delete-multiple-files": {
            uri: "log-viewer\/api\/delete-multiple-files",
            methods: ["POST"],
        },
        "log-viewer.logs": {
            uri: "log-viewer\/api\/logs",
            methods: ["GET", "HEAD"],
        },
        "log-viewer.folders.download": {
            uri: "log-viewer\/api\/folders\/{folderIdentifier}\/download",
            methods: ["GET", "HEAD"],
            parameters: ["folderIdentifier"],
        },
        "log-viewer.files.download": {
            uri: "log-viewer\/api\/files\/{fileIdentifier}\/download",
            methods: ["GET", "HEAD"],
            parameters: ["fileIdentifier"],
        },
        "log-viewer.index": {
            uri: "log-viewer\/{view?}",
            methods: ["GET", "HEAD"],
            wheres: { view: "(.*)" },
            parameters: ["view"],
        },
        "resend.webhook": { uri: "resend\/webhook", methods: ["POST"] },
        "api.home.index": { uri: "api\/v1\/home", methods: ["GET", "HEAD"] },
        "api.search.index": {
            uri: "api\/v1\/search",
            methods: ["GET", "HEAD"],
        },
        "api.search.suggestions": {
            uri: "api\/v1\/search\/suggestions",
            methods: ["GET", "HEAD"],
        },
        "api.translations.index": {
            uri: "api\/v1\/translations",
            methods: ["GET", "HEAD"],
        },
        "api.events.index": { uri: "api\/v1\/events", methods: ["POST"] },
        "api.events.selltickets": {
            uri: "api\/v1\/events\/selltickets",
            methods: ["POST"],
        },
        "api.events.filters": {
            uri: "api\/v1\/events\/filters",
            methods: ["GET", "HEAD"],
        },
        "api.events.show": {
            uri: "api\/v1\/events\/{slug}",
            methods: ["GET", "HEAD"],
            parameters: ["slug"],
        },
        "api.stadiums.index": { uri: "api\/v1\/stadiums", methods: ["POST"] },
        "api.stadiums.filters": {
            uri: "api\/v1\/stadiums\/filters",
            methods: ["GET", "HEAD"],
        },
        "api.stadiums.show": {
            uri: "api\/v1\/stadiums\/{slug}",
            methods: ["GET", "HEAD"],
            parameters: ["slug"],
        },
        "api.clubs.index": { uri: "api\/v1\/clubs", methods: ["POST"] },
        "api.clubs.filters": {
            uri: "api\/v1\/clubs\/filters",
            methods: ["GET", "HEAD"],
        },
        "api.clubs.show": {
            uri: "api\/v1\/clubs\/{slug}",
            methods: ["GET", "HEAD"],
            parameters: ["slug"],
        },
        "api.leagues.index": { uri: "api\/v1\/leagues", methods: ["POST"] },
        "api.leagues.filters": {
            uri: "api\/v1\/leagues\/filters",
            methods: ["GET", "HEAD"],
        },
        "api.leagues.show": {
            uri: "api\/v1\/leagues\/{slug}",
            methods: ["GET", "HEAD"],
            parameters: ["slug"],
        },
        "api.tickets.index": { uri: "api\/v1\/tickets", methods: ["POST"] },
        "api.user": { uri: "api\/v1\/user", methods: ["GET", "HEAD"] },
        "api.logout": { uri: "api\/v1\/auth\/logout", methods: ["POST"] },
        "api.tickets.my-tickets": {
            uri: "api\/v1\/tickets\/my-listing",
            methods: ["POST"],
        },
        "api.tickets.store": {
            uri: "api\/v1\/tickets\/add",
            methods: ["POST"],
        },
        "api.tickets.configurations": {
            uri: "api\/v1\/tickets\/configurations",
            methods: ["GET", "HEAD"],
        },
        "api.tickets.show": {
            uri: "api\/v1\/tickets\/{ticketNo}",
            methods: ["GET", "HEAD"],
            parameters: ["ticketNo"],
        },
        "api.tickets.update": {
            uri: "api\/v1\/tickets\/update",
            methods: ["POST"],
        },
        "api.tickets.delete": {
            uri: "api\/v1\/tickets\/delete\/{ticketNo}",
            methods: ["DELETE"],
            parameters: ["ticketNo"],
        },
        "api.reservations.create": {
            uri: "api\/v1\/reservation\/lock",
            methods: ["POST"],
        },
        "api.reservations.detail": {
            uri: "api\/v1\/reservation\/detail\/{reservationId}",
            methods: ["GET", "HEAD"],
            parameters: ["reservationId"],
        },
        "api.reservations.check-active": {
            uri: "api\/v1\/reservation\/check-active",
            methods: ["GET", "HEAD"],
        },
        "api.reservations.cancel": {
            uri: "api\/v1\/reservation",
            methods: ["DELETE"],
        },
        "api.checkout.create": {
            uri: "api\/v1\/checkout\/session",
            methods: ["POST"],
        },
        "api.orders.index": { uri: "api\/v1\/orders", methods: ["POST"] },
        "api.orders.store": {
            uri: "api\/v1\/orders\/create",
            methods: ["POST"],
        },
        "api.orders.check-status": {
            uri: "api\/v1\/orders\/check-status",
            methods: ["POST"],
        },
        "api.orders.statuses": {
            uri: "api\/v1\/orders\/statuses",
            methods: ["GET", "HEAD"],
        },
        "api.orders.download-invoice": {
            uri: "api\/v1\/orders\/invoice\/{orderId}",
            methods: ["GET", "HEAD"],
            parameters: ["orderId"],
        },
        "api.orders.show": {
            uri: "api\/v1\/orders\/{id}",
            methods: ["GET", "HEAD"],
            parameters: ["id"],
        },
        "api.orders.my-sales": {
            uri: "api\/v1\/orders\/my-sales",
            methods: ["POST"],
        },
        "api.orders.my-sales.show": {
            uri: "api\/v1\/orders\/my-sales\/{orderNo}",
            methods: ["GET", "HEAD"],
            parameters: ["orderNo"],
        },
        "api.orders.update-status": {
            uri: "api\/v1\/orders\/update-status",
            methods: ["POST"],
        },
        "api.orders.open-dispute": {
            uri: "api\/v1\/orders\/open-dispute",
            methods: ["POST"],
        },
        "api.orders.upload-tickets": {
            uri: "api\/v1\/orders\/upload-tickets",
            methods: ["POST"],
        },
        "api.orders.mark-tickets-downloaded": {
            uri: "api\/v1\/orders\/mark-tickets-downloaded",
            methods: ["POST"],
        },
        "api.payout-methods.index": {
            uri: "api\/v1\/payout-methods",
            methods: ["POST"],
        },
        "api.payout-methods.store": {
            uri: "api\/v1\/payout-methods\/create",
            methods: ["POST"],
        },
        "api.payout-methods.configurations": {
            uri: "api\/v1\/payout-methods\/configurations",
            methods: ["GET", "HEAD"],
        },
        "api.payout-methods.delete": {
            uri: "api\/v1\/payout-methods\/delete\/{payoutMethodId}",
            methods: ["DELETE"],
            parameters: ["payoutMethodId"],
        },
        "api.payout-methods.mark-default": {
            uri: "api\/v1\/payout-methods\/mark-default",
            methods: ["POST"],
        },
        "api.support-requests.index": {
            uri: "api\/v1\/support-requests",
            methods: ["GET", "HEAD"],
        },
        "api.support-requests.store": {
            uri: "api\/v1\/support-requests",
            methods: ["POST"],
        },
        "api.support-requests.enums": {
            uri: "api\/v1\/support-requests\/enums",
            methods: ["GET", "HEAD"],
        },
        "api.support-requests.show": {
            uri: "api\/v1\/support-requests\/{id}",
            methods: ["GET", "HEAD"],
            parameters: ["id"],
        },
        "api.support-requests.reply": {
            uri: "api\/v1\/support-requests\/{id}\/reply",
            methods: ["POST"],
            parameters: ["id"],
        },
        "api.login": { uri: "api\/auth\/login", methods: ["POST"] },
        "checkout.webhook": {
            uri: "api\/checkout\/webhook",
            methods: ["POST"],
        },
        "frontend.maintenance": {
            uri: "under-construction",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        home: { uri: "\/", methods: ["GET", "HEAD"], domain: "ticketgol.test" },
        search: {
            uri: "search",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        events: {
            uri: "events",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        selltickets: {
            uri: "selltickets",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        clubs: {
            uri: "clubs",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        leagues: {
            uri: "leagues",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        stadiums: {
            uri: "stadiums",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        dashboard: {
            uri: "my-account",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "my-account.settings": {
            uri: "my-account\/settings",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "profile.edit": {
            uri: "my-account\/profile",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "profile.update": {
            uri: "my-account\/profile",
            methods: ["PATCH"],
            domain: "ticketgol.test",
        },
        "profile.destroy": {
            uri: "my-account\/profile",
            methods: ["DELETE"],
            domain: "ticketgol.test",
        },
        "my-account.orders": {
            uri: "my-account\/orders",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "my-account.order-detail": {
            uri: "my-account\/orders\/{id}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["id"],
        },
        "my-account.support": {
            uri: "my-account\/support",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "my-account.support.show": {
            uri: "my-account\/support\/{id}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["id"],
        },
        "my-account.tickets": {
            uri: "my-account\/tickets",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "my-account.tickets.edit": {
            uri: "my-account\/tickets\/edit\/{ticketNo}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["ticketNo"],
        },
        "my-account.sales": {
            uri: "my-account\/sales",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "my-account.sales.detail": {
            uri: "my-account\/sales\/{orderNo}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["orderNo"],
        },
        "ticket.sell": {
            uri: "ticket\/sell\/{slug}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["slug"],
        },
        "ticket.checkout": {
            uri: "ticket\/checkout\/{reservationId}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["reservationId"],
        },
        "checkout.success": {
            uri: "checkout\/success",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "checkout.cancel": {
            uri: "checkout\/cancel",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        register: {
            uri: "register",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        login: {
            uri: "login",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "password.request": {
            uri: "forgot-password",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "password.email": {
            uri: "forgot-password",
            methods: ["POST"],
            domain: "ticketgol.test",
        },
        "password.reset": {
            uri: "reset-password\/{token}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["token"],
        },
        "password.store": {
            uri: "reset-password",
            methods: ["POST"],
            domain: "ticketgol.test",
        },
        "verification.notice": {
            uri: "verify-email",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "verification.verify": {
            uri: "verify-email\/{id}\/{hash}",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
            parameters: ["id", "hash"],
        },
        "verification.send": {
            uri: "email\/verification-notification",
            methods: ["POST"],
            domain: "ticketgol.test",
        },
        "password.confirm": {
            uri: "confirm-password",
            methods: ["GET", "HEAD"],
            domain: "ticketgol.test",
        },
        "password.update": {
            uri: "password",
            methods: ["PUT"],
            domain: "ticketgol.test",
        },
        logout: { uri: "logout", methods: ["POST"], domain: "ticketgol.test" },
        "detail.show": {
            uri: "{slug}",
            methods: ["GET", "HEAD"],
            wheres: { slug: ".*" },
            domain: "ticketgol.test",
            parameters: ["slug"],
        },
        "storage.local": {
            uri: "storage\/{path}",
            methods: ["GET", "HEAD"],
            wheres: { path: ".*" },
            parameters: ["path"],
        },
    },
};
if (typeof window !== "undefined" && typeof window.Ziggy !== "undefined") {
    Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
