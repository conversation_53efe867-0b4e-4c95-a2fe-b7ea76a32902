import { useDispatch, useSelector } from "react-redux";
import {
    fetchSupportTicketDetails as fetchDetails,
    sendSupportMessage,
    setNewMessage as setMessage,
    resetNewMessages,
    setCanScrollToBottom,
} from "../redux/slices/SupportRequestDetailSlice";

export default function useSupportRequestDetail(requestTicketId) {
    const dispatch = useDispatch();
    const {
        supportRequest,
        isLoading,
        error,
        newMessage,
        isMessageSending,
        canScrollToBottom,
    } = useSelector((state) => state.supportRequestDetail);

    // Fetch support ticket details
    const fetchSupportTicketDetails = () => {
        if (requestTicketId) {
            dispatch(fetchDetails(requestTicketId));
        }
    };

    const refreshMessage = () => {
        dispatch(setCanScrollToBottom(false));
        dispatch(fetchDetails(requestTicketId));
    };

    // Send a new message
    const sendMessage = (files) => {
        dispatch(
            sendSupportMessage({ requestTicketId, message: newMessage, files }),
        );
    };

    // Set new message
    const setNewMessage = (message) => {
        dispatch(setMessage(message));
    };

    const clearNewMessage = () => {
        dispatch(resetNewMessages());
    };

    return {
        supportRequest,
        supportMessages: supportRequest?.messages || [],
        newMessage,
        setNewMessage,
        clearNewMessage,
        error,
        processing: isLoading,
        fetchSupportTicketDetails,
        sendMessage,
        isMessageSending,
        refreshMessage,
        canScrollToBottom,
    };
}
