import { useSelector, useDispatch } from "react-redux";
import {
    fetchUserOrders,
    resetFilters,
    setFilterAtOnce,
} from "@/redux/slices/OrdersSlice";

export default function useOrders() {
    const dispatch = useDispatch();
    const { orders, isLoading, nextPageUrl, filters } = useSelector(
        (state) => state.orders,
    );

    const fetchOrders = async () => {
        dispatch(
            fetchUserOrders({
                url: route("api.orders.index"),
                filters: filters,
            }),
        );
    };

    const clearFilters = () => {
        dispatch(resetFilters());
    };

    const setFilter = (key, value) => {
        dispatch(
            setFilterAtOnce({
                ...filters,
                [key]: value,
            }),
        );
    };

    const loadMoreOrders = () => {
        if (nextPageUrl) {
            dispatch(
                fetchUserOrders({
                    url: nextPageUrl,
                    filters: filters,
                    canAppendData: true,
                }),
            );
        }
    };

    return {
        orders,
        isLoading,
        nextPageUrl,
        filters,
        fetchOrders,
        clearFilters,
        setFilter,
        loadMoreOrders,
    };
}
