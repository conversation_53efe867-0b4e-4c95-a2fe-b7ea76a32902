import { useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import getValidationRules from "@/helpers/validationRules";
import { validateForm } from "@/helpers/validateForm";

export default function useValidation(schema) {
    const { translate } = useSSRTranslations();
    const rules = getValidationRules(translate);
    const [errors, setErrors] = useState({});

    function resolveSchema() {
        const resolved = {};

        for (const field in schema) {
            resolved[field] = schema[field].map((rule) => {
                if (typeof rule === "function") {
                    return rule;
                }
                if (typeof rule === "string" && rules[rule]) {
                    return rules[rule];
                }
                if (typeof rule === "object" && rule.rule && rules[rule.rule]) {
                    return rules[rule.rule](rule.value);
                }
                console.log(`Unknown validation rule:`, rule);
                return () => null; // fallback to no-op
            });
        }

        return resolved;
    }

    function validate(formData) {
        const resolvedSchema = resolveSchema();
        const validationErrors = validateForm(formData, resolvedSchema);
        setErrors(validationErrors);
        return Object.keys(validationErrors).length === 0;
    }

    function validateField(fieldName, fieldValue, formData) {
        const fieldRules = findMatchingSchemaRule(fieldName);
        if (!fieldRules) return;

        const resolvedFieldRules = fieldRules.map((rule) => {
            if (typeof rule === "function") {
                return rule;
            }
            if (typeof rule === "string" && rules[rule]) {
                return rules[rule];
            }
            if (typeof rule === "object" && rule.rule && rules[rule.rule]) {
                return rules[rule.rule](rule.value);
            }
            console.log(
                `Unknown validation rule for field "${fieldName}":`,
                rule,
            );
            return () => null; // fallback to no-op
        });

        const value = fieldValue ?? getValueByPath(formData, fieldName);

        // Apply rules one by one
        for (const ruleFn of resolvedFieldRules) {
            const error = ruleFn(value, formData, fieldName);
            if (error) {
                setErrors((prev) => ({ ...prev, [fieldName]: error }));
                return error;
            }
        }

        // No error: clear field error
        setErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors[fieldName];
            return newErrors;
        });

        return null;
    }

    function getValueByPath(obj, path) {
        return path
            .split(".")
            .reduce(
                (o, key) =>
                    o && typeof o[key] !== "undefined" ? o[key] : undefined,
                obj,
            );
    }

    function findMatchingSchemaRule(fieldName) {
        for (const key in schema) {
            if (key.includes("*")) {
                const pattern = new RegExp(
                    "^" + key.replace(/\./g, "\\.").replace("*", "\\d+") + "$",
                );
                if (pattern.test(fieldName)) {
                    return schema[key];
                }
            } else if (key === fieldName) {
                return schema[key];
            }
        }
        return null;
    }

    return {
        errors,
        validate,
        validateField,
        setErrors,
    };
}
