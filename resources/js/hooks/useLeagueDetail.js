import { useSelector, useDispatch } from "react-redux";
import {
    fetchLeagueDetail,
    setLeagueFromSSR,
} from "@/redux/slices/LeagueDetailSlice";

export default function useLeagueDetail() {
    const { league, leagueLoading } = useSelector((state) => state.league);
    const dispatch = useDispatch();

    const getLeagueDetail = (slug) => {
        dispatch(
            fetchLeagueDetail({
                url: route("api.leagues.show", { slug: slug }),
            }),
        );
    };

    return {
        league,
        leagueLoading,
        getLeagueDetail,
        setFromSSR: (data) => dispatch(setLeagueFromSSR(data)),
    };
}
