import { useSelector, useDispatch } from "react-redux";
import {
    fetchEventDetail,
    fetchEventTickets,
    resetTicketFilters,
    setTicketFilter,
    resetNextPageUrl,
    setEventFromSSR,
} from "@/redux/slices/EventDetailSlice";

export default function useEventDetail() {
    const {
        event,
        eventLoading,
        tickets,
        ticketsLoading,
        totalTickets,
        filters,
        nextPageUrl,
    } = useSelector((state) => state.event);
    const dispatch = useDispatch();

    const getEventDetail = (slug) => {
        dispatch(
            fetchEventDetail({ url: route("api.events.show", { slug: slug }) }),
        );
    };

    const getEventTickets = () => {
        dispatch(
            fetchEventTickets({
                url: route("api.tickets.index"),
                eventId: event.id,
                filters: filters,
            }),
        );
    };

    const clearNextPageUrl = () => {
        dispatch(resetNextPageUrl());
    };

    const loadMoreEventTickets = () => {
        // Only fetch if we have a nextPageUrl
        if (!nextPageUrl) return Promise.resolve();

        return dispatch(
            fetchEventTickets({
                url: nextPageUrl,
                eventId: event.id,
                filters: filters,
                canAppendTickets: true,
            }),
        ).unwrap(); // Return a promise that resolves when the action is complete
    };

    const updateTicketFilter = (key, value) => {
        dispatch(setTicketFilter({ key, value }));
    };

    const clearTicketFilters = () => {
        dispatch(resetTicketFilters());
    };

    const setFromSSR = (data) => {
        dispatch(setEventFromSSR(data));
    };

    return {
        event,
        eventLoading,
        getEventDetail,
        tickets,
        ticketsLoading,
        getEventTickets,
        totalTickets,
        filters,
        nextPageUrl,
        loadMoreEventTickets,
        updateTicketFilter,
        clearTicketFilters,
        clearNextPageUrl,
        setFromSSR,
    };
}
