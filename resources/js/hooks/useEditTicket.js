import { useCallback } from "react";
import { router } from "@inertiajs/react";
import { useSelector, useDispatch } from "react-redux";
import { fetchTicketDetail, setFormData } from "@/redux/slices/EditTicketSlice";

export default function useEditTicket() {
    const { ticket, ticketLoading, formData } = useSelector(
        (state) => state.editTicket,
    );
    const dispatch = useDispatch();

    const getTicketDetail = (ticketNo) => {
        dispatch(
            fetchTicketDetail({
                url: route("api.tickets.show", { ticketNo }),
            }),
        );
    };

    const updateFormData = (key, value) => {
        dispatch(setFormData({ key, value }));
    };

    return {
        ticket,
        ticketLoading,
        getTicketDetail,
        formData,
        updateFormData,
    };
}
