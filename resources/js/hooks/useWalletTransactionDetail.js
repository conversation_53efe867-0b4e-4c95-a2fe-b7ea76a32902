import { useState, useEffect } from "react";
import axios from "axios";

const useWalletTransactionDetail = () => {
    const [transaction, setTransaction] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    const fetchWalletTransactionDetail = async (transactionNo) => {
        try {
            setIsLoading(true);

            const response = await axios.get(
                route("api.wallet-transactions.show", { transactionNo }),
            );

            if (response.data.success === true) {
                setTransaction(response.data.transaction);
            }
        } catch (err) {
            console.error("Failed to fetch wallet transaction:", err);
        } finally {
            setIsLoading(false);
        }
    };

    return {
        transaction,
        isLoading,
        fetchWalletTransactionDetail,
    };
};

export default useWalletTransactionDetail;
