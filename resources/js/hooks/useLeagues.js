import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchLeagues,
    fetchFilterOptions,
    resetFilters,
    setFilter,
    resetNextPageUrl,
    setInitialData,
} from "@/redux/slices/LeaguesSlice";

export default function useLeagues(initialData = null) {
    const {
        leagues,
        loading,
        filters,
        filterOptions,
        nextPageUrl,
        isFilterOptionsInitialized,
    } = useSelector((state) => state.leagues);
    const dispatch = useDispatch();

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const clearFilters = useCallback(() => {
        dispatch(resetFilters());
    }, []);

    const fetchOptions = () => {
        if (initialData || isFilterOptionsInitialized) {
            return;
        }
        dispatch(fetchFilterOptions());
    };

    const fetchLeaguesInitially = () => {
        if (initialData || isFilterOptionsInitialized > 0) {
            return;
        }
        dispatch(
            fetchLeagues({
                url: route("api.leagues.index"),
                filters: filters,
            }),
        );
    };

    const refreshLeagues = () => {
        dispatch(
            fetchLeagues({
                url: route("api.leagues.index"),
                filters: filters,
            }),
        );
    };

    const clearNextPageUrl = () => {
        dispatch(resetNextPageUrl());
    };

    const loadMoreLeagues = () => {
        if (!nextPageUrl) {
            return;
        }

        return dispatch(
            fetchLeagues({
                url: nextPageUrl,
                filters: filters,
                canAppendLeagues: true,
            }),
        );
    };

    const initializeWithSSRData = (initialData) => {
        dispatch(setInitialData(initialData));
    };

    return {
        leagues,
        loading,
        filterOptions,
        filters,
        updateFilter,
        clearFilters,
        fetchOptions,
        fetchLeaguesInitially,
        refreshLeagues,
        nextPageUrl,
        loadMoreLeagues,
        clearNextPageUrl,
        initializeWithSSRData,
    };
}
