import { useSelector, useDispatch } from "react-redux";
import {
    fetchPayoutMethods,
    fetchPayoutMethodConfigurations,
    setFormData,
    resetFormData,
} from "@/redux/slices/PayoutMethodSlice";

export default function usePayoutMethod() {
    const {
        userPayoutMethods,
        loading,
        configurations,
        configurationsLoading,
        formData,
    } = useSelector((state) => state.payoutMethod);
    const dispatch = useDispatch();

    const getUserPayoutMethods = () => {
        dispatch(
            fetchPayoutMethods({
                url: route("api.payout-methods.index"),
            }),
        );
    };

    const getConfigurationsData = () => {
        dispatch(
            fetchPayoutMethodConfigurations({
                url: route("api.payout-methods.configurations"),
            }),
        );
    };

    const updateFormData = (key, value) => {
        dispatch(setFormData({ key, value }));
    };

    const clearFormData = (key, value) => {
        dispatch(resetFormData());
    };

    return {
        userPayoutMethods,
        loading,
        getUserPayoutMethods,
        formData,
        updateFormData,
        clearFormData,
        configurations,
        configurationsLoading,
        getConfigurationsData,
    };
}
