import { useCallback, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchEventsList,
    resetFilters,
    setFilter,
    resetNextPageUrl,
    setInitialData,
} from "@/redux/slices/SellTicketsSlice";

export default function useSellTickets() {
    const { events, eventLoading, filters, filterChanged, nextPageUrl } =
        useSelector((state) => state.sellTickets);
    const dispatch = useDispatch();

    const setInitialDataFromProps = (initialData) => {
        dispatch(setInitialData(initialData));
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const clearFilters = useCallback(() => {
        dispatch(resetFilters());
    }, []);

    const updateCategories = (value) => {
        dispatch(toggleCategories({ value }));
    };

    const getEventsList = () => {
        dispatch(
            fetchEventsList({
                url: route("api.events.selltickets"),
                filters: filters,
            }),
        );
    };

    const clearNextPageUrl = () => {
        dispatch(resetNextPageUrl());
    };

    const loadMoreEvents = () => {
        if (!nextPageUrl) {
            return;
        }

        return dispatch(
            fetchEventsList({
                url: nextPageUrl,
                filters: filters,
                canAppendEvents: true,
            }),
        );
    };

    return {
        events,
        eventLoading,
        filters,
        filterChanged,
        updateFilter,
        clearFilters,
        getEventsList,
        nextPageUrl,
        loadMoreEvents,
        clearNextPageUrl,
        setInitialDataFromProps,
    };
}
