import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchStadiums,
    fetchFilterOptions,
    resetFilters,
    setFilter,
    resetNextPageUrl,
    setInitialData,
} from "@/redux/slices/StadiumsSlice";

export default function useStadiums() {
    const {
        stadiums,
        loading,
        filters,
        filterOptions,
        nextPageUrl,
        isFilterOptionsInitialized,
    } = useSelector((state) => state.stadiums);
    const dispatch = useDispatch();

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const clearFilters = useCallback(() => {
        dispatch(resetFilters());
    }, []);

    const fetchOptions = () => {
        if (isFilterOptionsInitialized) {
            return;
        }
        dispatch(fetchFilterOptions());
    };

    const fetchStadiumsInitially = () => {
        if (isFilterOptionsInitialized > 0) {
            return;
        }
        dispatch(
            fetchStadiums({
                url: route("api.stadiums.index"),
                filters: filters,
            }),
        );
    };

    const refreshStadiums = () => {
        dispatch(
            fetchStadiums({
                url: route("api.stadiums.index"),
                filters: filters,
            }),
        );
    };

    const clearNextPageUrl = () => {
        dispatch(resetNextPageUrl());
    };

    const loadMoreStadiums = () => {
        if (!nextPageUrl) {
            return;
        }

        return dispatch(
            fetchStadiums({
                url: nextPageUrl,
                filters: filters,
                canAppendStadiums: true,
            }),
        );
    };

    const initializeWithSSRData = (initialData) => {
        dispatch(setInitialData(initialData));
    };

    return {
        stadiums,
        loading,
        filterOptions,
        filters,
        updateFilter,
        clearFilters,
        fetchOptions,
        fetchStadiumsInitially,
        refreshStadiums,
        nextPageUrl,
        loadMoreStadiums,
        clearNextPageUrl,
        initializeWithSSRData,
    };
}
