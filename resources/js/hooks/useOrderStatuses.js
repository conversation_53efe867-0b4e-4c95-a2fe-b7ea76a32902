import { useState, useEffect } from "react";
import axios from "axios";

const useOrderStatuses = () => {
    const [statuses, setStatuses] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchStatuses = async () => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await axios.get(route("api.orders.statuses"));

            if (response.data.success === true) {
                setStatuses(response.data.statuses);
            }
        } catch (err) {
            setError(err.message);
            console.error("Failed to fetch order statuses:", err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchStatuses();
    }, []);

    return {
        statuses,
        isLoading,
        error,
        refetch: fetchStatuses,
    };
};

export default useOrderStatuses;
