import { usePage } from "@inertiajs/react";
import useTranslations from "./useTranslations";

/**
 * Custom hook that uses server-side translations for SSR and falls back to client-side translations
 */
function useSSRTranslations() {
    const { props } = usePage();
    const { translate: clientTranslate, isLoading } = useTranslations();

    // Get server-side translations from Inertia props
    const serverTranslations = props.translations?.translations || {};
    const serverLocale = props.translations?.locale || "en";

    const translate = (key, fallback = "") => {
        // For SSR or when server translations are available, use them first
        if (
            typeof window === "undefined" ||
            Object.keys(serverTranslations).length > 0
        ) {
            const keys = key.split(".");
            let value = serverTranslations;

            for (const k of keys) {
                if (value && typeof value === "object" && k in value) {
                    value = value[k];
                } else {
                    value = fallback || key;
                    break;
                }
            }

            return typeof value === "string" ? value : fallback || key;
        }

        // Fallback to client-side translations
        return clientTranslate(key, fallback);
    };

    return {
        translate,
        isLoading: typeof window === "undefined" ? false : isLoading,
        locale: serverLocale,
    };
}

export default useSSRTranslations;
