import { useSelector, useDispatch } from "react-redux";
import {
    fetchSupportRequests,
    fetchSupportEnums,
    setFilter,
    resetFilters,
    resetSupportData,
} from "@/redux/slices/SupportRequestSlice";
import { useState } from "react";

export default function useSupportRequests() {
    const dispatch = useDispatch();
    const {
        supportRequests,
        isLoading,
        options,
        filters,
        nextPageUrl,
        setMultipleFilters,
    } = useSelector((state) => state.supportRequests);

    const [isOptionsFetched, setIsOptionsFetched] = useState(false);

    const fetchSupportTickets = (isWithoutFilters = false) => {
        dispatch(
            fetchSupportRequests({
                url: route("api.support-requests.index"),
                filters: isWithoutFilters ? {} : filters,
            }),
        );
    };

    const fetchOptions = () => {
        if (isOptionsFetched) {
            return;
        }
        dispatch(fetchSupportEnums());
        setIsOptionsFetched(true);
    };

    const loadMoreSupportRequests = () => {
        if (!nextPageUrl) {
            return;
        }

        dispatch(
            fetchSupportRequests({
                url: nextPageUrl,
                filters: filters,
                canAppendData: true,
            }),
        );
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const updateMultipleFilters = (filters) => {
        dispatch(setMultipleFilters(filters));
    };

    const clearFilters = () => {
        dispatch(resetFilters());
    };

    const resetPage = () => {
        dispatch(resetSupportData());
    };

    return {
        supportRequests,
        isLoading,
        options,
        filters,
        nextPageUrl,
        updateMultipleFilters,
        fetchSupportTickets,
        fetchOptions,
        loadMoreSupportRequests,
        updateFilter,
        clearFilters,
        resetPage,
    };
}
