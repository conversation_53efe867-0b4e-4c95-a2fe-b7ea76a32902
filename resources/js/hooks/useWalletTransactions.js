import { useSelector, useDispatch } from "react-redux";
import {
    fetchWalletTransactions,
    resetFilters,
    setFilter,
} from "@/redux/slices/WalletTransactionsSlice";

export default function useWalletTransactions() {
    const dispatch = useDispatch();
    const { transactions, isLoading, filterChanged, nextPageUrl, filters } =
        useSelector((state) => state.walletTransactions);

    const getWalletTransactions = async () => {
        dispatch(
            fetchWalletTransactions({
                url: route("api.wallet-transactions.index"),
                filters: filters,
            }),
        );
    };

    const clearFilters = () => {
        dispatch(resetFilters());
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const loadMoreWalletTransactions = () => {
        if (nextPageUrl) {
            dispatch(
                fetchWalletTransactions({
                    url: nextPageUrl,
                    filters: filters,
                    canAppendData: true,
                }),
            );
        }
    };

    return {
        transactions,
        isLoading,
        filterChanged,
        nextPageUrl,
        filters,
        getWalletTransactions,
        clearFilters,
        updateFilter,
        loadMoreWalletTransactions,
    };
}
