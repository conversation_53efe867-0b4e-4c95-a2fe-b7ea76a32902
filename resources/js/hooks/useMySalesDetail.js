import { useState, useEffect } from "react";
import axios from "axios";

const useMySalesDetail = () => {
    const [order, setOrder] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    const fetchMySalesOrderDetail = async (orderNo) => {
        try {
            setIsLoading(true);

            const response = await axios.get(
                route("api.orders.my-sales.show", { orderNo }),
            );

            if (response.data.success === true) {
                setOrder(response.data.order);
            }
        } catch (err) {
            console.error("Failed to fetch sales order:", err);
        } finally {
            setIsLoading(false);
        }
    };

    return {
        order,
        isLoading,
        fetchMySalesOrderDetail,
    };
};

export default useMySalesDetail;
