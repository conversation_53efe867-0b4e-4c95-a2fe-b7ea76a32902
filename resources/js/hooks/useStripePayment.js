import { useState } from "react";
import axios from "axios";
import { useStripe, useElements, CardElement } from "@stripe/react-stripe-js";
import toast from "react-hot-toast";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useTicketCheckout from "@/hooks/useTicketCheckout";

export default function useStripePayment({ reservationId }) {
    const { translate } = useSSRTranslations();
    const { reservation, attendees, paymentState, updatePaymentState } =
        useTicketCheckout();
    const stripe = useStripe();
    const elements = useElements();

    const [loading, setLoading] = useState(false);
    const [cardCompleted, setCardCompleted] = useState(false);

    const createOrderIfNeeded = async () => {
        if (paymentState.orderId && paymentState.clientSecret) {
            return paymentState;
        }

        const params = {
            ticket_reservation_id: reservationId,
            event_id: reservation.ticket.event_id,
            currency_code: reservation.ticket.currency_code,
            attendees: attendees,
        };

        const { data } = await axios.post(route("api.orders.store"), params);

        if (!data.success) {
            throw new Error("Order creation failed");
        }

        updatePaymentState({
            orderId: data.order_id,
            clientSecret: data.clientSecret,
            encryptedOrderId: data.encryptedOrderId,
        });

        return {
            orderId: data.order_id,
            clientSecret: data.clientSecret,
            encryptedOrderId: data.encryptedOrderId,
        };
    };

    const confirmPayment = async () => {
        if (!stripe || !elements) {
            toast.error(translate("ticket.payment_element_error"));
            return;
        }

        if (!cardCompleted) {
            toast.error(
                translate(
                    "ticket.card_detail_error",
                    "Please enter your card details.",
                ),
            );
            return;
        }

        const cardElement = elements.getElement(CardElement);

        setLoading(true);

        try {
            const { orderId, clientSecret, encryptedOrderId } =
                await createOrderIfNeeded();

            const response = await axios.post(
                route("api.orders.check-status"),
                { order_id: encryptedOrderId },
            );

            if (response.data.is_completed) {
                toast.error(
                    translate(
                        "ticket.reservation_completed",
                        "This ticket reservation has already been paid in another tab.",
                    ),
                );
                setLoading(false);
                return { success: false };
            }

            const result = await stripe.confirmCardPayment(clientSecret, {
                payment_method: { card: cardElement },
            });

            if (result.error) {
                toast.error(result.error.message);
                return { success: false, error: result.error };
            }

            if (result.paymentIntent.status === "succeeded") {
                return {
                    success: true,
                    encryptedOrderId,
                    orderId,
                };
            }

            return { success: false, status: result.paymentIntent.status };
        } catch (error) {
            if (error.response?.status === 422 && error.response.data?.errors) {
                toast.error(Object.values(error.response.data?.errors)[0]?.[0]);
            } else {
                toast.error(translate("common.something_wrong"));
            }
            return { success: false, error };
        } finally {
            setLoading(false);
        }
    };

    return {
        loading,
        CardElement,
        confirmPayment,
        cardCompleted,
        setCardCompleted,
    };
}
