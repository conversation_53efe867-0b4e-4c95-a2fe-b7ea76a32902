import { router } from "@inertiajs/react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchReservation,
    setAttendees,
    setIsAgreed,
    setShowTimesUpPopup,
    setStep,
    setPaymentState,
    resetReservation,
} from "@/redux/slices/TicketCheckoutSlice";

export default function useTicketCheckout() {
    const {
        reservation,
        reservationLoading,
        attendees,
        showTimesUpPopup,
        step,
        paymentState,
        isAgreed,
    } = useSelector((state) => state.ticketCheckout);
    const dispatch = useDispatch();

    const getReservation = (reservationId) => {
        dispatch(
            fetchReservation({
                url: route("api.reservations.detail", { reservationId }),
            }),
        );
    };

    const clearReservation = (value) => {
        dispatch(resetReservation());
    };

    const updateAttendees = (index, field, value) => {
        dispatch(setAttendees({ index, field, value }));
    };

    const updateIsAgreed = (key, value) => {
        dispatch(setIsAgreed({ key, value }));
    };

    const handleTimesUpPopup = (value) => {
        dispatch(setShowTimesUpPopup({ value }));
    };

    const nextStep = () => {
        dispatch(setStep({ value: Math.min(step + 1, 4) }));
    };

    const updatePaymentState = (values) => {
        dispatch(setPaymentState(values));
    };

    const prevStep = () => {
        dispatch(setStep({ value: Math.max(step - 1, 1) }));
    };

    const handleTimesUpPopupCloseClick = () => {
        router.visit(route("detail.show", reservation.ticket.event.slug));
    };

    return {
        reservation,
        reservationLoading,
        getReservation,
        clearReservation,
        attendees,
        isAgreed,
        showTimesUpPopup,
        updateAttendees,
        updateIsAgreed,
        handleTimesUpPopup,
        handleTimesUpPopupCloseClick,
        step,
        paymentState,
        updatePaymentState,
        nextStep,
        prevStep,
    };
}
