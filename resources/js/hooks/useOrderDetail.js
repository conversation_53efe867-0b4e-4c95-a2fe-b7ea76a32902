import { useState, useEffect } from "react";
import axios from "axios";
import toast from "react-hot-toast";

export default function useOrderDetail(orderId) {
    const [order, setOrder] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchOrderDetail = async () => {
        setIsLoading(true);
        setError(null);

        try {
            const response = await axios.get(route("api.orders.show", orderId));

            if (response.data.success === true) {
                setOrder(response.data.order);
            } else {
                toast.error("Failed to fetch order details");
                setError("Failed to fetch order details");
            }
        } catch (error) {
            toast.error("Failed to fetch order details");
            setError(
                error.response?.data?.message ||
                    "An error occurred while fetching order details",
            );
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (orderId) {
            fetchOrderDetail();
        }
    }, [orderId]);

    return {
        order,
        isLoading,
        error,
        fetchOrderDetail,
    };
}
