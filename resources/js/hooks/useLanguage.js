import { useState, useEffect } from "react";
import { usePage } from "@inertiajs/react";

export default function useLanguage(defaultLocale = "en") {
    const [locale, setLocale] = useState(defaultLocale);
    const { pageSlugs } = usePage().props;

    const getCookie = (name) => {
        // Check if document is available (not during SSR)
        if (typeof document === "undefined") {
            return defaultLocale;
        }

        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);

        if (parts.length === 2) return parts.pop().split(";").shift();

        return defaultLocale;
    };

    const handleLanguageChange = (newLocale) => {
        setLocale(newLocale);

        if (typeof document !== "undefined") {
            document.cookie = `selected_locale=${newLocale}; path=/; max-age=31536000`;
            const newSlug = pageSlugs?.[newLocale];

            if (
                newSlug &&
                newSlug !== pageSlugs[locale] &&
                route().current("detail.show")
            ) {
                window.location.href = route("detail.show", newSlug);
            } else {
                window.location.reload();
            }
        }
    };

    useEffect(() => {
        const cookieLocale = getCookie("selected_locale");
        setLocale(cookieLocale);
    }, []);

    return {
        locale,
        handleLanguageChange,
    };
}
