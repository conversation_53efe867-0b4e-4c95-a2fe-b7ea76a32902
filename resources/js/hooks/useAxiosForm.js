import { useState } from "react";
import axios from "axios";

export default function useAxiosForm(initialData = {}) {
    const [data, setData] = useState(initialData);

    const [errors, setErrors] = useState({});
    const [processing, setProcessing] = useState(false);
    const [progress, setProgress] = useState(null);

    const reset = () => {
        setData(initialData);
        setErrors({});
    };

    const setFormData = (key, value) => {
        if (typeof key === "string") {
            setData((data) => {
                return {
                    ...data,
                    [key]: value,
                };
            });
        } else if (typeof key === "function") {
            setData(key);
        } else if (typeof key === "object") {
            setData((data) => {
                return {
                    ...data,
                    ...key,
                };
            });
        }
    };

    const clearErrors = (field) => {
        if (field) {
            setErrors({
                ...errors,
                [field]: null,
            });
        } else {
            setErrors({});
        }
    };

    const get = (url, options = {}) => {
        submit("get", url, options);
    };

    const post = (url, options = {}) => {
        submit("post", url, options);
    };

    const put = (url, options = {}) => {
        submit("put", url, options);
    };

    const patch = (url, options = {}) => {
        submit("patch", url, options);
    };

    const del = (url, options = {}) => {
        submit("delete", url, options);
    };

    const submit = async (method, url, options = {}) => {
        const {
            onSuccess,
            onError,
            onFinish,
            headers = {},
            resetOnSuccess = true,
            ...axiosOptions
        } = options;

        setProcessing(true);
        clearErrors();

        try {
            const requestConfig = {
                method,
                url,
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                    "X-Requested-With": "XMLHttpRequest",
                    ...headers,
                },
                onUploadProgress: (progressEvent) => {
                    const percentCompleted = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total,
                    );
                    setProgress(percentCompleted);
                },
                ...axiosOptions,
            };

            // Add data based on method
            if (["post", "put", "patch"].includes(method.toLowerCase())) {
                requestConfig.data = data;
            } else if (
                method.toLowerCase() === "get" ||
                method.toLowerCase() === "delete"
            ) {
                requestConfig.params = data;
            }

            const response = await axios(requestConfig);

            // Handle successful response
            if (resetOnSuccess) {
                reset();
            }

            if (onSuccess) {
                onSuccess(response.data);
            }

            return response.data;
        } catch (error) {
            const responseData = error.response?.data;

            // Handle validation errors
            if (error.response?.status === 422 && responseData?.errors) {
                setErrors(responseData.errors);
            } else if (responseData?.message) {
                // Set a generic error message
                setErrors({
                    message: responseData.message,
                });
            } else {
                // Fallback error handling
                setErrors({
                    message:
                        error.message ||
                        "An error occurred while processing your request",
                });
            }

            if (onError) {
                onError(
                    responseData?.errors || {
                        message: responseData?.message || error.message,
                    },
                );
            }

            return null;
        } finally {
            setProcessing(false);
            setProgress(null);

            if (onFinish) {
                onFinish();
            }
        }
    };

    return {
        data,
        setFormData,
        errors,
        processing,
        progress,
        reset,
        clearErrors,
        get,
        post,
        put,
        patch,
        delete: del,
        submit,
    };
}
