import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { fetchTranslations } from "@/redux/slices/TranslationSlice";

const loadingLocales = new Set();

export default function useTranslations() {
    // Check if we're in a browser environment before accessing document
    // Default to 'en' during SSR
    const locale =
        typeof document !== "undefined" ? document.documentElement.lang : "en";

    const dispatch = useDispatch();

    const translations = useSelector(
        (state) => state.translations.data[locale],
    );

    useEffect(() => {
        if (!translations || Object.keys(translations).length === 0) {
            if (!loadingLocales.has(locale)) {
                loadingLocales.add(locale);

                dispatch(
                    fetchTranslations({
                        locale,
                        url: route("api.translations.index"),
                    }),
                ).finally(() => {
                    loadingLocales.delete(locale);
                });
            }
        }
    }, [locale]);

    const translate = (key, fallback = "") => {
        if (!translations) return fallback;

        const parts = key.split(".");
        let result = translations;

        for (const part of parts) {
            if (typeof result === "undefined" || result === null) {
                return fallback;
            }
            result = result[part];
        }

        return typeof result !== "undefined" && result !== null
            ? result
            : fallback;
    };

    return { translate };
}
