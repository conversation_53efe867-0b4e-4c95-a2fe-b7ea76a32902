import { useSelector, useDispatch } from "react-redux";
import {
    fetchMySalesOrders,
    resetFilters,
    setFilter,
} from "@/redux/slices/MySalesSlice";

export default function useMySales() {
    const dispatch = useDispatch();
    const { orders, isLoading, filterChanged, nextPageUrl, filters } =
        useSelector((state) => state.mySales);

    const getMySalesOrders = async () => {
        dispatch(
            fetchMySalesOrders({
                url: route("api.orders.my-sales"),
                filters: filters,
            }),
        );
    };

    const clearFilters = () => {
        dispatch(resetFilters());
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const loadMoreSalesOrders = () => {
        if (nextPageUrl) {
            dispatch(
                fetchMySalesOrders({
                    url: nextPageUrl,
                    filters: filters,
                    canAppendData: true,
                }),
            );
        }
    };

    return {
        orders,
        isLoading,
        filterChanged,
        nextPageUrl,
        filters,
        getMySalesOrders,
        clearFilters,
        updateFilter,
        loadMoreSalesOrders,
    };
}
