import { useSelector, useDispatch } from "react-redux";
import {
    fetchMyTickets,
    resetFilters,
    setFilter,
} from "@/redux/slices/MyTicketsSlice";

export default function useMyTickets() {
    const dispatch = useDispatch();
    const { tickets, isLoading, filterChanged, nextPageUrl, filters } =
        useSelector((state) => state.myTickets);

    const getMyTickets = async () => {
        dispatch(
            fetchMyTickets({
                url: route("api.tickets.my-tickets"),
                filters: filters,
            }),
        );
    };

    const clearFilters = () => {
        dispatch(resetFilters());
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const loadMoreTickets = () => {
        if (nextPageUrl) {
            dispatch(
                fetchMyTickets({
                    url: nextPageUrl,
                    filters: filters,
                    canAppendData: true,
                }),
            );
        }
    };

    return {
        tickets,
        isLoading,
        filterChanged,
        nextPageUrl,
        filters,
        getMyTickets,
        clearFilters,
        updateFilter,
        loadMoreTickets,
    };
}
