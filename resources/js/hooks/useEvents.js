import { useCallback, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchEvents,
    fetchFilterOptions,
    resetFilters,
    setFilter,
    toggleCategories,
    resetNextPageUrl,
    setIsFilterCleared,
    setInitialData,
} from "@/redux/slices/EventsSlice";

export default function useEvents(initialData = null) {
    const {
        events,
        eventLoading,
        filters,
        filterOptions,
        nextPageUrl,
        isFilterOptionsInitialized,
        isFilterCleared,
    } = useSelector((state) => state.events);
    const dispatch = useDispatch();

    const setInitialDataFromProps = () => {
        if (initialData) {
            dispatch(setInitialData(initialData));
        }
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const updatesFilterCleared = (value) => {
        dispatch(setIsFilterCleared(value));
    };

    const updateMultipleFilters = (filtersObject) => {
        Object.entries(filtersObject).forEach(([key, value]) => {
            if (key === "categories") {
                value.forEach((category) => {
                    dispatch(toggleCategories({ value: category }));
                });
                return;
            }

            dispatch(setFilter({ key, value }));
        });
    };

    const clearFilters = useCallback(() => {
        dispatch(resetFilters());
    }, []);

    const updateCategories = (value) => {
        dispatch(toggleCategories({ value }));
    };

    const fetchOptions = () => {
        if (isFilterOptionsInitialized || initialData) {
            return;
        }
        dispatch(fetchFilterOptions());
    };

    const fetchEventsInitially = () => {
        if (initialData) {
            return; // Skip API call if initial data is provided
        }
        dispatch(
            fetchEvents({
                url: route("api.events.index"),
                filters: filters,
            }),
        );
    };

    const refreshEvents = () => {
        dispatch(
            fetchEvents({
                url: route("api.events.index"),
                filters: filters,
                updateMultipleFilters,
            }),
        );
    };

    const clearNextPageUrl = () => {
        dispatch(resetNextPageUrl());
    };

    const loadMoreEvents = () => {
        if (!nextPageUrl) {
            return;
        }

        return dispatch(
            fetchEvents({
                url: nextPageUrl,
                filters: filters,
                canAppendEvents: true,
            }),
        );
    };

    return {
        events,
        eventLoading,
        filterOptions,
        filters,
        updateFilter,
        updateMultipleFilters,
        clearFilters,
        updateCategories,
        fetchOptions,
        fetchEventsInitially,
        refreshEvents,
        nextPageUrl,
        loadMoreEvents,
        clearNextPageUrl,
        isFilterCleared,
        updatesFilterCleared,
        setInitialDataFromProps,
    };
}
