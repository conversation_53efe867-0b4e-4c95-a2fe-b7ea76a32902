import { useSelector, useDispatch } from "react-redux";
import {
    fetchWithdrawals,
    fetchWithdrawalConfigurations,
    setFilter,
    resetFilters,
    setFormData,
    resetFormData,
} from "@/redux/slices/WithdrawalsSlice";

export default function useWithdrawals() {
    const {
        withdrawals,
        isLoading,
        filterChanged,
        nextPageUrl,
        filters,
        configurations,
        configurationsLoading,
        formData,
    } = useSelector((state) => state.withdrawals);
    const dispatch = useDispatch();

    const getWithdrawals = () => {
        dispatch(
            fetchWithdrawals({
                url: route("api.withdrawals.index"),
                filters: filters,
            }),
        );
    };

    const getConfigurationsData = () => {
        dispatch(
            fetchWithdrawalConfigurations({
                url: route("api.withdrawals.configurations"),
            }),
        );
    };

    const loadMoreWithdrawals = () => {
        if (nextPageUrl) {
            dispatch(
                fetchWithdrawals({
                    url: nextPageUrl,
                    filters: filters,
                    canAppendData: true,
                }),
            );
        }
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const clearFilters = () => {
        dispatch(resetFilters());
    };

    const updateFormData = (key, value) => {
        dispatch(setFormData({ key, value }));
    };

    const clearFormData = (key, value) => {
        dispatch(resetFormData());
    };

    return {
        withdrawals,
        isLoading,
        getWithdrawals,
        filterChanged,
        nextPageUrl,
        filters,
        clearFilters,
        updateFilter,
        loadMoreWithdrawals,
        formData,
        updateFormData,
        clearFormData,
        configurations,
        configurationsLoading,
        getConfigurationsData,
    };
}
