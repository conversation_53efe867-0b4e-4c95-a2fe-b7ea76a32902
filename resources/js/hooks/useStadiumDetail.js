import { useSelector, useDispatch } from "react-redux";
import {
    fetchStadiumDetail,
    setStadiumFromSSR,
} from "@/redux/slices/StadiumDetailSlice";

export default function useStadiumDetail() {
    const { stadium, stadiumLoading } = useSelector((state) => state.stadium);
    const dispatch = useDispatch();

    const getStadiumDetail = (slug) => {
        dispatch(
            fetchStadiumDetail({
                url: route("api.stadiums.show", { slug: slug }),
            }),
        );
    };

    return {
        stadium,
        stadiumLoading,
        getStadiumDetail,
        setFromSSR: (data) => dispatch(setStadiumFromSSR(data)),
    };
}
