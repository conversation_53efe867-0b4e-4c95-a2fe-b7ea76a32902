import { useSelector, useDispatch } from "react-redux";
import {
    fetchEventDetail,
    fetchTicketConfigurations,
    setStep,
    setFormData,
} from "@/redux/slices/TicketSellSlice";

export default function useTicketSell() {
    const { event, eventLoading, configurations, formData, step } = useSelector(
        (state) => state.ticketSell,
    );
    const dispatch = useDispatch();

    const getEventDetail = (slug) => {
        dispatch(
            fetchEventDetail({
                url: route("api.events.show", { slug }),
            }),
        );
    };

    const getConfigurationsData = () => {
        dispatch(
            fetchTicketConfigurations({
                url: route("api.tickets.configurations"),
            }),
        );
    };

    const updateFormData = (key, value) => {
        dispatch(setFormData({ key, value }));
    };

    const nextStep = () => {
        dispatch(setStep({ value: Math.min(step + 1, 4) }));
    };

    const prevStep = () => {
        dispatch(setStep({ value: Math.max(step - 1, 1) }));
    };

    return {
        event,
        eventLoading,
        getEventDetail,
        formData,
        updateFormData,
        step,
        nextStep,
        prevStep,
        configurations,
        getConfigurationsData,
    };
}
