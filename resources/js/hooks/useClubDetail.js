import { useSelector, useDispatch } from "react-redux";
import {
    fetchClubDetail,
    setClubFromSSR,
} from "@/redux/slices/ClubDetailSlice";

export default function useClubDetail() {
    const { club, clubLoading } = useSelector((state) => state.club);
    const dispatch = useDispatch();

    const getClubDetail = (slug) => {
        dispatch(
            fetchClubDetail({ url: route("api.clubs.show", { slug: slug }) }),
        );
    };

    return {
        club,
        clubLoading,
        getClubDetail,
        setFromSSR: (data) => dispatch(setClubFromSSR(data)),
    };
}
