import { useSelector, useDispatch } from "react-redux";
import {
    fetchDashboardStats,
    setFilter,
} from "@/redux/slices/MyDashboardStatsSlice";

export default function useMyDashboardStats() {
    const dispatch = useDispatch();
    const { stats, isLoading, dateRangeFilters, filters } = useSelector(
        (state) => state.myDashboardStats,
    );

    const getMyDashboardStats = async () => {
        dispatch(
            fetchDashboardStats({
                url: route("api.dashboard.my-stats"),
                filters: filters,
            }),
        );
    };

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    return {
        stats,
        isLoading,
        dateRangeFilters,
        filters,
        getMyDashboardStats,
        updateFilter,
    };
}
