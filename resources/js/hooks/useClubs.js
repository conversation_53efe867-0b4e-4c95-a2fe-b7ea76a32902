import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    fetchClubs,
    fetchFilterOptions,
    resetFilters,
    setFilter,
    resetNextPageUrl,
    setInitialData,
} from "@/redux/slices/ClubsSlice";

export default function useClubs(initialData = null) {
    const {
        clubs,
        loading,
        filters,
        filterOptions,
        nextPageUrl,
        isFilterOptionsInitialized,
    } = useSelector((state) => state.clubs);
    const dispatch = useDispatch();

    const updateFilter = (key, value) => {
        dispatch(setFilter({ key, value }));
    };

    const clearFilters = useCallback(() => {
        dispatch(resetFilters());
    }, []);

    const fetchOptions = () => {
        if (isFilterOptionsInitialized || initialData) {
            return;
        }
        dispatch(fetchFilterOptions());
    };

    const fetchClubsInitially = () => {
        if (initialData) {
            return; // Skip API call if initial data is provided
        }
        dispatch(
            fetchClubs({
                url: route("api.clubs.index"),
                filters: filters,
            }),
        );
    };

    const refreshClubs = () => {
        dispatch(
            fetchClubs({
                url: route("api.clubs.index"),
                filters: filters,
            }),
        );
    };

    const clearNextPageUrl = () => {
        dispatch(resetNextPageUrl());
    };

    const loadMoreClubs = () => {
        if (!nextPageUrl) {
            return;
        }

        return dispatch(
            fetchClubs({
                url: nextPageUrl,
                filters: filters,
                canAppendClubs: true,
            }),
        );
    };

    const initializeWithSSRData = (initialData) => {
        dispatch(setInitialData(initialData));
    };

    return {
        clubs,
        loading,
        filterOptions,
        filters,
        updateFilter,
        clearFilters,
        fetchOptions,
        fetchClubsInitially,
        refreshClubs,
        nextPageUrl,
        loadMoreClubs,
        clearNextPageUrl,
        initializeWithSSRData,
    };
}
