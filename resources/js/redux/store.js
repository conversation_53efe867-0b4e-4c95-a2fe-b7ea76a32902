import { configureStore } from "@reduxjs/toolkit";
import eventsReducer from "./slices/EventsSlice";
import eventDetailReducer from "./slices/EventDetailSlice";
import stadiumsReducer from "./slices/StadiumsSlice";
import stadiumDetailReducer from "./slices/StadiumDetailSlice";
import clubsReducer from "./slices/ClubsSlice";
import clubDetailReducer from "./slices/ClubDetailSlice";
import leaguesReducer from "./slices/LeaguesSlice";
import leagueDetailReducer from "./slices/LeagueDetailSlice";
import ticketCheckoutReducer from "./slices/TicketCheckoutSlice";
import sellTicketsReducer from "./slices/SellTicketsSlice";
import ticketSellReducer from "./slices/TicketSellSlice";
import editTicketReducer from "./slices/EditTicketSlice";
import translationReducer from "./slices/TranslationSlice";
import ordersReducer from "./slices/OrdersSlice";
import myTicketsReducer from "./slices/MyTicketsSlice";
import supportRequestReducer from "./slices/SupportRequestSlice";
import supportRequestDetailReducer from "./slices/SupportRequestDetailSlice";
import mySalesReducer from "./slices/MySalesSlice";
import payoutMethodReducer from "./slices/PayoutMethodSlice";
import walletTransactionsReducer from "./slices/WalletTransactionsSlice";
import withdrawalsReducer from "./slices/WithdrawalsSlice";

export const store = configureStore({
    reducer: {
        events: eventsReducer,
        event: eventDetailReducer,
        stadiums: stadiumsReducer,
        stadium: stadiumDetailReducer,
        clubs: clubsReducer,
        club: clubDetailReducer,
        leagues: leaguesReducer,
        league: leagueDetailReducer,
        ticketCheckout: ticketCheckoutReducer,
        sellTickets: sellTicketsReducer,
        ticketSell: ticketSellReducer,
        editTicket: editTicketReducer,
        translations: translationReducer,
        orders: ordersReducer,
        myTickets: myTicketsReducer,
        supportRequests: supportRequestReducer,
        supportRequestDetail: supportRequestDetailReducer,
        mySales: mySalesReducer,
        payoutMethod: payoutMethodReducer,
        walletTransactions: walletTransactionsReducer,
        withdrawals: withdrawalsReducer,
    },
});
