import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchEventDetail = createAsyncThunk(
    "events/fetchEventDetail",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data.event;
        } else {
            throw new Error("Failed to fetch event detail");
        }
    },
);

export const fetchEventTickets = createAsyncThunk(
    "events/fetchEventTickets",
    async ({ url, eventId, filters, canAppendTickets = false }) => {
        const response = await axios.post(url, {
            ...filters,
            eventId: eventId,
        });

        if (response.data.success === true) {
            return { canAppendTickets, ...response.data };
        } else {
            throw new Error("Failed to fetch tickets");
        }
    },
);

const initialState = {
    event: null,
    eventLoading: true,
    tickets: [],
    nextPageUrl: null,
    ticketsLoading: false,
    totalTickets: 0,
    originalPriceRange: [],
    filters: {
        priceRange: [],
        sector: "",
        quantity: "",
        ticketType: "",
        sort: "remain_qty_asc",
    },
};

const eventDetailSlice = createSlice({
    name: "event",
    initialState,
    reducers: {
        setTicketFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
        },

        resetTicketFilters: (state) => {
            state.filters = {
                ...initialState.filters,
                priceRange: state.originalPriceRange,
            };
        },

        resetNextPageUrl: (state) => {
            state.nextPageUrl = null;
        },
        setEventFromSSR: (state, action) => {
            const { event, tickets } = action.payload;

            // Update event state
            state.event = event;
            state.eventLoading = false;

            // Update tickets state
            state.tickets = tickets.tickets;
            state.nextPageUrl = tickets.meta.next_page_url;
            state.totalTickets = tickets.meta.total;
            state.ticketsLoading = false;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchEventDetail.pending, (state) => {
                state.eventLoading = true;
            })
            .addCase(fetchEventDetail.fulfilled, (state, action) => {
                state.event = action.payload;
                state.eventLoading = false;
                const minPrice =
                    state.event.min_price === state.event.max_price
                        ? 0
                        : state.event.min_price;
                const maxPrice = state.event.max_price;
                // Ensure min is always less than max
                const range = [
                    Math.min(minPrice, maxPrice),
                    Math.max(minPrice, maxPrice),
                ];
                state.filters.priceRange = range;
                state.originalPriceRange = range;
            })
            .addCase(fetchEventDetail.rejected, (state) => {
                state.event = null;
                state.eventLoading = false;
            });

        builder
            .addCase(fetchEventTickets.pending, (state) => {
                state.ticketsLoading = true;
            })
            .addCase(fetchEventTickets.fulfilled, (state, action) => {
                const { tickets, meta } = action.payload;

                if (action.payload.canAppendTickets) {
                    // Create a map of existing tickets by ID for quick lookup
                    const existingTicketsMap = new Map(
                        state.tickets.map((ticket) => [ticket.id, ticket]),
                    );

                    // Add only new tickets that don't already exist
                    const newTickets = tickets.filter(
                        (ticket) => !existingTicketsMap.has(ticket.id),
                    );

                    // Append only unique tickets
                    state.tickets = [...state.tickets, ...newTickets];
                } else {
                    state.tickets = tickets;
                }

                state.nextPageUrl = meta.next_page_url;
                state.totalTickets = meta.total;
                state.ticketsLoading = false;
            })
            .addCase(fetchEventTickets.rejected, (state) => {
                state.tickets = [];
                state.ticketsLoading = false;
                state.totalTickets = 0;
            });
    },
});

export const {
    setTicketFilter,
    resetTicketFilters,
    resetNextPageUrl,
    setEventFromSSR,
} = eventDetailSlice.actions;
export default eventDetailSlice.reducer;
