import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchMySalesOrders = createAsyncThunk(
    "orders/fetchMySalesOrders",
    async ({ url, filters, canAppendData = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendData, ...response.data };
        } else {
            throw new Error("Failed to fetch my orders");
        }
    },
);

const initialState = {
    orders: [],
    isLoading: true,
    filterChanged: false,
    nextPageUrl: null,
    filters: {
        search: "",
        status: "",
        date_from: "",
        date_to: "",
        sort: "",
    },
};

const mySalesSlice = createSlice({
    name: "mySales",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
            state.filterChanged = true;
        },
        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
            state.filterChanged = true;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchMySalesOrders.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(fetchMySalesOrders.fulfilled, (state, action) => {
                const { orders, meta } = action.payload;

                if (action.payload.canAppendData) {
                    const existingOrdersMap = new Map(
                        state.orders.map((order) => [order.id, order]),
                    );
                    const newOrders = orders.filter(
                        (order) => !existingOrdersMap.has(order.id),
                    );
                    state.orders = [...state.orders, ...newOrders];
                } else {
                    state.orders = orders;
                }

                state.isLoading = false;
                state.filterChanged = false;
                state.nextPageUrl = meta.next_page_url;
            })
            .addCase(fetchMySalesOrders.rejected, (state) => {
                state.filterChanged = false;
                state.isLoading = false;
                state.orders = [];
            });
    },
});

export const { resetFilters, setFilter } = mySalesSlice.actions;
export default mySalesSlice.reducer;
