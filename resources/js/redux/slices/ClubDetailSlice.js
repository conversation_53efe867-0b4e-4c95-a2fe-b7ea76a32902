import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchClubDetail = createAsyncThunk(
    "clubs/fetchClubDetail",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data.club;
        } else {
            throw new Error("Failed to fetch club detail");
        }
    },
);

const initialState = {
    club: null,
    clubLoading: true,
};

const clubDetailSlice = createSlice({
    name: "club",
    initialState,
    reducers: {
        setClubFromSSR: (state, action) => {
            state.club = action.payload;
            state.clubLoading = false;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchClubDetail.pending, (state) => {
                state.clubLoading = true;
            })
            .addCase(fetchClubDetail.fulfilled, (state, action) => {
                state.club = action.payload;
                state.clubLoading = false;
            })
            .addCase(fetchClubDetail.rejected, (state) => {
                state.club = null;
                state.clubLoading = false;
            });
    },
});

export const { setClubFromSSR } = clubDetailSlice.actions;
export default clubDetailSlice.reducer;
