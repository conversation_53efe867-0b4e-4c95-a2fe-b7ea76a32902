import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchEvents = createAsyncThunk(
    "events/fetchEvents",
    async ({ url, filters, canAppendEvents = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendEvents, ...response.data };
        } else {
            throw new Error("Failed to fetch events");
        }
    },
);

export const fetchFilterOptions = createAsyncThunk(
    "events/fetchFilterOptions",
    async () => {
        const response = await axios.get(route("api.events.filters"));

        if (response.data.success) {
            return response.data;
        } else {
            throw new Error("Failed to fetch filter options");
        }
    },
);

const initialState = {
    events: [],
    eventLoading: true,
    nextPageUrl: null,
    filterOptions: {},
    filterOptionsLoading: false,
    isFilterOptionsInitialized: false,
    isFilterCleared: false,
    filters: {
        categories: [],
        stadiums: [],
        clubs: [],
        countries: [],
        leagues: [],
        search: "",
        sort: "",
    },
};

const eventsSlice = createSlice({
    name: "events",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
        },

        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
        },

        setIsFilterCleared: (state, action) => {
            state.isFilterCleared = action.payload;
        },

        toggleCategories: (state, action) => {
            const { value } = action.payload;

            if (state.filters.categories.includes(value)) {
                state.filters.categories = state.filters.categories.filter(
                    (item) => item !== value,
                );
            } else {
                state.filters.categories.push(value);
            }
        },
        resetNextPageUrl: (state) => {
            state.events = [];
            state.eventLoading = true;
            state.nextPageUrl = null;
        },
        setInitialData: (state, action) => {
            console.log(action.payload);
            const { events, filters } = action.payload;
            state.events = events.data.events || [];
            state.filterOptions = filters || {};
            state.nextPageUrl = events.data.meta?.next_page_url || null;
            state.eventLoading = false;
            state.filterOptionsLoading = false;
            state.isFilterOptionsInitialized = true;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchEvents.pending, (state) => {
                state.eventLoading = true;
            })
            .addCase(fetchEvents.fulfilled, (state, action) => {
                const { events, meta } = action.payload;

                if (action.payload.canAppendEvents) {
                    // Create a map of existing events by ID for quick lookup
                    const existingEventsMap = new Map(
                        state.events.map((event) => [event.id, event]),
                    );

                    // Add only new events that don't already exist
                    const newEvents = events.filter(
                        (event) => !existingEventsMap.has(event.id),
                    );

                    // Append only unique events
                    state.events = [...state.events, ...newEvents];
                } else {
                    state.events = events;
                }

                state.nextPageUrl = meta.next_page_url;
                state.eventLoading = false;
            })
            .addCase(fetchEvents.rejected, (state) => {
                state.events = [];
                state.eventLoading = false;
            });

        builder
            .addCase(fetchFilterOptions.pending, (state) => {
                state.filterOptionsLoading = true;
            })
            .addCase(fetchFilterOptions.fulfilled, (state, action) => {
                state.filterOptions = action.payload;
                state.filterOptionsLoading = false;
                state.isFilterOptionsInitialized = true;
            })
            .addCase(fetchFilterOptions.rejected, (state) => {
                state.filterOptions = {};
                state.filterOptionsLoading = false;
            });
    },
});

export const {
    resetFilters,
    setFilter,
    toggleCategories,
    resetNextPageUrl,
    setIsFilterCleared,
    setInitialData,
} = eventsSlice.actions;

export default eventsSlice.reducer;
