import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchTicketDetail = createAsyncThunk(
    "ticket/fetchTicketDetail",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data.ticket;
        } else {
            throw new Error("Failed to fetch ticket detail");
        }
    },
);

const initialState = {
    ticket: null,
    ticketLoading: true,
    formData: {
        ticket_id: "",
        quantity: 1,
        price: "",
        face_value_price: "",
        currency_code: "EUR",
        ticket_rows: "",
        ticket_seats: "",
        quantity_split_type: "any",
        ticket_type: "",
        description: "",
        restrictions: [],
        terms_agreed: false,
    },
};

const editTicketSlice = createSlice({
    name: "editTicket",
    initialState,
    reducers: {
        setFormData: (state, action) => {
            const { key, value } = action.payload;
            if (key === "restrictions") {
                if (state.formData.restrictions.includes(Number(value))) {
                    state.formData.restrictions =
                        state.formData.restrictions.filter(
                            (item) => item !== Number(value),
                        );
                } else {
                    state.formData.restrictions.push(Number(value));
                }
            } else {
                state.formData[key] = Number(value);
            }
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchTicketDetail.pending, (state) => {
                state.ticketLoading = true;
            })
            .addCase(fetchTicketDetail.fulfilled, (state, action) => {
                const ticket = action.payload;
                state.ticket = ticket;

                state.formData = {
                    ticket_id: ticket.id || "",
                    quantity: ticket.quantity || 1,
                    price: ticket.price || "",
                    face_value_price: ticket.face_value_price || "",
                    currency_code: ticket.currency_code || "EUR",
                    ticket_rows: ticket.ticket_rows || "",
                    ticket_seats: ticket.ticket_seats || "",
                    quantity_split_type: ticket.quantity_split_type || "any",
                    ticket_type: ticket.ticket_type.value || "",
                    description: ticket.description || "",
                    restrictions: ticket.selectedRestrictions || [],
                    terms_agreed: false,
                };
                state.ticketLoading = false;
            })
            .addCase(fetchTicketDetail.rejected, (state) => {
                state.ticket = null;
                state.ticketLoading = false;
            });
    },
});

export const { setFormData } = editTicketSlice.actions;

export default editTicketSlice.reducer;
