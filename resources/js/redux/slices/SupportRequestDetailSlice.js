import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import toast from "react-hot-toast";

// Async thunk for fetching support ticket details
export const fetchSupportTicketDetails = createAsyncThunk(
    "supportRequestDetail/fetchSupportTicketDetails",
    async (requestTicketId, { rejectWithValue }) => {
        try {
            const response = await axios.get(
                route("api.support-requests.show", { id: requestTicketId }),
            );

            if (response.data.success) {
                return response.data;
            } else {
                return rejectWithValue(
                    response.data.message ||
                        "Failed to fetch support ticket details",
                );
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || error.message,
            );
        }
    },
);

// Async thunk for sending a new message
export const sendSupportMessage = createAsyncThunk(
    "supportRequestDetail/sendSupportMessage",
    async ({ requestTicketId, message, files }, { rejectWithValue }) => {
        try {
            const response = await axios.post(
                route("api.support-requests.reply", requestTicketId),
                {
                    message,
                    files,
                },
                {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                },
            );

            if (response.data.success) {
                return response.data;
            } else {
                return rejectWithValue(
                    response.data.message || "Failed to send message",
                );
            }
        } catch (error) {
            return rejectWithValue(
                error.response?.data?.message || error.message,
            );
        }
    },
);

const initialState = {
    supportRequest: null,
    isLoading: false,
    error: null,
    newMessage: "",
    isMessageSending: false,
    isInitialLoad: true,
    canScrollToBottom: true,
};

const supportRequestDetailSlice = createSlice({
    name: "supportRequestDetail",
    initialState,
    reducers: {
        setNewMessage: (state, action) => {
            state.newMessage = action.payload;
        },
        setCanScrollToBottom: (state, action) => {
            state.canScrollToBottom = action.payload;
        },
        resetSupportRequestDetail: () => initialState,
        resetNewMessages: (state) => {
            state.newMessage = "";
            state.isMessageSending = false;
        },
    },
    extraReducers: (builder) => {
        builder
            // Handle fetch support ticket details
            .addCase(fetchSupportTicketDetails.pending, (state) => {
                state.isLoading = state.isInitialLoad ? true : false;
                state.error = null;
            })
            .addCase(fetchSupportTicketDetails.fulfilled, (state, action) => {
                state.isLoading = false;
                state.supportRequest = action.payload.support_request;
                state.isInitialLoad = false;
            })
            .addCase(fetchSupportTicketDetails.rejected, (state, action) => {
                state.isLoading = false;
                state.error =
                    action.payload || "Failed to fetch support ticket details";
            })

            // Handle send support message
            .addCase(sendSupportMessage.pending, (state) => {
                state.isMessageSending = true;
                state.sendError = null;
                state.canScrollToBottom = true;
            })
            .addCase(sendSupportMessage.fulfilled, (state, action) => {
                state.supportRequest = action.payload.support_request;
                state.newMessage = "";
                state.isMessageSending = false;
            })
            .addCase(sendSupportMessage.rejected, (state, action) => {
                state.isMessageSending = false;
                state.sendError = action.payload || "Failed to send message";
            });
    },
});

export const {
    setNewMessage,
    resetSupportRequestDetail,
    resetNewMessages,
    setCanScrollToBottom,
} = supportRequestDetailSlice.actions;

export default supportRequestDetailSlice.reducer;
