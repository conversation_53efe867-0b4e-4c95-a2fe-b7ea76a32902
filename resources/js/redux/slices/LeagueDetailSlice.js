import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchLeagueDetail = createAsyncThunk(
    "leagues/fetchLeagueDetail",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data.league;
        } else {
            throw new Error("Failed to fetch league detail");
        }
    },
);

const initialState = {
    league: null,
    leagueLoading: true,
};

const leagueDetailSlice = createSlice({
    name: "league",
    initialState,
    reducers: {
        setLeagueFromSSR: (state, action) => {
            state.league = action.payload;
            state.leagueLoading = false;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchLeagueDetail.pending, (state) => {
                state.leagueLoading = true;
            })
            .addCase(fetchLeagueDetail.fulfilled, (state, action) => {
                state.league = action.payload;
                state.leagueLoading = false;
            })
            .addCase(fetchLeagueDetail.rejected, (state) => {
                state.league = null;
                state.leagueLoading = false;
            });
    },
});

export const { setLeagueFromSSR } = leagueDetailSlice.actions;
export default leagueDetailSlice.reducer;
