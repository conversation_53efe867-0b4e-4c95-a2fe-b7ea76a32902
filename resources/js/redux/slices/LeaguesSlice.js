import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchLeagues = createAsyncThunk(
    "leagues/fetchLeagues",
    async ({ url, filters, canAppendLeagues = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendLeagues, ...response.data };
        } else {
            throw new Error("Failed to fetch leagues");
        }
    },
);

export const fetchFilterOptions = createAsyncThunk(
    "leagues/fetchFilterOptions",
    async () => {
        const response = await axios.get(route("api.leagues.filters"));

        if (response.data.success) {
            return response.data;
        } else {
            throw new Error("Failed to fetch filter options");
        }
    },
);

const initialState = {
    leagues: [],
    loading: true,
    nextPageUrl: null,
    filterOptions: {},
    filterOptionsLoading: false,
    isFilterOptionsInitialized: false,
    filters: {
        seasons: [],
        countries: [],
        search: "",
        sort: "",
    },
};

const leaguesSlice = createSlice({
    name: "leagues",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
        },

        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
        },

        resetNextPageUrl: (state) => {
            state.nextPageUrl = null;
        },

        setInitialData: (state, action) => {
            const { leagues, filters } = action.payload;
            state.leagues = leagues.leagues || [];
            state.filterOptions = filters || {};
            state.nextPageUrl = leagues.meta?.next_page_url || null;
            state.loading = false;
            state.filterOptionsLoading = false;
            state.isFilterOptionsInitialized = true;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchLeagues.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchLeagues.fulfilled, (state, action) => {
                const { leagues, meta } = action.payload;

                if (action.payload.canAppendLeagues) {
                    // Create a map of existing leagues by ID for quick lookup
                    const existingLeaguesMap = new Map(
                        state.leagues.map((league) => [league.id, league]),
                    );

                    // Add only new leagues that don't already exist
                    const newLeagues = leagues.filter(
                        (league) => !existingLeaguesMap.has(league.id),
                    );

                    // Append only unique leagues
                    state.leagues = [...state.leagues, ...newLeagues];
                } else {
                    state.leagues = leagues;
                }

                state.nextPageUrl = meta.next_page_url;
                state.loading = false;
            })
            .addCase(fetchLeagues.rejected, (state) => {
                state.leagues = [];
                state.loading = false;
            });

        builder
            .addCase(fetchFilterOptions.pending, (state) => {
                state.filterOptionsLoading = true;
            })
            .addCase(fetchFilterOptions.fulfilled, (state, action) => {
                state.filterOptions = action.payload;
                state.filterOptionsLoading = false;
                state.isFilterOptionsInitialized = true;
            })
            .addCase(fetchFilterOptions.rejected, (state) => {
                state.filterOptions = {};
                state.filterOptionsLoading = false;
            });
    },
});

export const { resetFilters, setFilter, resetNextPageUrl, setInitialData } =
    leaguesSlice.actions;

export default leaguesSlice.reducer;
