import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchStadiumDetail = createAsyncThunk(
    "stadiums/fetchStadiumDetail",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data.stadium;
        } else {
            throw new Error("Failed to fetch stadium detail");
        }
    },
);

const initialState = {
    stadium: null,
    stadiumLoading: true,
};

const stadiumDetailSlice = createSlice({
    name: "stadium",
    initialState,
    reducers: {
        setStadiumFromSSR: (state, action) => {
            state.stadium = action.payload;
            state.stadiumLoading = false;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchStadiumDetail.pending, (state) => {
                state.stadiumLoading = true;
            })
            .addCase(fetchStadiumDetail.fulfilled, (state, action) => {
                state.stadium = action.payload;
                state.stadiumLoading = false;
            })
            .addCase(fetchStadiumDetail.rejected, (state) => {
                state.stadium = null;
                state.stadiumLoading = false;
            });
    },
});

export const { setStadiumFromSSR } = stadiumDetailSlice.actions;
export default stadiumDetailSlice.reducer;
