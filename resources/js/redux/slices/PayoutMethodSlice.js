import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchPayoutMethods = createAsyncThunk(
    "payoutMethod/fetchPayoutMethods",
    async ({ url }) => {
        const response = await axios.post(url);

        if (response.data.success === true) {
            return response.data;
        } else {
            throw new Error("Failed to fetch payout methods");
        }
    },
);

export const fetchPayoutMethodConfigurations = createAsyncThunk(
    "payoutMethod/fetchPayoutMethodConfigurations",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data;
        } else {
            throw new Error("Failed to fetch payout method configurations");
        }
    },
);

const initialState = {
    userPayoutMethods: [],
    loading: true,
    configurations: [],
    configurationsLoading: true,
    formData: {
        payout_method_type: "",
        country_code: "",
        bank_name: "",
        branch_name: "",
        account_type: "",
        account_holder_name: "",
        account_number: "",
        swift_code: "",
        iban: "",
        ifsc_code: "",
        routing_number: "",
        sort_code: "",
        bsb: "",
        bank_code: "",
        branch_code: "",
        institution_number: "",
        transit_number: "",
    },
};

const payoutMethodSlice = createSlice({
    name: "payoutMethod",
    initialState,
    reducers: {
        setFormData: (state, action) => {
            const { key, value } = action.payload;
            state.formData[key] = value;
        },

        resetFormData: (state) => {
            state.formData = initialState.formData;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchPayoutMethods.pending, (state) => {
                state.userPayoutMethods = [];
                state.loading = true;
            })
            .addCase(fetchPayoutMethods.fulfilled, (state, action) => {
                state.userPayoutMethods = action.payload.payoutMethods;
                state.loading = false;
            })
            .addCase(fetchPayoutMethods.rejected, (state) => {
                state.userPayoutMethods = [];
                state.loading = false;
            });

        builder
            .addCase(fetchPayoutMethodConfigurations.pending, (state) => {
                state.configurationsLoading = true;
            })
            .addCase(
                fetchPayoutMethodConfigurations.fulfilled,
                (state, action) => {
                    state.configurations = action.payload;
                    state.configurationsLoading = false;
                },
            )
            .addCase(fetchPayoutMethodConfigurations.rejected, (state) => {
                state.configurations = [];
                state.configurationsLoading = false;
            });
    },
});

export const { setFormData, resetFormData } = payoutMethodSlice.actions;

export default payoutMethodSlice.reducer;
