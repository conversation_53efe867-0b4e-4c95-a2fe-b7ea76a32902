import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchUserOrders = createAsyncThunk(
    "orders/fetchUserOrders",
    async ({
        url = route("api.orders.index"),
        filters,
        canAppendData = false,
    }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendData, ...response.data };
        } else {
            throw new Error("Failed to fetch orders");
        }
    },
);

const initialState = {
    orders: [],
    isLoading: true,
    nextPageUrl: null,
    filters: {
        search: "",
        status: "",
        date_from: "",
        date_to: "",
    },
};

const ordersSlice = createSlice({
    name: "orders",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
        },
        setFilterAtOnce: (state, action) => {
            state.filters = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchUserOrders.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(fetchUserOrders.fulfilled, (state, action) => {
                const { orders, meta } = action.payload;

                if (action.payload.canAppendData) {
                    const existingOrdersMap = new Map(
                        state.orders.map((order) => [order.id, order]),
                    );
                    const newOrders = orders.filter(
                        (order) => !existingOrdersMap.has(order.id),
                    );
                    state.orders = [...state.orders, ...newOrders];
                } else {
                    state.orders = orders;
                }

                state.isLoading = false;
                state.nextPageUrl = meta.next_page_url;
            })
            .addCase(fetchUserOrders.rejected, (state) => {
                state.orders = [];
                state.isLoading = false;
            });
    },
});

export const { resetFilters, setFilterAtOnce } = ordersSlice.actions;
export default ordersSlice.reducer;
