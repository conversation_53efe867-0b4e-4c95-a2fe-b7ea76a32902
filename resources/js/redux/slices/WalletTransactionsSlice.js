import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchWalletTransactions = createAsyncThunk(
    "withdrawals/fetchWalletTransactions",
    async ({ url, filters, canAppendData = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendData, ...response.data };
        } else {
            throw new Error("Failed to fetch user wallet transactions");
        }
    },
);

const initialState = {
    transactions: [],
    isLoading: true,
    filterChanged: false,
    nextPageUrl: null,
    filters: {
        search: "",
        transaction_type: "",
        entry_type: "",
        status: "",
        date_from: "",
        date_to: "",
        sort: "",
    },
};

const walletTransactionsSlice = createSlice({
    name: "walletTransactions",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
            state.filterChanged = true;
        },
        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
            state.filterChanged = true;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchWalletTransactions.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(fetchWalletTransactions.fulfilled, (state, action) => {
                const { transactions, meta } = action.payload;

                if (action.payload.canAppendData) {
                    const existingTransactionsMap = new Map(
                        state.transactions.map((transaction) => [
                            transaction.id,
                            transaction,
                        ]),
                    );
                    const newTransactions = transactions.filter(
                        (transaction) =>
                            !existingTransactionsMap.has(transaction.id),
                    );
                    state.transactions = [
                        ...state.transactions,
                        ...newTransactions,
                    ];
                } else {
                    state.transactions = transactions;
                }

                state.isLoading = false;
                state.filterChanged = false;
                state.nextPageUrl = meta.next_page_url;
            })
            .addCase(fetchWalletTransactions.rejected, (state) => {
                state.filterChanged = false;
                state.isLoading = false;
                state.transactions = [];
            });
    },
});

export const { resetFilters, setFilter } = walletTransactionsSlice.actions;
export default walletTransactionsSlice.reducer;
