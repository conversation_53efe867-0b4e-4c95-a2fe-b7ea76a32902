import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchWithdrawals = createAsyncThunk(
    "withdrawals/fetchWithdrawals",
    async ({ url, filters, canAppendData = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendData, ...response.data };
        } else {
            throw new Error("Failed to fetch user withdrawals");
        }
    },
);

export const fetchWithdrawalConfigurations = createAsyncThunk(
    "withdrawals/fetchWithdrawalConfigurations",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data;
        } else {
            throw new Error("Failed to fetch withdrawal configurations");
        }
    },
);

const initialState = {
    withdrawals: [],
    isLoading: true,
    filterChanged: false,
    nextPageUrl: null,
    filters: {
        search: "",
        date_from: "",
        date_to: "",
        status: "",
        sort: "",
    },
    configurations: [],
    configurationsLoading: true,
    formData: {
        amount: "",
        previous_amount: "",
        currency_code: "",
        payout_method: "",
    },
};

const withdrawalsSlice = createSlice({
    name: "withdrawals",
    initialState,
    reducers: {
        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
            state.filterChanged = true;
        },

        resetFilters: (state) => {
            state.filters = initialState.filters;
            state.filterChanged = true;
        },

        setFormData: (state, action) => {
            const { key, value } = action.payload;
            state.formData[key] = value;
        },

        resetFormData: (state) => {
            state.formData.amount = "";
            state.formData.payout_method = "";
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchWithdrawals.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(fetchWithdrawals.fulfilled, (state, action) => {
                const { withdrawals, meta } = action.payload;

                if (action.payload.canAppendData) {
                    const existingWithdrawalsMap = new Map(
                        state.withdrawals.map((withdrawal) => [
                            withdrawal.id,
                            withdrawal,
                        ]),
                    );
                    const newWithdrawals = withdrawals.filter(
                        (withdrawal) =>
                            !existingWithdrawalsMap.has(withdrawal.id),
                    );
                    state.withdrawals = [
                        ...state.withdrawals,
                        ...newWithdrawals,
                    ];
                } else {
                    state.withdrawals = withdrawals;
                }

                state.isLoading = false;
                state.filterChanged = false;
                state.nextPageUrl = meta.next_page_url;
            })
            .addCase(fetchWithdrawals.rejected, (state) => {
                state.filterChanged = false;
                state.isLoading = false;
                state.withdrawals = [];
            });

        builder
            .addCase(fetchWithdrawalConfigurations.pending, (state) => {
                state.configurationsLoading = true;
            })
            .addCase(
                fetchWithdrawalConfigurations.fulfilled,
                (state, action) => {
                    state.configurations = action.payload;
                    state.configurationsLoading = false;

                    state.formData.previous_amount =
                        state.configurations.balance;
                    state.formData.currency_code =
                        state.configurations.currency_code;
                },
            )
            .addCase(fetchWithdrawalConfigurations.rejected, (state) => {
                state.configurations = [];
                state.configurationsLoading = false;
            });
    },
});

export const { setFilter, resetFilters, setFormData, resetFormData } =
    withdrawalsSlice.actions;

export default withdrawalsSlice.reducer;
