import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchSupportEnums = createAsyncThunk(
    "supportRequests/fetchEnums",
    async () => {
        try {
            const response = await axios.get(
                route("api.support-requests.enums"),
            );

            if (response.data) {
                return response.data;
            } else {
                throw new Error("Failed to fetch support request enums");
            }
        } catch (error) {
            console.error("Error fetching support request enums:", error);
            throw new Error(error.message);
        }
    },
);

export const fetchSupportRequests = createAsyncThunk(
    "supportRequests/fetchSupportRequests",
    async ({ url, filters, canAppendData = false }) => {
        const response = await axios.get(url, { params: filters });

        if (response.data.success == true) {
            return { canAppendData, ...response.data };
        } else {
            throw new Error("Failed to fetch support requests");
        }
    },
);

const initialState = {
    supportRequests: [],
    isLoading: true,
    nextPageUrl: null,
    filters: {
        requestType: [],
        status: [],
        priority: [],
    },
    options: {
        requestTypes: [],
        statuses: [],
        priorities: [],
    },
    isOptionsLoading: true,
};

const supportRequestSlice = createSlice({
    name: "supportRequests",
    initialState,
    reducers: {
        setFilter: (state, action) => {
            state.filters[action.payload.key] = action.payload.value;
        },

        setMultipleFilters: (state, action) => {
            Object.entries(action.payload).forEach(([key, value]) => {
                state.filters[key] = value;
            });
        },

        resetFilters: (state) => {
            state.filters = initialState.filters;
        },

        resetSupportData: (state) => {
            state.supportRequests = [];
            state.nextPageUrl = null;
            state.isLoading = true;
        },
    },
    extraReducers: (builder) => {
        builder
            // Handle support request fetching states
            .addCase(fetchSupportRequests.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(fetchSupportRequests.fulfilled, (state, action) => {
                const { canAppendData, support_requests, meta } =
                    action.payload;

                if (canAppendData) {
                    const existingSupportRequestsMap = new Map(
                        state.supportRequests.map((request) => [
                            request.id,
                            request,
                        ]),
                    );

                    const newRequests = support_requests.filter(
                        (request) =>
                            !existingSupportRequestsMap.has(request.id),
                    );

                    state.supportRequests = [
                        ...state.supportRequests,
                        ...newRequests,
                    ];
                } else {
                    state.supportRequests = support_requests;
                }

                state.isLoading = false;
                state.nextPageUrl = meta.next_page_url;
            })
            .addCase(fetchSupportRequests.rejected, (state) => {
                state.isLoading = false;
            })

            // Handle enum values fetching states
            .addCase(fetchSupportEnums.pending, (state) => {
                state.isOptionsLoading = true;
            })
            .addCase(fetchSupportEnums.fulfilled, (state, action) => {
                state.isOptionsLoading = false;
                state.options.requestTypes = Object.entries(
                    action.payload.types || {},
                ).map(([value, label]) => ({
                    value,
                    label,
                }));

                state.options.statuses = Object.entries(
                    action.payload.statuses || {},
                ).map(([value, label]) => ({
                    value,
                    label,
                }));

                state.options.priorities = Object.entries(
                    action.payload.priorities || {},
                ).map(([value, label]) => ({
                    value,
                    label,
                }));
            })
            .addCase(fetchSupportEnums.rejected, (state) => {
                state.options.isLoading = false;
            });
    },
});

export default supportRequestSlice.reducer;

export const { resetFilters, setFilter, resetSupportData } =
    supportRequestSlice.actions;
