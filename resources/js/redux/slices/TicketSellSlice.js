import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchEventDetail = createAsyncThunk(
    "ticket/fetchEventDetail",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data.event;
        } else {
            throw new Error("Failed to fetch event detail");
        }
    },
);

export const fetchTicketConfigurations = createAsyncThunk(
    "ticket/fetchTicketConfigurations",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data;
        } else {
            throw new Error("Failed to fetch ticket configurations");
        }
    },
);

const initialState = {
    event: null,
    eventLoading: true,
    configurations: [],
    configurationsLoading: true,
    step: 1,
    formData: {
        event_id: "",
        quantity: 1,
        price: "",
        face_value_price: "",
        currency_code: "EUR",
        sector_id: "",
        sector_name: "",
        ticket_rows: "",
        ticket_seats: "",
        quantity_split_type: "any",
        sell_in_multiples: "",
        ticket_type: "",
        description: "",
        restrictions: [],
        terms_agreed: false,
    },
};

const ticketSellSlice = createSlice({
    name: "ticketSell",
    initialState,
    reducers: {
        setStep: (state, action) => {
            const { value } = action.payload;
            state.step = value;
        },

        setFormData: (state, action) => {
            const { key, value } = action.payload;
            if (key === "restrictions") {
                if (state.formData.restrictions.includes(value)) {
                    state.formData.restrictions =
                        state.formData.restrictions.filter(
                            (item) => item !== value,
                        );
                } else {
                    state.formData.restrictions.push(value);
                }
            } else {
                state.formData[key] = value;
            }
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchEventDetail.pending, (state) => {
                state.eventLoading = true;
            })
            .addCase(fetchEventDetail.fulfilled, (state, action) => {
                state.event = action.payload;
                state.formData.event_id = state.event.id;
                state.eventLoading = false;
            })
            .addCase(fetchEventDetail.rejected, (state) => {
                state.event = null;
                state.eventLoading = false;
            });

        builder
            .addCase(fetchTicketConfigurations.pending, (state) => {
                state.configurationsLoading = true;
            })
            .addCase(fetchTicketConfigurations.fulfilled, (state, action) => {
                state.configurations = action.payload;
                state.configurationsLoading = false;
            })
            .addCase(fetchTicketConfigurations.rejected, (state) => {
                state.configurations = [];
                state.configurationsLoading = false;
            });
    },
});

export const { setStep, setFormData } = ticketSellSlice.actions;

export default ticketSellSlice.reducer;
