import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchReservation = createAsyncThunk(
    "ticket/fetchReservation",
    async ({ url }) => {
        const response = await axios.get(url);

        if (response.data.success === true) {
            return response.data.reservation;
        } else {
            throw new Error("Failed to fetch reservation detail");
        }
    },
);

const initialState = {
    reservation: null,
    reservationLoading: true,
    attendees: [],
    paymentState: {
        orderId: null,
        clientSecret: null,
        encryptedOrderId: null,
    },
    showTimesUpPopup: false,
    step: 1,
    isAgreed: {
        terms: false,
        restrictions: false,
    },
};

const ticketCheckoutSlice = createSlice({
    name: "ticketCheckout",
    initialState,
    reducers: {
        setAttendees: (state, action) => {
            const { index, field, value } = action.payload;
            state.attendees[index][field] = value;
        },

        setIsAgreed: (state, action) => {
            const { key, value } = action.payload;
            state.isAgreed[key] = value;
        },

        setShowTimesUpPopup: (state, action) => {
            const { value } = action.payload;
            state.showTimesUpPopup = value;
        },

        setStep: (state, action) => {
            const { value } = action.payload;
            state.step = value;
        },

        setPaymentState: (state, action) => {
            state.paymentState = action.payload;
        },

        resetReservation: (state) => {
            state.reservation = null;
            state.reservationLoading = true;
            state.attendees = [];
            state.paymentState = {
                orderId: null,
                clientSecret: null,
                encryptedOrderId: null,
            };
            state.showTimesUpPopup = false;
            state.step = 1;
            state.isAgreed = {
                terms: false,
                restrictions: false,
            };
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchReservation.pending, (state) => {
                state.reservation = null;
                state.reservationLoading = true;
            })
            .addCase(fetchReservation.fulfilled, (state, action) => {
                state.reservation = action.payload;

                if (state.reservation) {
                    state.attendees = Array.from(
                        { length: state.reservation.quantity },
                        () => ({
                            name: "",
                            email: "",
                            gender: "",
                            dob: "",
                        }),
                    );

                    if (state.reservation.order) {
                        state.paymentState = {
                            orderId: state.reservation.order.id,
                            clientSecret: state.reservation.order.clientSecret,
                            encryptedOrderId:
                                state.reservation.order.encryptedOrderId,
                        };

                        state.attendees = state.reservation.order.attendees.map(
                            (attendee) => ({
                                name: attendee.name || "",
                                email: attendee.email || "",
                                gender: attendee.gender || "",
                                dob: attendee.dob || "",
                            }),
                        );
                    }
                }
                state.reservationLoading = false;
            })
            .addCase(fetchReservation.rejected, (state) => {
                state.reservation = null;
                state.reservationLoading = false;
            });
    },
});

export const {
    setAttendees,
    setIsAgreed,
    setShowTimesUpPopup,
    setStep,
    setPaymentState,
    resetReservation,
} = ticketCheckoutSlice.actions;

export default ticketCheckoutSlice.reducer;
