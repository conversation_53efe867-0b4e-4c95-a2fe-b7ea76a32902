import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { loadFromStorage, saveToStorage } from "@/helpers/translationsHelper";

export const fetchTranslations = createAsyncThunk(
    "translations/fetch",
    async ({ locale, url }, { getState }) => {
        const existing = getState().translations.data[locale];
        if (existing) return { locale, translations: existing };

        const cached = loadFromStorage(locale);
        if (cached) return { locale, translations: cached };

        const response = await axios.get(url);

        if (response.data.success === true) {
            return { locale, translations: response.data.translations };
        } else {
            throw new Error("Failed to fetch translations");
        }
    },
);

const initialState = {
    data: {},
    loading: {},
};

const translationSlice = createSlice({
    name: "translations",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchTranslations.pending, (state, action) => {
                state.loading[action.meta.arg.locale] = true;
            })
            .addCase(fetchTranslations.fulfilled, (state, action) => {
                const { locale, translations } = action.payload;
                state.data[locale] = translations;
                state.loading[locale] = false;
                saveToStorage(locale, translations);
            })
            .addCase(fetchTranslations.rejected, (state, action) => {
                state.loading[action.meta.arg.locale] = false;
            });
    },
});

export default translationSlice.reducer;
