import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchEventsList = createAsyncThunk(
    "sellTickets/fetchEventsList",
    async ({ url, filters, canAppendEvents = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendEvents, ...response.data };
        } else {
            throw new Error("Failed to fetch events");
        }
    },
);

const initialState = {
    events: [],
    eventLoading: true,
    filterChanged: false,
    nextPageUrl: null,
    filters: {
        search: "",
        sort: "",
    },
};

const sellTicketsSlice = createSlice({
    name: "sellTickets",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
            state.filterChanged = true;
        },
        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
            state.filterChanged = true;
        },
        resetNextPageUrl: (state) => {
            state.nextPageUrl = null;
        },
        setInitialData: (state, action) => {
            const { events, meta } = action.payload;
            state.events = events || [];
            state.nextPageUrl = meta?.next_page_url || null;
            state.eventLoading = false;
            state.filterChanged = false;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchEventsList.pending, (state) => {
                state.eventLoading = true;
            })
            .addCase(fetchEventsList.fulfilled, (state, action) => {
                const { events, meta } = action.payload;

                if (action.payload.canAppendEvents) {
                    // Create a map of existing events by ID for quick lookup
                    const existingEventsMap = new Map(
                        state.events.map((event) => [event.id, event]),
                    );

                    // Add only new events that don't already exist
                    const newEvents = events.filter(
                        (event) => !existingEventsMap.has(event.id),
                    );

                    // Append only unique events
                    state.events = [...state.events, ...newEvents];
                } else {
                    state.events = events;
                }
                state.filterChanged = false;
                state.nextPageUrl = meta.next_page_url;
                state.eventLoading = false;
            })
            .addCase(fetchEventsList.rejected, (state) => {
                state.events = [];
                state.filterChanged = false;
                state.eventLoading = false;
            });
    },
});

export const { resetFilters, setFilter, resetNextPageUrl, setInitialData } =
    sellTicketsSlice.actions;

export default sellTicketsSlice.reducer;
