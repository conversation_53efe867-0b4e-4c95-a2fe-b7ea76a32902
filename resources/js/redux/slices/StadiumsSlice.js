import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchStadiums = createAsyncThunk(
    "stadiums/fetchStadiums",
    async ({ url, filters, canAppendStadiums = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendStadiums, ...response.data };
        } else {
            throw new Error("Failed to fetch stadiums");
        }
    },
);

export const fetchFilterOptions = createAsyncThunk(
    "stadiums/fetchFilterOptions",
    async () => {
        const response = await axios.get(route("api.stadiums.filters"));

        if (response.data.success) {
            return response.data;
        } else {
            throw new Error("Failed to fetch filter options");
        }
    },
);

const initialState = {
    stadiums: [],
    loading: true,
    nextPageUrl: null,
    filterOptions: {},
    filterOptionsLoading: false,
    isFilterOptionsInitialized: false,
    filters: {
        countries: [],
        search: "",
        sort: "",
    },
};

const stadiumsSlice = createSlice({
    name: "stadiums",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
        },

        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
        },

        resetNextPageUrl: (state) => {
            state.nextPageUrl = null;
        },

        setInitialData: (state, action) => {
            const { stadiums, filters } = action.payload;
            state.stadiums = stadiums.stadiums || [];
            state.nextPageUrl = stadiums.meta?.next_page_url || null;
            state.filterOptions = filters || {};
            state.isFilterOptionsInitialized = true;
            state.loading = false;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchStadiums.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchStadiums.fulfilled, (state, action) => {
                const { stadiums, meta } = action.payload;

                if (action.payload.canAppendStadiums) {
                    // Create a map of existing stadiums by ID for quick lookup
                    const existingStadiumsMap = new Map(
                        state.stadiums.map((stadium) => [stadium.id, stadium]),
                    );

                    // Add only new stadiums that don't already exist
                    const newStadiums = stadiums.filter(
                        (stadium) => !existingStadiumsMap.has(stadium.id),
                    );

                    // Append only unique stadiums
                    state.stadiums = [...state.stadiums, ...newStadiums];
                } else {
                    state.stadiums = stadiums;
                }

                state.nextPageUrl = meta.next_page_url;
                state.loading = false;
            })
            .addCase(fetchStadiums.rejected, (state) => {
                state.stadiums = [];
                state.loading = false;
            });

        builder
            .addCase(fetchFilterOptions.pending, (state) => {
                state.filterOptionsLoading = true;
            })
            .addCase(fetchFilterOptions.fulfilled, (state, action) => {
                state.filterOptions = action.payload;
                state.filterOptionsLoading = false;
                state.isFilterOptionsInitialized = true;
            })
            .addCase(fetchFilterOptions.rejected, (state) => {
                state.filterOptions = {};
                state.filterOptionsLoading = false;
            });
    },
});

export const { resetFilters, setFilter, resetNextPageUrl, setInitialData } =
    stadiumsSlice.actions;

export default stadiumsSlice.reducer;
