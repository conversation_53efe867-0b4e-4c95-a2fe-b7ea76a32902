import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchMyTickets = createAsyncThunk(
    "tickets/fetchMyTickets",
    async ({ url, filters, canAppendData = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendData, ...response.data };
        } else {
            throw new Error("Failed to fetch my tickets");
        }
    },
);

const initialState = {
    tickets: [],
    isLoading: true,
    filterChanged: false,
    nextPageUrl: null,
    filters: {
        search: "",
        ticket_type: "",
        date_from: "",
        date_to: "",
        sort: "",
    },
};

const myTicketsSlice = createSlice({
    name: "myTickets",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
            state.filterChanged = true;
        },
        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
            state.filterChanged = true;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchMyTickets.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(fetchMyTickets.fulfilled, (state, action) => {
                const { tickets, meta } = action.payload;

                if (action.payload.canAppendData) {
                    const existingTicketsMap = new Map(
                        state.tickets.map((ticket) => [ticket.id, ticket]),
                    );
                    const newTickets = tickets.filter(
                        (ticket) => !existingTicketsMap.has(ticket.id),
                    );
                    state.tickets = [...state.tickets, ...newTickets];
                } else {
                    state.tickets = tickets;
                }

                state.isLoading = false;
                state.filterChanged = false;
                state.nextPageUrl = meta.next_page_url;
            })
            .addCase(fetchMyTickets.rejected, (state) => {
                state.filterChanged = false;
                state.isLoading = false;
                state.tickets = [];
            });
    },
});

export const { resetFilters, setFilter } = myTicketsSlice.actions;
export default myTicketsSlice.reducer;
