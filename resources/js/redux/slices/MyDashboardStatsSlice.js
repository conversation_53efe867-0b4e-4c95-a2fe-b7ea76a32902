import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchDashboardStats = createAsyncThunk(
    "dashboard/fetchDashboardStats",
    async ({ url, filters }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return response.data;
        } else {
            throw new Error("Failed to fetch my orders");
        }
    },
);

const initialState = {
    stats: null,
    isLoading: true,
    dateRangeFilters: [],
    filters: {
        period: "all_time",
    },
};

const myDashboardStatsSlice = createSlice({
    name: "myDashboardStats",
    initialState,
    reducers: {
        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchDashboardStats.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(fetchDashboardStats.fulfilled, (state, action) => {
                const { stats, dateRangeFilters } = action.payload;

                state.stats = stats;
                state.dateRangeFilters = dateRangeFilters;
                state.isLoading = false;
            })
            .addCase(fetchDashboardStats.rejected, (state) => {
                state.isLoading = false;
                state.stats = null;
                state.dateRangeFilters = null;
            });
    },
});

export const { setFilter } = myDashboardStatsSlice.actions;
export default myDashboardStatsSlice.reducer;
