import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

export const fetchClubs = createAsyncThunk(
    "clubs/fetchClubs",
    async ({ url, filters, canAppendClubs = false }) => {
        const response = await axios.post(url, filters);

        if (response.data.success === true) {
            return { canAppendClubs, ...response.data };
        } else {
            throw new Error("Failed to fetch clubs");
        }
    },
);

export const fetchFilterOptions = createAsyncThunk(
    "clubs/fetchFilterOptions",
    async () => {
        const response = await axios.get(route("api.clubs.filters"));

        if (response.data.success) {
            return response.data;
        } else {
            throw new Error("Failed to fetch filter options");
        }
    },
);

const initialState = {
    clubs: [],
    loading: true,
    nextPageUrl: null,
    filterOptions: {},
    filterOptionsLoading: false,
    isFilterOptionsInitialized: false,
    filters: {
        stadiums: [],
        countries: [],
        search: "",
        sort: "",
    },
};

const clubsSlice = createSlice({
    name: "clubs",
    initialState,
    reducers: {
        resetFilters: (state) => {
            state.filters = initialState.filters;
        },

        setFilter: (state, action) => {
            const { key, value } = action.payload;
            state.filters[key] = value;
        },

        resetNextPageUrl: (state) => {
            state.nextPageUrl = null;
        },

        setInitialData: (state, action) => {
            const { clubs, filters } = action.payload;
            state.clubs = clubs.clubs || [];
            state.filterOptions = filters || {};
            state.nextPageUrl = clubs.meta?.next_page_url || null;
            state.loading = false;
            state.filterOptionsLoading = false;
            state.isFilterOptionsInitialized = true;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchClubs.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchClubs.fulfilled, (state, action) => {
                const { clubs, meta } = action.payload;

                if (action.payload.canAppendClubs) {
                    // Create a map of existing clubs by ID for quick lookup
                    const existingClubsMap = new Map(
                        state.clubs.map((club) => [club.id, club]),
                    );

                    // Add only new Clubs that don't already exist
                    const newClubs = clubs.filter(
                        (club) => !existingClubsMap.has(club.id),
                    );

                    // Append only unique clubs
                    state.clubs = [...state.clubs, ...newClubs];
                } else {
                    state.clubs = clubs;
                }

                state.nextPageUrl = meta.next_page_url;
                state.loading = false;
            })
            .addCase(fetchClubs.rejected, (state) => {
                state.clubs = [];
                state.loading = false;
            });

        builder
            .addCase(fetchFilterOptions.pending, (state) => {
                state.filterOptionsLoading = true;
            })
            .addCase(fetchFilterOptions.fulfilled, (state, action) => {
                state.filterOptions = action.payload;
                state.filterOptionsLoading = false;
                state.isFilterOptionsInitialized = true;
            })
            .addCase(fetchFilterOptions.rejected, (state) => {
                state.filterOptions = {};
                state.filterOptionsLoading = false;
            });
    },
});

export const { resetFilters, setFilter, resetNextPageUrl, setInitialData } =
    clubsSlice.actions;

export default clubsSlice.reducer;
