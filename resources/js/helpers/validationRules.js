import dayjs from "dayjs";

function formatMessage(message, replacements = {}) {
    let formatted = message;
    for (const key in replacements) {
        const regex = new RegExp(`:${key}`, "g");
        formatted = formatted.replace(regex, replacements[key]);
    }
    return formatted;
}

function humanizeFieldName(fieldName) {
    return fieldName
        .replace(/\.\d+\./g, ".") // removes ".0.", ".1." → "."
        .replace(/\./g, " ") // replace "." with space
        .replace(/_/g, " ") // replace "_" with space
        .replace(/\b\w/g, (char) => char.toUpperCase());
}

/**
 * @param {function} translate - translation function: translate(key, defaultMessage)
 */
export default function getValidationRules(translate) {
    return {
        required: (value, formData, fieldName) => {
            if (
                typeof value === "undefined" ||
                value === null ||
                value === ""
            ) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.required",
                    "The :attribute field is required.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        email: (value, formData, fieldName) => {
            if (!value) return null;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.email",
                    "The :attribute must be a valid email address.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        number: (value, formData, fieldName) => {
            if (!value) return null;
            if (isNaN(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.numeric",
                    "The :attribute must be a number.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        min: (min) => (value, formData, fieldName) => {
            if (!value) return null;
            if (Number(value) < min) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.min.numeric",
                    "The :attribute must be at least :min.",
                );
                return formatMessage(msgTemplate, { attribute: label, min });
            }
            return null;
        },

        max: (max) => (value, formData, fieldName) => {
            if (!value) return null;
            if (Number(value) > max) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.max.numeric",
                    "The :attribute may not be greater than :max.",
                );
                return formatMessage(msgTemplate, { attribute: label, max });
            }
            return null;
        },

        maxlength: (max) => (value, formData, fieldName) => {
            if (typeof value === "string" && value.length > max) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.max.string",
                    "The :attribute may not be greater than :max characters.",
                );
                return formatMessage(msgTemplate, { attribute: label, max });
            }
            return null;
        },

        minlength: (min) => (value, formData, fieldName) => {
            if (typeof value === "string" && value.length < min) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.min.string",
                    "The :attribute must be at least :min characters.",
                );
                return formatMessage(msgTemplate, { attribute: label, min });
            }
            return null;
        },

        date: (value, formData, fieldName) => {
            if (!value) return null;
            if (isNaN(Date.parse(value))) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.date",
                    "The :attribute is not a valid date.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        greaterThan: (fieldToCompare) => (value, formData, fieldName) => {
            if (!value || !formData[fieldToCompare]) return null;

            if (value <= formData[fieldToCompare]) {
                const label = humanizeFieldName(fieldName);
                const compareLabel = humanizeFieldName(fieldToCompare);
                const msgTemplate = translate(
                    "validation.greater_than",
                    "The :attribute must be greater than :other.",
                );
                return formatMessage(msgTemplate, {
                    attribute: label,
                    other: compareLabel,
                });
            }
            return null;
        },

        maxDate: (maxDate) => (value, formData, fieldName) => {
            if (!value) return null;

            const selectedDate = dayjs(value).startOf("day");
            const today = dayjs().startOf("day");

            if (selectedDate.isAfter(maxDate)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.before",
                    "The :attribute must be before :date.",
                );
                return formatMessage(msgTemplate, {
                    attribute: label,
                    date: dayjs(maxDate).format("DD/MM/YYYY"),
                });
            }

            return null;
        },

        array: (value, formData, fieldName) => {
            if (!Array.isArray(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.array",
                    "The :attribute must be an array.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        minArrayLength: (min) => (value, formData, fieldName) => {
            if (!Array.isArray(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.array",
                    "The :attribute must be an array.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            if (value.length < min) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.min.array",
                    "The :attribute must have at least :min items.",
                );
                return formatMessage(msgTemplate, { attribute: label, min });
            }
            return null;
        },

        equalsField: (fieldToCompare) => (value, formData, fieldName) => {
            if (value !== formData[fieldToCompare]) {
                const label = humanizeFieldName(fieldName);
                const compareLabel = humanizeFieldName(fieldToCompare);
                const msgTemplate = translate(
                    "validation.confirmed",
                    "The :attribute confirmation does not match.",
                );
                return formatMessage(msgTemplate, {
                    attribute: label,
                    other: compareLabel,
                });
            }
            return null;
        },

        account_number: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{6,20}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.account_number",
                    "The :attribute must be a valid account number.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        account_holder_name: (value, formData, fieldName) => {
            if (!value) return null;

            const label = humanizeFieldName(fieldName);

            // Only letters and spaces, at least 2 characters, max 50
            const regex = /^[a-zA-Z\s]{2,50}$/;

            if (!regex.test(value.trim())) {
                const msgTemplate = translate(
                    "validation.custom.account_holder_name",
                    "The :attribute may only contain letters and spaces (2–50 characters).",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }

            return null;
        },

        iban: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[A-Z]{2}[0-9A-Z]{13,32}$/;
            if (!regex.test(value.toUpperCase())) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.iban",
                    "The :attribute must be a valid IBAN.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        swift_code: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
            if (!regex.test(value.toUpperCase())) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.swift_code",
                    "The :attribute must be a valid SWIFT code.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        ifsc_code: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.ifsc_code",
                    "The :attribute must be a valid IFSC code.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        routing_number: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{9}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.routing_number",
                    "The :attribute must be a 9-digit routing number.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }

            const digits = value.split("").map(Number);
            const checksum =
                3 * (digits[0] + digits[3] + digits[6]) +
                7 * (digits[1] + digits[4] + digits[7]) +
                (digits[2] + digits[5] + digits[8]);

            if (checksum % 10 !== 0) {
                const msgTemplate = translate(
                    "validation.custom.routing_number_checksum",
                    "The :attribute is not a valid routing number (checksum failed).",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        sort_code: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{6}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.sort_code",
                    "The :attribute must be a valid 6-digit sort code.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        bsb: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{6}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.bsb",
                    "The :attribute must be a valid 6-digit BSB number.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        bank_code: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{3,5}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.bank_code",
                    "The :attribute must be a valid bank code.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        branch_code: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{1,6}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.branch_code",
                    "The :attribute must be a valid branch code.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        institution_number: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{3}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.institution_number",
                    "The :attribute must be a valid 3-digit institution number.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },

        transist_number: (value, formData, fieldName) => {
            if (!value) return null;
            const regex = /^[0-9]{5}$/;
            if (!regex.test(value)) {
                const label = humanizeFieldName(fieldName);
                const msgTemplate = translate(
                    "validation.custom.transist_number",
                    "The :attribute must be a valid 5-digit transit number.",
                );
                return formatMessage(msgTemplate, { attribute: label });
            }
            return null;
        },
    };
}
