export const getStatusBadgeClass = (color = "") => {
    const colorKey = color.toLowerCase();

    const badgeColors = {
        success: "bg-green-100 text-green-800 border-green-200",
        warning: "bg-yellow-100 text-yellow-800 border-yellow-200",
        info: "bg-blue-100 text-blue-800 border-blue-200",
        danger: "bg-red-100 text-red-800 border-red-200",
    };

    return badgeColors[colorKey] || "bg-gray-100 text-gray-800 border-gray-200";
};

export const getSupportStatusBadgeClass = (status) => {
    const statusClasses = {
        pending: "bg-green-100 text-green-800",
        inprogress: "bg-blue-100 text-blue-800",
        resolved: "bg-yellow-100 text-yellow-800",
        closed: "bg-gray-100 text-gray-800",
    };

    return statusClasses[status] || "bg-gray-100 text-gray-800";
};

export const getSupportPriorityBadgeClass = (priority) => {
    const priorityClasses = {
        low: "bg-green-100 text-green-800",
        medium: "bg-yellow-100 text-yellow-800",
        high: "bg-red-100 text-red-800",
    };

    return priorityClasses[priority] || "bg-gray-100 text-gray-800";
};
