export function getFilteredQueryParams(allowedKeys = [], clearParams = false) {
    // Check if window is available (not during SSR)
    if (typeof window === "undefined") {
        return {};
    }

    const params = new URLSearchParams(window.location.search);
    const result = {};

    for (const [key, value] of params.entries()) {
        // Extract the base key name (e.g., 'categories' from 'categories[0]')
        const baseKey = key.replace(/\[\d*\]$/, "");

        if (allowedKeys.length && !allowedKeys.includes(baseKey)) continue;

        // If key contains square brackets, treat it as an array
        if (key.includes("[")) {
            if (!result[baseKey]) {
                result[baseKey] = [];
            }
            result[baseKey].push(value);
        } else {
            result[key] = value;
        }
    }

    if (
        clearParams &&
        typeof window !== "undefined" &&
        typeof document !== "undefined"
    ) {
        setTimeout(() => {
            window.history.replaceState(
                {},
                document.title,
                window.location.pathname,
            );
        }, 500);
    }

    return result;
}
