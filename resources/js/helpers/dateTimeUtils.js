import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import advancedFormat from "dayjs/plugin/advancedFormat";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

const createDateTimeObject = (dayjsInstance) => {
    if (!dayjsInstance || !dayjsInstance.isValid()) {
        return null;
    }
    return {
        dayjs: dayjsInstance,
        toIsoString: () => dayjsInstance.toISOString(),
        toDateString: (formatType) =>
            dayjsInstance.format(formatType ?? "DD/MM/YYYY"),
        toTimeString: (formatType) =>
            dayjsInstance.format(formatType ?? "h:mm A z"),
        toFormattedDateTime: (formatType) =>
            dayjsInstance.format(formatType ?? "DD/MM/YYYY | h:mm A"),
        getFullTimezoneName: (formatType) =>
            dayjsInstance.format(formatType ?? "zzz"),
        getShortTimezoneName: () =>
            dayjsInstance
                .format("zzz")
                .split(" ")
                .map((word) => word[0])
                .join(""),
        format: (formatType) => dayjsInstance.format(formatType),
    };
};

export const convertToTimezone = (dateTime, targetTimezone, sourceTimezone) => {
    if (!dateTime || !targetTimezone) {
        return null;
    }

    // If a source timezone is provided, parse the dateTime within that context.
    // Otherwise, assume the dateTime is in UTC.
    const sourceDateTime = sourceTimezone
        ? dayjs.tz(dateTime, sourceTimezone)
        : dayjs.utc(dateTime);

    if (!sourceDateTime.isValid()) {
        return null;
    }

    const targetDateTime = sourceDateTime.tz(targetTimezone);
    return createDateTimeObject(targetDateTime);
};

export const convertToLocalTimezone = (dateTime, sourceTimezone) => {
    const userTimezone = dayjs.tz.guess();
    return convertToTimezone(dateTime, userTimezone, sourceTimezone);
};
