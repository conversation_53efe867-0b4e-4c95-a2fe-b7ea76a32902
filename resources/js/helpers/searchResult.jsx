import { CalendarDays, Shield<PERSON>heck, MapPinned, House } from "lucide-react";

export const searchResultTypeIcon = (type) => {
    const icons = {
        event: <CalendarDays className="w-5 h-5 inline-block mr-1" />,
        stadium: <MapPinned className="w-5 h-5 inline-block mr-1" />,
        league: <ShieldCheck className="w-5 h-5 inline-block mr-1" />,
        club: <House className="w-5 h-5 inline-block mr-1" />,
    };
    return icons[type.toLowerCase()] || null;
};

export const highlight = (text, query) => {
    if (!query) return text;
    const regex = new RegExp(`(${query})`, "gi");
    return text.split(regex).map((part, i) =>
        part.toLowerCase() === query.toLowerCase() ? (
            <span key={i} className="font-semibold">
                {part}
            </span>
        ) : (
            part
        ),
    );
};
