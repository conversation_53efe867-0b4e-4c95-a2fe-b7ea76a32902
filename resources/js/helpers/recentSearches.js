const RECENT_SEARCHES_KEY = "recent_searches";
const MAX_ENTRIES = 10;

export function getRecentSearches() {
    if (typeof window === "undefined") return [];
    const data = localStorage.getItem(RECENT_SEARCHES_KEY);
    return data ? JSON.parse(data) : [];
}

export function addRecentSearch(query) {
    if (!query) return;
    const items = getRecentSearches().filter((item) => item !== query);
    items.unshift(query);
    if (items.length > MAX_ENTRIES) items.pop();
    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(items));
}

export function clearRecentSearches() {
    localStorage.removeItem(RECENT_SEARCHES_KEY);
}
