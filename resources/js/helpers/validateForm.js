/**
 * @param {Object} formData - data to validate
 * @param {Object} schema - { fieldName: [ruleFn1, ruleFn2, ...] }
 * @returns {Object} errors - { fieldName: errorMessage }
 */
export function validateForm(formData, schema) {
    const errors = {};

    for (const field in schema) {
        const rules = schema[field];

        // Check for wildcard array field like "attendees.*.name"
        const wildcardMatch = field.match(/^(.+)\.\*\.(.+)$/);

        if (wildcardMatch) {
            const arrayField = wildcardMatch[1]; // e.g. attendees
            const subField = wildcardMatch[2]; // e.g. name

            const arrayValue = formData[arrayField];
            if (Array.isArray(arrayValue)) {
                arrayValue.forEach((item, index) => {
                    const fullField = `${arrayField}.${index}.${subField}`;
                    const value = item?.[subField];

                    for (const rule of rules) {
                        const error = rule(value, formData, fullField);
                        if (error) {
                            errors[fullField] = error;
                            break; // stop on first error per field
                        }
                    }
                });
            }
        } else {
            // Normal field
            const value = formData[field];

            for (const rule of rules) {
                const error = rule(value, formData, field);
                if (error) {
                    errors[field] = error;
                    break; // stop on first error per field
                }
            }
        }
    }

    return errors;
}
