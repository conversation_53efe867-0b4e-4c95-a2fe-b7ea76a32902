const CACHE_DURATION_MS = 1000 * 60 * 60; // 1 hour

export function getStorageKey(locale) {
    return `tg_translations_${locale}`;
}

export function loadFromStorage(locale) {
    try {
        const data = localStorage.getItem(getStorageKey(locale));
        if (!data) return null;

        const { translations, timestamp, version } = JSON.parse(data);
        if (version !== __TRANSLATIONS_VERSION__) {
            localStorage.removeItem(getStorageKey(locale));
            return null;
        }
        if (Date.now() - timestamp > CACHE_DURATION_MS) {
            localStorage.removeItem(getStorageKey(locale));
            return null;
        }

        return translations;
    } catch {
        return null;
    }
}

export function saveToStorage(locale, translations) {
    localStorage.setItem(
        getStorageKey(locale),
        JSON.stringify({
            translations,
            timestamp: Date.now(),
            version: __TRANSLATIONS_VERSION__,
        }),
    );
}
