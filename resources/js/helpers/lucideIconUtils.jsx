import {
    AlertCircle,
    CheckCircle,
    Lock,
    CircleAlert,
    CircleCheck,
    Clock3,
} from "lucide-react";

export const getSupportStatusIcon = (status) => {
    const icons = {
        pending: <Clock3 className="w-3.5 h-3.5 mr-1" />,
        inprogress: <AlertCircle className="w-3.5 h-3.5 mr-1" />,
        resolved: <CheckCircle className="w-3.5 h-3.5 mr-1" />,
        closed: <Lock className="w-3.5 h-3.5 mr-1" />,
    };
    return (
        icons[status.toLowerCase()] || (
            <CircleAlert className="w-3.5 h-3.5 mr-1" />
        )
    );
};

export const getSupportPriorityIcon = (priority) => {
    const icons = {
        high: <CircleAlert className="w-3.5 h-3.5 text-red-600 mr-1" />,
        medium: <CircleAlert className="w-3.5 h-3.5 text-amber-600 mr-1" />,
        low: <CircleCheck className="w-3.5 h-3.5 text-emerald-600 mr-1" />,
    };

    return (
        icons[priority.toLowerCase()] || (
            <CircleAlert className="w-3.5 h-3.5 text-gray-600 mr-1" />
        )
    );
};
