import dayjs from "dayjs";

export const formatDate = (dateString, format = "DD/MM/YYYY") => {
    if (!dateString) return "";
    return dayjs(dateString).format(format);
};

export const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    return dayjs(dateString).format("YYYY-MM-DD");
};

export const formatTime = (timeString) => {
    if (!timeString) return null;
    try {
        // Handle full datetime strings (ISO format)
        if (timeString.includes("T") || timeString.includes("-")) {
            return new Date(timeString).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
            });
        }
        // Handle time-only strings (HH:MM:SS format)
        return new Date(`1970-01-01T${timeString}`).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
        });
    } catch {
        return timeString;
    }
};

export const formatCurrency = (amount, currency = "EUR") => {
    return new Intl.NumberFormat("en-EU", {
        style: "currency",
        currency: currency,
    }).format(amount);
};
