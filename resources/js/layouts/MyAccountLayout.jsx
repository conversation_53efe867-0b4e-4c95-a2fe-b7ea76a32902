import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountSidebar from "@/components/myaccount/MyAccountSidebar";
import MyAccountContent from "@/components/myaccount/MyAccountContent";

function MyAccountLayout(props) {
    const { auth, activeMenu, children } = props;
    const { translate } = useSSRTranslations();

    return (
        <>
            <div className="py-3">
                <div className="container px-4 mx-auto">
                    <h1 className="mb-4 text-2xl font-bold">
                        Welcome Back, {auth.user.name}
                    </h1>

                    <div className="flex flex-col gap-6 md:flex-row">
                        <MyAccountSidebar activeMenu={activeMenu} />

                        <MyAccountContent activeMenu={activeMenu}>
                            {children}
                        </MyAccountContent>
                    </div>
                </div>
            </div>
        </>
    );
}

MyAccountLayout.layout = (page) => <AppLayout children={page} />;

export default MyAccountLayout;
