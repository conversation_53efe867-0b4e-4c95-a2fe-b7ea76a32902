import AppLayout from "@/layouts/AppLayout";
import { MapPin } from "lucide-react";
import { Head, usePage } from "@inertiajs/react";
import { useEffect } from "react";
import LeagueEventList from "@/components/leagues/LeagueEventList";
import useLeagueDetail from "@/hooks/useLeagueDetail";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function Show() {
    const { translate } = useSSRTranslations();
    const { slug, initialData } = usePage().props;
    const { league, getLeagueDetail, leagueLoading, setFromSSR } =
        useLeagueDetail();

    useEffect(() => {
        if (initialData) {
            setFromSSR(initialData);
        } else {
            getLeagueDetail(slug);
        }
    }, [initialData, slug, setFromSSR]);

    if (leagueLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head>
                <title>{league.name}</title>
                <meta name="title" content={league.meta_title} />
                <meta name="keywords" content={league.meta_keywords} />
                <meta name="description" content={league.meta_description} />
            </Head>
            <div className="container mx-auto px-10 py-5">
                <div className="relative">
                    {league.image !== "" ? (
                        <img
                            src={league.image}
                            alt={league.image_alt || league.name}
                            className="w-full h-96 object-cover rounded-xl"
                        />
                    ) : (
                        <div className="bg-base-200 w-full h-96 flex items-center justify-center rounded-xl text-gray-500">
                            <img
                                src="/img/ticketgol-logo.png"
                                alt={league.name}
                                className="object-cover w-1/3"
                            />
                        </div>
                    )}
                    <div className="absolute inset-0 bg-black bg-opacity-65 flex flex-col rounded-xl justify-center items-center text-white text-center px-4">
                        <h1 className="text-xl sm:text-3xl font-bold mb-2">
                            {league.name}
                        </h1>
                        <div className="flex items-center text-warning font-medium">
                            <MapPin className="w-4 h-4 mr-2" />
                            <span className="text-base sm:text-lg">
                                {league.country.name}
                            </span>
                        </div>
                        <p className="text-sm sm:text-base mt-2 max-w-xl">
                            {league.description}
                        </p>
                    </div>
                </div>

                <LeagueEventList />
            </div>
        </>
    );
}

Show.layout = (page) => <AppLayout children={page} />;

export default Show;
