import React from "react";
import { Head } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function UnderConstruction() {
    const { translate } = useSSRTranslations();
    return (
        <>
            <Head title={translate("common.under_construction_text")} />
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600">
                <div className="text-center">
                    <h1 className="text-6xl font-bold text-white mb-4">
                        {translate("common.under_construction_text")}
                    </h1>
                    <p className="text-xl text-white mb-8">
                        {translate("common.working_hard_text")}
                    </p>
                    <div className="animate-bounce">
                        <svg
                            className="w-16 h-16 text-yellow-300 mx-auto"
                            fill="none"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                    </div>
                    <p className="text-white mt-8">
                        {translate("common.comming_soon_text")}
                    </p>
                </div>
            </div>
        </>
    );
}

UnderConstruction.layout = (page) => <AppLayout children={page} />;

export default UnderConstruction;
