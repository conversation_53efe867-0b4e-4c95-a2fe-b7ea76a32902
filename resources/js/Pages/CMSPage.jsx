import { Head } from "@inertiajs/react";

import AppLayout from "@/layouts/AppLayout";

function CMSPage({ cmsData }) {
    return (
        <>
            <Head>
                <title>{cmsData.translation.title}</title>
                <meta name="title" content={cmsData.translation.meta_title} />
                <meta
                    name="description"
                    content={cmsData.translation.meta_description}
                />
            </Head>
            <div
                className="prose"
                dangerouslySetInnerHTML={{
                    __html: cmsData.translation.content,
                }}
            />
        </>
    );
}

CMSPage.layout = (page) => <AppLayout children={page} />;

export default CMSPage;
