import { Head, <PERSON> } from "@inertiajs/react";

import AppLayout from "@/layouts/AppLayout";
import { useEffect } from "react";
import { useState } from "react";
import { CircleCheckBig, AlertCircle } from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function CheckoutSuccess({ orderId }) {
    const { translate } = useSSRTranslations();

    const [isPaymentVerified, setIsPaymentVerified] = useState(false);
    const [retryCount, setRetryCount] = useState(0);
    const [verificationFailed, setVerificationFailed] = useState(false);
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 3000; // 3 seconds

    useEffect(() => {
        const verifyPayment = async () => {
            if (retryCount >= MAX_RETRIES) {
                setVerificationFailed(true);
                return;
            }

            try {
                let params = { order_id: orderId };

                if (retryCount === 2) {
                    params.check_payment_status = true;
                }
                const response = await axios.post(
                    route("api.orders.check-status"),
                    params,
                );
                console.log(
                    `Verification attempt ${retryCount + 1}:`,
                    response,
                );

                if (response.data.is_completed) {
                    setIsPaymentVerified(true);
                } else {
                    // If not verified, schedule a retry after delay
                    setTimeout(() => {
                        setRetryCount((prevCount) => prevCount + 1);
                    }, RETRY_DELAY);
                }
            } catch (error) {
                console.log(
                    `Verification attempt ${retryCount + 1} failed:`,
                    error,
                );
                // Schedule a retry after delay
                setTimeout(() => {
                    setRetryCount((prevCount) => prevCount + 1);
                }, RETRY_DELAY);
            }
        };

        verifyPayment();
    }, [retryCount]);

    return (
        <>
            <Head title={translate("checkout.success_head_title")} />
            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div className="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                        <div className="p-6 py-24 text-gray-900">
                            <div className="text-center">
                                {isPaymentVerified ? (
                                    <CircleCheckBig
                                        className="mx-auto my-6 text-green-500"
                                        size={48}
                                    />
                                ) : verificationFailed ? (
                                    <AlertCircle
                                        className="mx-auto my-6 text-amber-500"
                                        size={48}
                                    />
                                ) : (
                                    <div className="mx-auto my-6 w-12 h-12 rounded-full bg-gray-200 animate-pulse flex items-center justify-center relative">
                                        <div className="w-8 h-8 rounded-full bg-gray-300 absolute animate-ping"></div>
                                        <div className="w-6 h-6 rounded-full border-2 border-gray-400 border-t-transparent animate-spin"></div>
                                    </div>
                                )}
                                <h2 className="mt-4 text-2xl font-bold">
                                    {isPaymentVerified
                                        ? translate("checkout.payment_verified")
                                        : verificationFailed
                                          ? translate(
                                                "checkout.verification_taken_longer_text",
                                            )
                                          : `${translate("checkout.wait_text")} (${translate("checkout.attempt_text")} ${retryCount + 1}/${MAX_RETRIES})`}
                                </h2>
                                {isPaymentVerified && (
                                    <p className="mt-2 text-gray-600">
                                        {translate(
                                            "checkout.tickets_reserved_text",
                                        )}
                                    </p>
                                )}
                                {verificationFailed && (
                                    <div className="mt-2 text-gray-600">
                                        <p>
                                            {translate(
                                                "checkout.processing_payment_text",
                                            )}
                                        </p>
                                        <p className="mt-2">
                                            {translate(
                                                "checkout.processing_payment_desc",
                                            )}
                                        </p>
                                        <p className="mt-2 text-sm text-gray-500">
                                            {translate(
                                                "checkout.contact_support",
                                            )}
                                        </p>
                                    </div>
                                )}
                                <div className="mt-6">
                                    {(isPaymentVerified ||
                                        verificationFailed) && (
                                        <Link
                                            href={route("dashboard")}
                                            className="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                        >
                                            {translate(
                                                "checkout.return_to_dashboard",
                                            )}
                                        </Link>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

CheckoutSuccess.layout = (page) => <AppLayout children={page} />;

export default CheckoutSuccess;
