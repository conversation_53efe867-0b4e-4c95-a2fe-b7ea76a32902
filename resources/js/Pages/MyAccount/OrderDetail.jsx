import React from "react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import useOrderDetail from "@/hooks/useOrderDetail";
import OrderSummary from "@/components/orders/detail/OrderSummary";
import EventInfo from "@/components/orders/detail/EventInfo";
import StadiumInfo from "@/components/orders/detail/StadiumInfo";
import TicketInfo from "@/components/orders/detail/TicketInfo";
import BuyerInfo from "@/components/orders/detail/BuyerInfo";
import RestrictionsInfo from "@/components/orders/detail/RestrictionsInfo";
import TransactionInfo from "@/components/orders/detail/TransactionInfo";
import AttendeesInfo from "@/components/orders/detail/AttendeesInfo";
import Spinner from "@/components/ui/Spinner";

function OrderDetail({ id }) {
    const { translate } = useSSRTranslations();
    const { order, isLoading /* error not used */ } = useOrderDetail(id);

    return (
        <>
            <Head title={translate("order.title", "My Orders")} />

            <div className="py-6 min-h-screen bg-gray-50">
                <div className="container px-4 mx-auto max-w-7xl">
                    {/* Page Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-2">
                            <h1 className="text-md sm:text-3xl font-bold text-gray-900">
                                {translate(
                                    "order.detail_title",
                                    "Order Details",
                                )}
                            </h1>
                            <Link
                                href={route("my-account.orders")}
                                className="inline-flex items-center text-blue-600 transition-colors duration-200 hover:text-blue-800"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="mr-1 w-5 h-5"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M15 19l-7-7 7-7"
                                    />
                                </svg>
                                {translate("order.back", "Back to Orders")}
                            </Link>
                        </div>

                        <p className="text-gray-600">
                            {translate(
                                "order.subtitle",
                                "Complete information about your order",
                            )}
                        </p>
                    </div>

                    {isLoading ? (
                        <div className="flex justify-center items-center py-12">
                            <Spinner size="lg" color="primary" />
                            <span className="ml-3 text-gray-600">
                                {translate("common.loading", "Loading...")}
                            </span>
                        </div>
                    ) : order ? (
                        <div className="space-y-6">
                            {/* Order Summary Header */}
                            <OrderSummary order={order} />

                            {/* Enhanced Event Information */}
                            {order.ticket?.event && (
                                <EventInfo event={order.ticket.event} />
                            )}

                            {/* Enhanced Stadium Information */}
                            {order.ticket?.event?.stadium && (
                                <StadiumInfo
                                    stadium={order.ticket.event.stadium}
                                />
                            )}

                            {/* Enhanced Ticket Information */}
                            {order.ticket && (
                                <TicketInfo ticket={order.ticket} />
                            )}

                            {/* Enhanced Buyer Information */}
                            {order.buyer && <BuyerInfo buyer={order.buyer} />}

                            {/* Enhanced Attendees Information */}
                            {order.attendees.length > 0 && (
                                <AttendeesInfo attendees={order.attendees} />
                            )}

                            {/* Enhanced Restrictions */}
                            {order.combined_restrictions.length > 0 && (
                                <RestrictionsInfo
                                    restrictions={order.combined_restrictions}
                                />
                            )}

                            {order.transactions.length > 0 && (
                                <TransactionInfo
                                    transactions={order.transactions}
                                />
                            )}
                        </div>
                    ) : (
                        <div className="p-12 text-center bg-white rounded-xl border border-gray-200 shadow-sm">
                            <div className="mb-4 text-gray-400">
                                <svg
                                    className="mx-auto w-16 h-16"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={1}
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                </svg>
                            </div>
                            <h3 className="mb-2 text-lg font-medium text-gray-900">
                                {translate(
                                    "order.not_found",
                                    "Order not found",
                                )}
                            </h3>
                            <p className="text-gray-600">
                                {translate(
                                    "order.not_found_description",
                                    "The order you're looking for doesn't exist or you don't have permission to view it.",
                                )}
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}

OrderDetail.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="orders"
            />
        </AppLayout>
    );
};

export default OrderDetail;
