import { useEffect } from "react";
import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import React from "react";
import InvalidSupportRequest from "@/components/support/detail/InvalidSupportRequest";
import useSupportRequestDetail from "@/hooks/useSupportRequestDetail";
import Spinner from "@/components/ui/Spinner";
import Header from "@/components/support/detail/Header";
import NewResponse from "@/components/support/detail/NewResponse";
import ChatWindow from "@/components/support/detail/ChatWindow";

function Show({ ticketId }) {
    const { translate } = useSSRTranslations();
    const {
        fetchSupportTicketDetails,
        processing,
        supportRequest,
        refreshMessage,
    } = useSupportRequestDetail(ticketId);

    useEffect(() => {
        fetchSupportTicketDetails();

        const refreshInterval = setInterval(() => {
            refreshMessage();
        }, 1000 * 60);

        return () => {
            clearInterval(refreshInterval);
        };
    }, []);

    if (processing) {
        return (
            <div className="flex justify-center items-center h-64">
                <Spinner size="lg" color="primary" />
                <span className="m-6">{translate("common.loading")}</span>
            </div>
        );
    }

    if (!supportRequest) {
        return <InvalidSupportRequest />;
    }

    return (
        <>
            <Head
                title={`${translate("support.request", "Support Request")} #${supportRequest.sr_no}`}
            />

            <div className="py-3">
                <div className="container mx-auto px-4">
                    <Header supportRequest={supportRequest} />

                    <div className="bg-white rounded-lg  overflow-hidden mb-6 border border-gray-200">
                        <div className="p-6">
                            <ChatWindow />
                        </div>

                        {supportRequest.status.value !== "closed" && (
                            <NewResponse requestTicketId={ticketId} />
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}

Show.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout auth={page.props.auth} activeMenu="support">
                {page}
            </MyAccountLayout>
        </AppLayout>
    );
};

export default Show;
