// resources/js/Pages/MyAccount/Support/Index.jsx
import { useEffect, useState } from "react";
import { Head } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import SupportRequestFilters from "@/components/support/SupportRequestFilters";
import SupportRequestCard from "@/components/support/SupportRequestCard";
import SupportRequestModal from "@/components/support/SupportRequestModal";
import useSupportRequests from "@/hooks/useSupportRequests";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import Header from "@/components/support/Header";
import NoTickets from "@/components/support/NoTickets";
import React from "react";
import Spinner from "@/components/ui/Spinner";

function Index() {
    const { translate } = useSSRTranslations();
    const {
        supportRequests,
        isLoading,
        fetchSupportTickets,
        nextPageUrl,
        loadMoreSupportRequests,
        fetchOptions,
    } = useSupportRequests();

    const [showCreateModal, setShowCreateModal] = useState(false);

    useEffect(() => {
        fetchOptions();
        fetchSupportTickets();
    }, []);

    const [infiniteRef] = useInfiniteScroll({
        loading: isLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreSupportRequests,
        rootMargin: "100px",
    });

    const openCreateModal = () => {
        setShowCreateModal(true);
    };

    const closeCreateModal = () => {
        setShowCreateModal(false);
    };

    const handleSupportRequestCreated = () => {
        setShowCreateModal(false);
        fetchSupportTickets();
    };

    return (
        <>
            <Head title={translate("support.title", "My Support Requests")} />

            <div className="py-6 bg-gray-50">
                <div className="container mx-auto px-4 max-w-6xl">
                    <Header openCreateModal={openCreateModal} />

                    <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
                        <SupportRequestFilters />
                    </div>

                    {supportRequests?.length === 0 && !isLoading ? (
                        <NoTickets openCreateModal={openCreateModal} />
                    ) : (
                        <div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {supportRequests.map((request) => (
                                    <SupportRequestCard
                                        key={request.id}
                                        supportRequest={request}
                                    />
                                ))}
                            </div>

                            {/* Load more for Infinite Scroll */}
                            {isLoading && (
                                <div
                                    ref={infiniteRef}
                                    className="flex justify-center py-6"
                                >
                                    <div className="relative">
                                        <Spinner size="lg" color="primary" />
                                        <span className="sr-only">
                                            {translate(
                                                "common.loading",
                                                "Loading...",
                                            )}
                                        </span>
                                    </div>
                                </div>
                            )}

                            {/* No more requests */}
                            {!isLoading &&
                                supportRequests.length > 0 &&
                                !nextPageUrl && (
                                    <div className="text-center py-8 text-gray-500 text-sm">
                                        {translate(
                                            "support.end_of_list",
                                            "End of the list - No more requests to load",
                                        )}
                                    </div>
                                )}
                        </div>
                    )}
                </div>
            </div>

            <SupportRequestModal
                isOpen={showCreateModal}
                onClose={closeCreateModal}
                onSaved={handleSupportRequestCreated}
            />
        </>
    );
}

Index.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout auth={page.props.auth} activeMenu="support">
                {page}
            </MyAccountLayout>
        </AppLayout>
    );
};

export default Index;
