import { useEffect } from "react";
import { Head, Link, usePage } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useWalletTransactionDetail from "@/hooks/useWalletTransactionDetail";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import TransactionSummary from "@/components/withdrawals/transactions/detail/TransactionSummary";
import OrderSummary from "@/components/withdrawals/transactions/detail/OrderSummary";

import { ChevronLeft, AlertTriangle } from "lucide-react";

function WalletTransaction() {
    const { transactionNo } = usePage().props;
    const { translate } = useSSRTranslations();

    const { transaction, isLoading, fetchWalletTransactionDetail } =
        useWalletTransactionDetail();

    useEffect(() => {
        fetchWalletTransactionDetail(transactionNo);
    }, []);

    if (isLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head
                title={translate(
                    "my_withdrawals.transaction_detail_page_title",
                )}
            />
            <div className="container px-1 md:px-3 py-3 mx-auto">
                {transaction ? (
                    <div className="w-full">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between border-b pb-3">
                            <h2 className="text-md sm:text-xl font-bold mb-2">
                                {translate("my_withdrawals.labels.transaction")}{" "}
                                : #{transaction.transaction_no}
                            </h2>
                            <Link
                                className="btn btn-sm btn-neutral btn-outline px-1 sm:px-3 mb-2"
                                href={route("my-account.withdrawals")}
                            >
                                <ChevronLeft className="w-5 h-5" />
                                {translate(
                                    "my_withdrawals.back_to_transactions_btn",
                                )}
                            </Link>
                        </div>

                        <TransactionSummary transaction={transaction} />

                        {transaction.order && (
                            <OrderSummary order={transaction.order} />
                        )}
                    </div>
                ) : (
                    <div className="w-full bg-base-100 p-5">
                        <div className="flex items-center justify-center py-12">
                            <div className="text-center">
                                <div className="flex justify-center mb-6">
                                    <AlertTriangle className="w-16 h-16 text-yellow-500 animate-pulse" />
                                </div>
                                <h1 className="text-3xl font-extrabold text-gray-800 mb-4">
                                    {translate(
                                        "my_withdrawals.transaction_detail_page_title",
                                    )}
                                </h1>
                                <p className="text-xl text-gray-600 mb-6">
                                    {translate(
                                        "my_withdrawals.transaction_not_found",
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}

WalletTransaction.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="withdrawals"
            />
        </AppLayout>
    );
};

export default WalletTransaction;
