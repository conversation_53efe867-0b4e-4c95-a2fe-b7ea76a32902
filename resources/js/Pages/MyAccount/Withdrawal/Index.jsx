import { useState } from "react";
import { Head } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import WithdrawalRequests from "@/components/withdrawals/requests/WithdrawalRequests";
import WalletTransactions from "@/components/withdrawals/transactions/WalletTransactions";

function Index() {
    const { translate } = useSSRTranslations();

    const withdrawalsTabs = [
        {
            id: "withdrawals",
            title: translate("my_withdrawals.withdarwal_tab_title"),
            content: <WithdrawalRequests />,
        },
        {
            id: "wallet_transactions",
            title: translate("my_withdrawals.wallet_transactions_tab_title"),
            content: <WalletTransactions />,
        },
    ];

    const [activeTab, setActiveTab] = useState(withdrawalsTabs[0].id);

    return (
        <>
            <Head title={translate("my_withdrawals.head_title")} />

            <div className="container px-1 md:px-3 py-3 mx-auto">
                <div className="flex justify-between items-center border-b pb-5 mb-6">
                    <h1 className="text-2xl font-bold">
                        {translate("my_withdrawals.page_title")}
                    </h1>
                </div>

                <div
                    role="tablist"
                    className="tabs tabs-bordered mb-5 overflow-x-auto whitespace-nowrap"
                >
                    {withdrawalsTabs.map((tab) => (
                        <button
                            key={tab.id}
                            role="tab"
                            className={`tab text-base ${activeTab === tab.id ? "tab-active" : ""}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            {tab.title}
                        </button>
                    ))}
                </div>

                {/* Content */}
                <div className="rounded-md">
                    {withdrawalsTabs.map((tab) =>
                        activeTab === tab.id ? (
                            <div key={tab.id}>{tab.content}</div>
                        ) : null,
                    )}
                </div>
            </div>
        </>
    );
}

Index.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="withdrawals"
            />
        </AppLayout>
    );
};

export default Index;
