import { useEffect, useState } from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import SalesOrderFilters from "@/components/sales/SalesOrderFilters";
import SalesOrderCard from "@/components/sales/SalesOrderCard";
import useMySales from "@/hooks/useMySales";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";

function Index() {
    const { translate } = useSSRTranslations();

    const {
        orders,
        isLoading,
        filterChanged,
        getMySalesOrders,
        nextPageUrl,
        loadMoreSalesOrders,
    } = useMySales();

    useEffect(() => {
        getMySalesOrders();
    }, []);

    const [observerRef] = useInfiniteScroll({
        loading: isLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreSalesOrders,
        rootMargin: "100px",
    });

    return (
        <>
            <Head title={translate("my_sales.head_title")} />

            <div className="container px-1 md:px-3 py-3 mx-auto">
                <div className="flex justify-between items-center border-b pb-5 mb-6">
                    <h1 className="text-2xl font-bold">
                        {translate("my_sales.page_title")}
                    </h1>
                </div>

                <SalesOrderFilters />

                <div className="space-y-4 max-h-[70vh] overflow-y-auto my-5">
                    {!isLoading && orders.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                            <h2 className="text-2xl font-semibold text-base-content">
                                {translate("my_sales.no_sales")}
                            </h2>
                            <p className="text-gray-500 mt-2 max-w-md text-center">
                                {translate("my_sales.no_sales_details")}
                            </p>
                        </div>
                    ) : (
                        <>
                            {filterChanged ? (
                                <p className="flex items-center justify-center h-64">
                                    <span className="loading loading-bars loading-xl"></span>
                                </p>
                            ) : (
                                <>
                                    {orders.map((order) => (
                                        <SalesOrderCard
                                            key={order.id}
                                            order={order}
                                        />
                                    ))}

                                    {nextPageUrl && (
                                        <div
                                            ref={observerRef}
                                            className="h-10"
                                        ></div>
                                    )}
                                </>
                            )}

                            {isLoading && !filterChanged && (
                                <p className="flex items-center justify-center h-64">
                                    <span className="loading loading-bars loading-xl"></span>
                                </p>
                            )}
                        </>
                    )}
                </div>
            </div>
        </>
    );
}

Index.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="sales"
            />
        </AppLayout>
    );
};

export default Index;
