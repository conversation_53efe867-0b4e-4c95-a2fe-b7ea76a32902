import { useEffect } from "react";
import { Head, Link, usePage } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import useMySalesDetail from "@/hooks/useMySalesDetail";
import ManageOrderAction from "@/components/sales/detail/ManageOrderAction";
import OrderSummary from "@/components/sales/detail/OrderSummary";
import EventInfo from "@/components/sales/detail/EventInfo";
import TicketInfo from "@/components/sales/detail/TicketInfo";
import BuyerInfo from "@/components/sales/detail/BuyerInfo";
import AttendeesInfo from "@/components/sales/detail/AttendeesInfo";
import RestrictionsInfo from "@/components/sales/detail/RestrictionsInfo";
import { ChevronLeft, AlertTriangle } from "lucide-react";

function Detail() {
    const { orderNo } = usePage().props;
    const { translate } = useSSRTranslations();

    const { order, isLoading, fetchMySalesOrderDetail } = useMySalesDetail();

    useEffect(() => {
        fetchMySalesOrderDetail(orderNo);
    }, []);

    if (isLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head
                title={translate(
                    "my_sales.detail_head_title",
                    "My Sales order",
                )}
            />
            <div className="container px-1 md:px-3 py-3 mx-auto">
                {order ? (
                    <div className="w-full">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between border-b pb-3">
                            <h2 className="text-md sm:text-xl font-bold mb-2">
                                {translate(
                                    "my_sales.detail_page_title",
                                    "Sales Order",
                                )}
                                : #{order.order_no}
                            </h2>
                            <Link
                                className="btn btn-sm btn-neutral btn-outline px-1 sm:px-3 mb-2"
                                href={route("my-account.sales")}
                            >
                                <ChevronLeft className="w-5 h-5" />
                                {translate(
                                    "my_sales.back_btn",
                                    "Back to My Sales",
                                )}
                            </Link>
                        </div>

                        <ManageOrderAction order={order} />

                        <OrderSummary order={order} />

                        {order.event && <EventInfo event={order.event} />}
                        {order.ticket && <TicketInfo ticket={order.ticket} />}

                        {order.buyer && <BuyerInfo buyer={order.buyer} />}

                        {order.attendees.length > 0 && (
                            <AttendeesInfo attendees={order.attendees} />
                        )}

                        {(order.event.restrictions.length > 0 ||
                            order.ticket.restrictions.length > 0) && (
                            <RestrictionsInfo
                                eventRestrictions={order.event.restrictions}
                                ticketRestrictions={order.ticket.restrictions}
                            />
                        )}
                    </div>
                ) : (
                    <div className="w-full bg-base-100 rounded-xl shadow p-5">
                        <div className="flex items-center justify-center py-12">
                            <div className="text-center">
                                <div className="flex justify-center mb-6">
                                    <AlertTriangle className="w-16 h-16 text-yellow-500 animate-pulse" />
                                </div>
                                <h1 className="text-3xl font-extrabold text-gray-800 mb-4">
                                    {translate(
                                        "my_sales.detail_head_title",
                                        "My Sales Order",
                                    )}
                                </h1>
                                <p className="text-xl text-gray-600 mb-6">
                                    {translate(
                                        "my_sales.sales_order_not_found",
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}

Detail.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="sales"
            />
        </AppLayout>
    );
};

export default Detail;
