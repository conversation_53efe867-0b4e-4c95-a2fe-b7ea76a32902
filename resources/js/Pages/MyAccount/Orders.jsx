import React, { useEffect } from "react";
import { Head } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import useOrders from "@/hooks/useOrders";
import OrderCard from "@/components/orders/OrderCard";
import OrderFilters from "@/components/orders/OrderFilters";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import Spinner from "@/components/ui/Spinner";

function Orders() {
    const { translate } = useSSRTranslations();

    const {
        orders,
        isLoading,
        fetchOrders,
        filters,
        nextPageUrl,
        loadMoreOrders,
    } = useOrders();

    useEffect(() => {
        fetchOrders();
    }, []);

    const [infiniteRef] = useInfiniteScroll({
        loading: isLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreOrders,
        rootMargin: "100px",
    });

    return (
        <>
            <Head title={translate("order.title", "My Orders")} />

            <div className="py-3">
                <div className="container px-4 mx-auto">
                    <h1 className="text-2xl font-bold border-b pb-5 mb-6">
                        {translate("order.title", "My Orders")}
                    </h1>

                    {/* Search and Filters */}
                    <OrderFilters filters={filters} loading={isLoading} />

                    {/* Orders list */}
                    {orders.length === 0 && !isLoading ? (
                        <div className="p-6 mt-4 text-center bg-gray-50 rounded-lg border">
                            <p>
                                {translate(
                                    "order.no_orders",
                                    "You don't have any orders yet.",
                                )}
                            </p>
                        </div>
                    ) : (
                        <div className="space-y-4 rounded-lg mt-4">
                            {orders.map((order, index) => {
                                const isLastElement =
                                    index === orders.length - 1;
                                return (
                                    <OrderCard
                                        key={order.id}
                                        order={order}
                                        ref={isLastElement ? infiniteRef : null}
                                    />
                                );
                            })}

                            {/* Loading indicator */}
                            {isLoading && (
                                <div className="py-4 text-center">
                                    <Spinner size="md" color="primary" />
                                    <span className="sr-only">
                                        {translate(
                                            "common.loading",
                                            "Loading...",
                                        )}
                                    </span>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}

Orders.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="orders"
            />
        </AppLayout>
    );
};

export default Orders;
