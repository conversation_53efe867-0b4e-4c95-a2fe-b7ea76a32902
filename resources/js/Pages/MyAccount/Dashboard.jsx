import React, { useEffect, useRef } from "react";
import { Head } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import SelectInput from "@/components/forms/SelectInput";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";
import { useDebounce } from "@uidotdev/usehooks";
import useMyDashboardStats from "@/hooks/useMyDashboardStats";

function Dashboard(props) {
    const { auth, activeMenu, children } = props;
    const { translate } = useSSRTranslations();

    const {
        stats,
        isLoading,
        getMyDashboardStats,
        dateRangeFilters,
        filters,
        updateFilter,
    } = useMyDashboardStats();

    useEffect(() => {
        getMyDashboardStats();
    }, []);

    const debouncedFilters = useDebounce(filters, 500);
    const isFirstRender = useRef(true);

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        getMyDashboardStats();
    }, [debouncedFilters]);

    const filterOptions = prepareOptionsFromEnum(dateRangeFilters);
    return (
        <>
            <Head
                title={`${translate("common.dashboard.head_title", "Dashboard")}`}
            />
            <div className="container px-1 md:px-3 py-3 mx-auto">
                <h2 className="mb-4 text-2xl font-semibold border-b pb-5">
                    {translate("common.dashboard.page_title", "Dashboard")}
                </h2>
                <p className="mb-4">
                    {translate(
                        "common.dashboard.welcome_message",
                        "Welcome to your account dashboard. Get a quick snapshot of your account activity, stats, and recent updates.",
                    )}
                </p>

                <div className="flex justify-end mb-5">
                    <SelectInput
                        wrapperClass="w-48"
                        options={filterOptions}
                        value={filterOptions.find(
                            (opt) => opt.value === filters.period,
                        )}
                        onChange={(option) =>
                            updateFilter("period", option?.value || "")
                        }
                        placeholder="Filter by Period"
                    />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <div className="bg-purple-600 text-white shadow rounded-lg p-5 flex flex-col justify-center">
                        <h3 className="text-lg font-semibold">
                            {translate(
                                "common.dashboard.stats.purchase_orders",
                                "Purchased Orders",
                            )}
                        </h3>
                        <p className="mt-2 text-3xl font-bold">
                            {stats?.total_orders ?? 0}
                        </p>
                    </div>

                    <div className="bg-blue-500 text-white shadow rounded-lg p-5 flex flex-col justify-center">
                        <h3 className="text-lg font-semibold">
                            {translate(
                                "common.dashboard.stats.sales_orders",
                                "Sales Orders",
                            )}
                        </h3>
                        <p className="mt-2 text-3xl font-bold">
                            {stats?.total_sales_orders ?? 0}
                        </p>
                    </div>

                    <div className="bg-indigo-500 text-white shadow rounded-lg p-5 flex flex-col justify-center">
                        <h3 className="text-lg font-semibold">
                            {translate(
                                "common.dashboard.stats.tickets_added",
                                "Tickets Added",
                            )}
                        </h3>
                        <p className="mt-2 text-3xl font-bold">
                            {stats?.total_tickets_added ?? 0}
                        </p>
                    </div>

                    <div className="bg-green-500 text-white shadow rounded-lg p-5 flex flex-col justify-center">
                        <h3 className="text-lg font-semibold">
                            {translate(
                                "common.dashboard.stats.tickets_sold",
                                "Tickets Sold",
                            )}
                        </h3>
                        <p className="mt-2 text-3xl font-bold">
                            {stats?.total_tickets_sold ?? 0}
                        </p>
                    </div>
                    <div className="bg-teal-500 text-white shadow rounded-lg p-5 flex flex-col justify-center">
                        <h3 className="text-lg font-semibold">
                            {translate(
                                "common.dashboard.stats.wallet_balance",
                                "Current Wallet Balance",
                            )}
                        </h3>
                        <p className="mt-2 text-3xl font-bold">
                            €{stats?.current_balance ?? 0.0}
                        </p>
                    </div>

                    <div className="bg-yellow-500 text-white shadow rounded-lg p-5 flex flex-col justify-center">
                        <h3 className="text-lg font-semibold">
                            {translate(
                                "common.dashboard.stats.withdrawal_requests",
                                "Withdrawal Requests",
                            )}
                        </h3>
                        <p className="mt-2 text-3xl font-bold">
                            {stats?.withdrawal_requests ?? 0}
                        </p>
                    </div>
                    <div className="bg-red-500 text-white shadow rounded-lg p-5 flex flex-col justify-center">
                        <h3 className="text-lg font-semibold">
                            {translate(
                                "common.dashboard.stats.support_requests",
                                "Support Requests",
                            )}
                        </h3>
                        <p className="mt-2 text-3xl font-bold">
                            {stats?.support_requests ?? 0}
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}

Dashboard.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="dashboard"
            />
        </AppLayout>
    );
};

export default Dashboard;
