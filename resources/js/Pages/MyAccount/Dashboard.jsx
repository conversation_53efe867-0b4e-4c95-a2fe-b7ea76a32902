import React from "react";
import { Head } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";

function Dashboard(props) {
    const { auth, activeMenu, children } = props;
    const { translate } = useSSRTranslations();

    return (
        <>
            <Head
                title={`${translate("common.menu.my_account.dashboard", "Dashboard")} - ${auth.user.name}`}
            />
            <div className="py-3">
                <div className="text-gray-900">
                    <h2 className="mb-4 text-xl font-semibold">
                        {translate("my_account.dashboard.title", "Dashboard")}
                    </h2>
                    <p className="mb-4">
                        {translate(
                            "my_account.dashboard.welcome_message",
                            "Welcome to your account dashboard. Use the sidebar to navigate to different sections of your account.",
                        )}
                    </p>

                    <div className="grid grid-cols-1 gap-4 mt-6 md:grid-cols-2">
                        <div className="p-4 bg-gray-50 rounded-lg border">
                            <h3 className="mb-2 text-lg font-medium">
                                {translate(
                                    "my_account.dashboard.recent_orders",
                                    "Recent Orders",
                                )}
                            </h3>
                            <p className="text-sm text-gray-600">
                                {translate(
                                    "my_account.dashboard.no_recent_orders",
                                    "You don't have any recent orders.",
                                )}
                            </p>
                        </div>

                        <div className="p-4 bg-gray-50 rounded-lg border">
                            <h3 className="mb-2 text-lg font-medium">
                                {translate(
                                    "my_account.dashboard.upcoming_events",
                                    "Upcoming Events",
                                )}
                            </h3>
                            <p className="text-sm text-gray-600">
                                {translate(
                                    "my_account.dashboard.no_upcoming_events",
                                    "You don't have any upcoming events.",
                                )}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

Dashboard.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="dashboard"
            />
        </AppLayout>
    );
};

export default Dashboard;
