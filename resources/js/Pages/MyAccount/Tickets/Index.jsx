import { useEffect, useState } from "react";
import { Head, Link } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import TicketFilters from "@/components/tickets/TicketFilters";
import TicketCard from "@/components/tickets/TicketCard";
import TicketDeleteModal from "@/components/tickets/TicketDeleteModal";
import useMyTickets from "@/hooks/useMyTickets";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";

function Index() {
    const { translate } = useSSRTranslations();

    const [selectedTicket, setSelectedTicket] = useState(null);

    const {
        tickets,
        isLoading,
        filterChanged,
        getMyTickets,
        nextPageUrl,
        loadMoreTickets,
    } = useMyTickets();

    useEffect(() => {
        getMyTickets();
    }, []);

    const [observerRef] = useInfiniteScroll({
        loading: isLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreTickets,
        rootMargin: "100px",
    });

    const handleDeleteClick = (ticket) => {
        setSelectedTicket(ticket);
    };

    const closeDeleteModal = () => {
        setSelectedTicket(null);
    };

    return (
        <>
            <Head title={translate("my_tickets.head_title")} />

            <div className="container px-1 md:px-3 py-3 mx-auto">
                <div className="flex justify-between items-center border-b pb-5 mb-6">
                    <h1 className="text-2xl font-bold">
                        {translate("my_tickets.page_title")}
                    </h1>
                    <Link
                        className="btn btn-sm btn-primary"
                        href={route("selltickets")}
                    >
                        {translate("my_tickets.sell_tickets_btn")}
                    </Link>
                </div>

                <TicketFilters />

                <div className="space-y-4 max-h-[70vh] overflow-y-auto my-5">
                    {!isLoading && tickets.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                            <h2 className="text-2xl font-semibold text-base-content">
                                {translate("my_tickets.no_tickets")}
                            </h2>
                            <p className="text-gray-500 mt-2 max-w-md text-center">
                                {translate("my_tickets.no_tickets_details")}
                            </p>
                        </div>
                    ) : (
                        <>
                            {filterChanged ? (
                                <p className="flex items-center justify-center h-64">
                                    <span className="loading loading-bars loading-xl"></span>
                                </p>
                            ) : (
                                <>
                                    {tickets.map((ticket) => (
                                        <TicketCard
                                            key={ticket.id}
                                            ticket={ticket}
                                            onDeleteClick={handleDeleteClick}
                                        />
                                    ))}

                                    {nextPageUrl && (
                                        <div
                                            ref={observerRef}
                                            className="h-10"
                                        ></div>
                                    )}
                                </>
                            )}

                            {isLoading && !filterChanged && (
                                <p className="flex items-center justify-center h-64">
                                    <span className="loading loading-bars loading-xl"></span>
                                </p>
                            )}
                        </>
                    )}
                </div>
            </div>
            <TicketDeleteModal
                open={!!selectedTicket}
                onClose={closeDeleteModal}
                ticket={selectedTicket}
            />
        </>
    );
}

Index.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="tickets"
            />
        </AppLayout>
    );
};

export default Index;
