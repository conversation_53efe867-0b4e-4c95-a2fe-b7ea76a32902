import { Head, usePage, <PERSON> } from "@inertiajs/react";
import { useEffect } from "react";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import TicketForm from "@/components/tickets/edit/TicketForm";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useEditTicket from "@/hooks/useEditTicket";
import { AlertTriangle, ChevronLeft } from "lucide-react";

function Edit() {
    const { ticketNo } = usePage().props;
    const { translate } = useSSRTranslations();

    const { ticket, ticketLoading, getTicketDetail } = useEditTicket();

    useEffect(() => {
        getTicketDetail(ticketNo);
    }, []);

    if (ticketLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head title={translate("sell.edit_head_title")} />
            <div className="container px-4 py-3 mx-auto">
                {ticket ? (
                    <div className="w-full">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between border-b pb-3">
                            <h2 className="text-md sm:text-xl font-bold mb-2">
                                {translate("sell.edit_text")}: #
                                {ticket.ticket_no}
                            </h2>
                            <Link
                                className="btn btn-sm btn-neutral btn-outline mb-2"
                                href={route("my-account.tickets")}
                            >
                                <ChevronLeft className="w-5 h-5" />
                                {translate("sell.back_to_tickets_btn")}
                            </Link>
                        </div>
                        <p className="my-2">
                            <b>{translate("sell.labels.event")}:</b>{" "}
                            {ticket.event.name}
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-2 my-2">
                            <p>
                                <b>{translate("sell.labels.sector_id")}:</b>{" "}
                                {ticket.sector.name}
                            </p>
                            <p>
                                <b>
                                    {translate("sell.labels.total_quantity")}:
                                </b>{" "}
                                {ticket.quantity}
                            </p>
                            <p>
                                <b>
                                    {translate(
                                        "sell.labels.available_quantity",
                                    )}
                                    :
                                </b>{" "}
                                {ticket.quantity -
                                    (ticket.reservations_sum_quantity ?? 0)}
                            </p>
                            <p>
                                <b>
                                    {translate("sell.labels.reserved_quantity")}
                                    :
                                </b>{" "}
                                {ticket.reservations_sum_quantity ?? 0}
                            </p>
                        </div>
                        <TicketForm />
                    </div>
                ) : (
                    <div className="w-full bg-base-100 rounded-xl shadow p-5">
                        <div className="flex items-center justify-center py-12">
                            <div className="text-center">
                                <div className="flex justify-center mb-6">
                                    <AlertTriangle className="w-16 h-16 text-yellow-500 animate-pulse" />
                                </div>
                                <h1 className="text-3xl font-extrabold text-gray-800 mb-4">
                                    {translate("sell.edit_head_title")}
                                </h1>
                                <p className="text-xl text-gray-600 mb-6">
                                    {translate("sell.ticket_not_found")}
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}

Edit.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="tickets"
            />
        </AppLayout>
    );
};

export default Edit;
