import PrimaryButton from "@/components/buttons/PrimaryButton";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import { Transition } from "@headlessui/react";
import { Link, useForm, usePage } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import toast from "react-hot-toast";

export default function UpdateProfileInformation({
    mustVerifyEmail,
    status,
    countries,
    genders,
}) {
    const user = usePage().props.auth.user;
    const { translate } = useSSRTranslations();

    const { data, setData, patch, errors, processing, recentlySuccessful } =
        useForm({
            name: user.name || "",
            user_name: user.user_name || "",
            email: user.email || "",
            surname: user.user_detail?.surname || "",
            phone: user.user_detail?.phone || "",
            gender: user.user_detail?.gender || "",
            address: user.user_detail?.address || "",
            city: user.user_detail?.city || "",
            country_id: parseInt(user.user_detail?.country_id) || "",
            zip: user.user_detail?.zip || "",
            company: user.user_detail?.company || "",
            government_id: user.user_detail?.government_id || "",
        });

    const genderOptions = Object.entries(genders).map(([key, value]) => ({
        value: key,
        label: value,
    }));

    const countryOptions = countries.map((country) => ({
        value: country.id,
        label: country.translation.name,
    }));

    const submit = (e) => {
        e.preventDefault();

        patch(route("profile.update"), {
            preserveScroll: true,
            onError: (errors) => {
                toast.error(Object.values(errors)[0]);
            },
            onSuccess: () => {
                toast.success(translate("common.saved_text"));
            },
        });
    };

    return (
        <section>
            <form onSubmit={submit} className="mt-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <TextInput
                        id="name"
                        name="name"
                        value={data.name}
                        placeholder={translate("common.placeholder.name")}
                        label={translate("common.labels.name")}
                        isFocused={true}
                        datarequired="true"
                        onChange={(e) => setData("name", e.target.value)}
                        error={errors.name}
                    />

                    <TextInput
                        id="surname"
                        surname="surname"
                        value={data.surname}
                        placeholder={translate("common.placeholder.surname")}
                        label={translate("common.labels.surname")}
                        datarequired="true"
                        onChange={(e) => setData("surname", e.target.value)}
                        error={errors.surname}
                    />

                    <TextInput
                        id="user_name"
                        name="user_name"
                        value={data.user_name}
                        placeholder={translate("common.placeholder.user_name")}
                        label={translate("common.labels.user_name")}
                        datarequired="true"
                        onChange={(e) => setData("user_name", e.target.value)}
                        error={errors.user_name}
                    />
                    <TextInput
                        id="email"
                        type="email"
                        name="email"
                        value={data.email}
                        autoComplete="username"
                        placeholder={translate("common.placeholder.email")}
                        label={translate("common.labels.email")}
                        datarequired="true"
                        onChange={(e) => setData("email", e.target.value)}
                        error={errors.email}
                    />
                    <TextInput
                        id="phone"
                        name="phone"
                        type="tel"
                        value={data.phone}
                        placeholder={translate("common.placeholder.phone")}
                        label={translate("common.labels.phone")}
                        datarequired="true"
                        maxLength="15"
                        onChange={(e) => setData("phone", e.target.value)}
                        error={errors.phone}
                    />
                    <SelectInput
                        id="gender"
                        name="gender"
                        placeholder={translate("common.placeholder.gender")}
                        label={translate("common.labels.gender")}
                        datarequired="true"
                        options={genderOptions}
                        value={genderOptions.find(
                            (option) => option.value === data.gender,
                        )}
                        onChange={(selectedOption) =>
                            setData("gender", selectedOption.value)
                        }
                        error={errors.gender}
                    />
                    {user.user_type === "broker" && (
                        <TextInput
                            id="company"
                            name="company"
                            value={data.company}
                            placeholder={translate(
                                "common.placeholder.company",
                            )}
                            label={translate("common.labels.company")}
                            datarequired="true"
                            onChange={(e) => setData("company", e.target.value)}
                            error={errors.company}
                        />
                    )}
                    {user.user_type === "broker" && (
                        <TextInput
                            id="government_id"
                            name="government_id"
                            value={data.government_id}
                            placeholder={translate(
                                "common.placeholder.government_id",
                            )}
                            label={translate("common.labels.government_id")}
                            datarequired="true"
                            onChange={(e) =>
                                setData("government_id", e.target.value)
                            }
                            error={errors.government_id}
                        />
                    )}
                    <TextInput
                        id="address"
                        name="address"
                        value={data.address}
                        placeholder={translate("common.placeholder.address")}
                        label={translate("common.labels.address")}
                        datarequired="true"
                        onChange={(e) => setData("address", e.target.value)}
                        error={errors.address}
                    />
                    <TextInput
                        id="city"
                        name="city"
                        value={data.city}
                        placeholder={translate("common.placeholder.city")}
                        label={translate("common.labels.city")}
                        datarequired="true"
                        onChange={(e) => setData("city", e.target.value)}
                        error={errors.city}
                    />

                    <SelectInput
                        id="country_id"
                        name="country_id"
                        placeholder={translate("common.placeholder.country")}
                        label={translate("common.labels.country")}
                        datarequired="true"
                        options={countryOptions}
                        value={countryOptions.find(
                            (option) => option.value === data.country_id,
                        )}
                        onChange={(selectedOption) =>
                            setData("country_id", selectedOption.value)
                        }
                        error={errors.country_id}
                    />
                    <TextInput
                        id="zip"
                        name="zip"
                        value={data.zip}
                        placeholder={translate("common.placeholder.zip")}
                        label={translate("common.labels.zip")}
                        datarequired="true"
                        onChange={(e) => setData("zip", e.target.value)}
                        error={errors.zip}
                    />
                </div>

                {mustVerifyEmail && user.email_verified_at === null && (
                    <div>
                        <p className="mt-2 text-sm text-gray-800">
                            {translate("profile.email_unverified_text")}
                            <Link
                                href={route("verification.send")}
                                method="post"
                                as="button"
                                className="rounded-md text-sm text-gray-600 underline ml-2 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                {translate("profile.resend_link_text")}
                            </Link>
                        </p>

                        {status === "verification-link-sent" && (
                            <div className="mt-2 text-sm font-medium text-green-600">
                                {translate("profile.new_link_text")}
                            </div>
                        )}
                    </div>
                )}

                <div className="mt-4 flex items-center justify-end gap-4">
                    <PrimaryButton
                        disabled={processing}
                        className="btn-sm mt-2"
                    >
                        {translate("common.save_btn")}
                    </PrimaryButton>
                    <Transition
                        show={recentlySuccessful}
                        enter="transition ease-in-out"
                        enterFrom="opacity-0"
                        leave="transition ease-in-out"
                        leaveTo="opacity-0"
                    >
                        <p className="text-sm text-gray-600">
                            {translate("common.saved_text")}
                        </p>
                    </Transition>
                </div>
            </form>
        </section>
    );
}
