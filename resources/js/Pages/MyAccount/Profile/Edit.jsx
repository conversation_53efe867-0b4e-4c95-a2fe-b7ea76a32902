import { Head } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";

import UpdateProfileInformationForm from "./Partials/UpdateProfileInformationForm";

function Edit({ mustVerifyEmail, status, countries, genders }) {
    const { translate } = useSSRTranslations();

    return (
        <>
            <Head title={translate("profile.page_title", "My Profile")} />

            <div className="py-3">
                <div className="container mx-auto px-4">
                    <h1 className="text-2xl font-bold mb-5 border-b pb-6">
                        {translate("profile.page_title", "My Profile")}
                    </h1>

                    <UpdateProfileInformationForm
                        mustVerifyEmail={mustVerifyEmail}
                        status={status}
                        countries={countries}
                        genders={genders}
                    />
                </div>
            </div>
        </>
    );
}

Edit.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="profile"
            />
        </AppLayout>
    );
};

export default Edit;
