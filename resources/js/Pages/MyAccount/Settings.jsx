import React, { useState } from "react";
import { Head } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import MyAccountLayout from "@/layouts/MyAccountLayout";
import PayoutMethods from "@/components/settings/payoutmethod/PayoutMethods";
import DeleteUserForm from "@/components/settings/profile/DeleteUserForm";
import UpdatePasswordForm from "@/components/settings/profile/UpdatePasswordForm";

function Settings(props) {
    const { auth, activeMenu, children } = props;
    const { translate } = useSSRTranslations();

    const settingsTabs = [
        {
            id: "payout",
            title: translate("my_settings.payout_method_tab_title"),
            content: <PayoutMethods />,
        },
        {
            id: "update_password",
            title: translate("my_settings.update_password_tab_title"),
            content: <UpdatePasswordForm />,
        },
        {
            id: "delete_account",
            title: translate("my_settings.delete_account_tab_title"),
            content: <DeleteUserForm />,
        },
    ];

    const [activeTab, setActiveTab] = useState(settingsTabs[0].id);
    return (
        <>
            <Head title={translate("my_settings.head_title")} />
            <div className="container px-1 md:px-3 py-3 mx-auto">
                <div className="flex justify-between items-center border-b pb-5 mb-6">
                    <h1 className="text-2xl font-bold">
                        {translate("my_settings.page_title")}
                    </h1>
                </div>

                <div
                    role="tablist"
                    className="tabs tabs-bordered mb-5 overflow-x-auto whitespace-nowrap"
                >
                    {settingsTabs.map((tab) => (
                        <button
                            key={tab.id}
                            role="tab"
                            className={`tab text-base ${activeTab === tab.id ? "tab-active" : ""}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            {tab.title}
                        </button>
                    ))}
                </div>

                {/* Content */}
                <div className="rounded-md">
                    {settingsTabs.map((tab) =>
                        activeTab === tab.id ? (
                            <div key={tab.id}>{tab.content}</div>
                        ) : null,
                    )}
                </div>
            </div>
        </>
    );
}

Settings.layout = (page) => {
    return (
        <AppLayout>
            <MyAccountLayout
                children={page}
                auth={page.props.auth}
                activeMenu="settings"
            />
        </AppLayout>
    );
};

export default Settings;
