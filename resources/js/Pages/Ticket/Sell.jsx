import { Head, usePage } from "@inertiajs/react";
import { useEffect } from "react";
import { AlertTriangle } from "lucide-react";
import AppLayout from "@/layouts/AppLayout";
import EventDetailSection from "@/components/eventdetails/EventDetailSection";
import TicketFormSection from "@/components/sell/TicketFormSection";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useTicketSell from "@/hooks/useTicketSell";

function Sell() {
    const { slug } = usePage().props;
    const { translate } = useSSRTranslations();

    const {
        event,
        eventLoading,
        configurations,
        getEventDetail,
        getConfigurationsData,
    } = useTicketSell();

    useEffect(() => {
        getConfigurationsData();
        getEventDetail(slug);
    }, []);

    if (eventLoading) {
        return (
            <>
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head title={translate("sell.head_title")} />
            <div className="container mx-auto">
                <div className="flex flex-col lg:flex-row gap-8 items-start px-6 py-5">
                    <div className="w-full md:w-2/5 bg-base-100 rounded-xl shadow">
                        <EventDetailSection event={event} />
                    </div>

                    <div className="w-full md:w-3/5 bg-base-100 rounded-xl shadow p-5">
                        {configurations.can_create_ticket ? (
                            <TicketFormSection />
                        ) : (
                            <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                                <div className="flex justify-center mb-6">
                                    <AlertTriangle className="w-12 h-12" />
                                </div>
                                <h2 className="text-2xl font-semibold text-base-content">
                                    {translate("sell.limit_exceed_text")}
                                </h2>
                                <p className="text-gray-500 mt-2 max-w-md text-center">
                                    <a
                                        className="underline underline-offset-4"
                                        href="mailto:<EMAIL>"
                                    >
                                        {translate("sell.become_seller_text")}
                                    </a>
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}

Sell.layout = (page) => <AppLayout>{page}</AppLayout>;
export default Sell;
