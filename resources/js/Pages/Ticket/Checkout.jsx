import { Head, usePage } from "@inertiajs/react";
import { useEffect } from "react";
import AppLayout from "@/layouts/AppLayout";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import CheckoutTimer from "@/components/checkout/CheckoutTimer";
import TicketDetailSection from "@/components/checkout/TicketDetailSection";
import CheckoutStepsSection from "@/components/checkout/CheckoutStepsSection";
import CheckoutSessionExpired from "@/components/checkout/CheckoutSessionExpired";
import TimesUpPopup from "@/components/checkout/TimesUpPopup";
import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

function Checkout() {
    const { reservationId } = usePage().props;
    const { translate } = useSSRTranslations();

    const {
        reservation,
        reservationLoading,
        getReservation,
        clearReservation,
        showTimesUpPopup,
        handleTimesUpPopup,
        handleTimesUpPopupCloseClick,
    } = useTicketCheckout();

    useEffect(() => {
        clearReservation();
        getReservation(reservationId);
    }, []);

    if (reservationLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-2xl"></span>
                </div>
            </>
        );
    }
    const isValidReservation =
        reservation &&
        (reservation.status === "active" ||
            reservation.status === "processing");

    if (!isValidReservation) {
        return (
            <>
                <Head
                    title={translate(
                        "ticket.session_expired_title",
                        "Checkout Session Expired",
                    )}
                />
                <CheckoutSessionExpired />
            </>
        );
    }

    return (
        <>
            <Head title={translate("ticket.head_title", "Purchase Ticket")} />
            <Elements stripe={stripePromise}>
                <div className="container mx-auto">
                    <div className="sticky top-0 bg-base-100 py-2 justify-center flex z-10">
                        {dayjs
                            .utc(reservation.expires_at)
                            .local()
                            .isAfter(dayjs()) && (
                            <CheckoutTimer
                                expiryDateTime={reservation.expires_at}
                                onExpire={handleTimesUpPopup}
                            />
                        )}
                    </div>
                    <div className="flex flex-col lg:flex-row gap-8 items-start px-6 py-5">
                        <div className="w-full md:w-1/2 bg-base-100 rounded-xl shadow">
                            <TicketDetailSection />
                        </div>

                        <div className="w-full md:w-1/2 bg-base-100 rounded-xl shadow p-5">
                            <CheckoutStepsSection />
                        </div>
                    </div>
                </div>
                <TimesUpPopup
                    open={showTimesUpPopup}
                    onClose={handleTimesUpPopupCloseClick}
                />
            </Elements>
        </>
    );
}

Checkout.layout = (page) => <AppLayout>{page}</AppLayout>;
export default Checkout;
