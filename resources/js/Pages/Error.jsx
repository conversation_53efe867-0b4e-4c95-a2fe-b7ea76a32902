import { <PERSON>, <PERSON> } from "@inertiajs/react";
import AppLayout from "@/layouts/AppLayout";
import { Ghost, AlertTriangle } from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function Error({ status }) {
    const { translate } = useSSRTranslations();

    if (status === 404) {
        return (
            <>
                <Head title={translate("common.page_not_found")} />
                <div className="flex items-center justify-center py-12">
                    <div className="text-center py-16 px-6 bg-white shadow-xl rounded-2xl max-w-lg">
                        <div className="flex justify-center mb-6">
                            <Ghost className="w-16 h-16 text-primary animate-bounce" />
                        </div>
                        <h1 className="text-6xl font-extrabold text-gray-800 mb-4">
                            404
                        </h1>
                        <p className="text-xl text-gray-600 mb-6">
                            {translate("common.page_not_exist")}
                        </p>
                        <Link
                            href={route("home")}
                            className="inline-block bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200"
                        >
                            {translate("common.back_home_btn")}
                        </Link>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            <Head title={translate("common.error_title")} />
            <div className="flex items-center justify-center py-12">
                <div className="text-center px-6 py-16 bg-white shadow-xl rounded-2xl max-w-lg">
                    <div className="flex justify-center mb-6">
                        <AlertTriangle className="w-16 h-16 text-yellow-500 animate-pulse" />
                    </div>
                    <h1 className="text-4xl font-bold text-gray-800 mb-2">
                        {translate("common.something_wrong")}
                    </h1>
                    <Link
                        href={route("home")}
                        className="inline-block bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg transition duration-200"
                    >
                        {translate("common.back_home_btn")}
                    </Link>
                </div>
            </div>
        </>
    );
}

Error.layout = (page) => <AppLayout children={page} />;

export default Error;
