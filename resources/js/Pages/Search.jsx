import { Head, usePage, router } from "@inertiajs/react";
import { useEffect, useState, useCallback, useMemo } from "react";
import axios from "axios";
import debounce from "lodash/debounce";
import useInfiniteScroll from "react-infinite-scroll-hook";
import AppLayout from "@/layouts/AppLayout";
import SearchListResult from "@/components/search/SearchListResult";
import useSSRTranslations from "@/hooks/useSSRTranslations";

import {
    getRecentSearches,
    addRecentSearch,
    clearRecentSearches,
} from "@/helpers/recentSearches";
import { Search as SearchIcon } from "lucide-react";

function Search() {
    const { props } = usePage();
    const { translate } = useSSRTranslations();

    const [query, setQuery] = useState(props.q);
    const [searchTerm, setSearchTerm] = useState(props.q);
    const [results, setResults] = useState([]);
    const [page, setPage] = useState(1);
    const [hasNextPage, setHasNextPage] = useState(false);
    const [loading, setLoading] = useState(false);

    const [recentSearches, setRecentSearches] = useState([]);
    const [showRecent, setShowRecent] = useState(false);

    const debouncedUpdate = useMemo(
        () =>
            debounce((val) => {
                setSearchTerm(val);
                setPage(1);
            }, 500),
        [],
    );

    const handleInputChange = (e) => {
        const val = e.target.value;
        const url = `/search?q=${encodeURIComponent(val)}`;
        router.visit(url, {
            preserveState: true,
            replace: true,
            only: [], // no server-side props
        });
        setQuery(val);
        debouncedUpdate(val);
    };

    const fetchResults = useCallback(
        async (search, page = 1, append = false) => {
            if (!search) {
                setResults([]);
                setShowRecent(true);
                setHasNextPage(false);
                return;
            }
            setLoading(true);
            try {
                const response = await axios.get(route("api.search.index"), {
                    params: { q: search, page },
                });

                const newResults = response.data.results.data;
                const { current_page, last_page } =
                    response.data.results.pagination;

                setResults((prev) =>
                    append ? [...prev, ...newResults] : newResults,
                );
                setPage(current_page);
                setHasNextPage(current_page < last_page);
                addRecentSearch(search);
                setRecentSearches(getRecentSearches());
                setShowRecent(false);
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
            }
        },
        [],
    );

    useEffect(() => {
        fetchResults(searchTerm, 1, false);
    }, [searchTerm]);

    // Infinite scroll hook
    const [observeRef] = useInfiniteScroll({
        loading,
        hasNextPage,
        onLoadMore: () => fetchResults(searchTerm, page + 1, true),
        rootMargin: "100px",
    });

    useEffect(() => {
        setRecentSearches(getRecentSearches());
    }, []);

    const updateSearchQuery = useCallback((e) => {
        const term = e.target.innerText;
        const url = `/search?q=${encodeURIComponent(term)}`;
        router.visit(url, {
            preserveState: true,
            replace: true,
            only: [],
        });
        setQuery(term);
        debouncedUpdate(term);
        setShowRecent(false);
    });

    return (
        <>
            <Head title="Search" />
            <div className="max-w-4xl mx-auto px-4 py-10">
                <h1 className="text-3xl font-bold mb-6 flex">
                    {translate("common.search_results_for")}{" "}
                    {searchTerm && (
                        <span className="text-gray-500 ml-2">
                            "{searchTerm}"
                        </span>
                    )}
                </h1>

                <div className="mb-6">
                    <div className="join w-full relative">
                        <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                            type="text"
                            className="pl-10 input input-bordered w-full text-gray-700"
                            placeholder={translate("common.search_placeholder")}
                            value={query}
                            onChange={handleInputChange}
                        />
                    </div>

                    {query.trim() === "" &&
                        showRecent &&
                        recentSearches.length > 0 && (
                            <div className="mt-2 bg-white border rounded shadow p-4">
                                <div className="font-medium text-gray-600 mb-2">
                                    {translate("common.recent_searches")}
                                </div>
                                <div className="flex">
                                    {recentSearches.map((term, i) => (
                                        <span
                                            key={i}
                                            className="badge badge-outline badge-lg mr-2 cursor-pointer hover:bg-blue-100 text-blue-600"
                                            onClick={updateSearchQuery}
                                        >
                                            {term}
                                        </span>
                                    ))}
                                </div>
                                <button
                                    className="mt-2 text-sm text-gray-400 hover:text-red-500"
                                    onClick={() => {
                                        clearRecentSearches();
                                        setRecentSearches([]);
                                    }}
                                >
                                    {translate("common.clear_recent_searches")}
                                </button>
                            </div>
                        )}
                </div>

                {!loading && searchTerm && results.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                        <h2 className="text-2xl font-semibold text-base-content">
                            {translate("common.no_search_results_found")}
                        </h2>
                    </div>
                ) : (
                    <>
                        <ul className="bg-white shadow rounded divide-y">
                            {results.map((result) => (
                                <SearchListResult
                                    key={`${result.type}-${result.id}`}
                                    result={result}
                                    query={query}
                                />
                            ))}
                        </ul>

                        {(hasNextPage || loading) && (
                            <div
                                ref={observeRef}
                                className="flex justify-center py-6"
                            >
                                <span className="loading loading-bars loading-md"></span>
                            </div>
                        )}
                    </>
                )}
            </div>
        </>
    );
}

Search.layout = (page) => <AppLayout children={page} />;
export default Search;
