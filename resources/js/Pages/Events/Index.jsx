import { useEffect } from "react";
import { Head } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import useSSRTranslations from "@/hooks/useSSRTranslations";

import { getFilteredQueryParams } from "@/helpers/parseUrl";
import { eventsUrlParams } from "@/constants/events";
import AppLayout from "@/layouts/AppLayout";
import EventCard from "@/components/events/EventCard";
import EventSidebar from "@/components/events/EventSidebar";
import SelectInput from "@/components/forms/SelectInput";
import useEvents from "@/hooks/useEvents";

function Events({ initialData }) {
    const { translate } = useSSRTranslations();

    const {
        events,
        filters,
        updateFilter,
        updateMultipleFilters,
        fetchOptions,
        eventLoading,
        loadMoreEvents,
        clearNextPageUrl,
        clearFilters,
        nextPageUrl,
        fetchEventsInitially,
        updatesFilterCleared,
        setInitialDataFromProps,
    } = useEvents(initialData);

    useEffect(() => {
        updatesFilterCleared(true);
        clearNextPageUrl();
        clearFilters();
        setInitialDataFromProps(initialData);
    }, []);

    useEffect(() => {
        fetchOptions();
        const urlFilters = getFilteredQueryParams(eventsUrlParams, true);

        const timer = setTimeout(() => {
            if (Object.keys(urlFilters).length > 0) {
                updateMultipleFilters(urlFilters);
            } else {
                fetchEventsInitially();
            }
        }, 50);

        return () => clearTimeout(timer);
    }, [filters]);

    const [observerRef] = useInfiniteScroll({
        loading: eventLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreEvents,
        rootMargin: "100px",
    });

    return (
        <>
            <Head title={translate("events.head_title", "Events")} />
            <div className="container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8">
                <EventSidebar />

                <main className="md:w-3/4 w-full">
                    <div className="flex justify-between items-center mb-8">
                        <h1 className="text-2xl md:text-4xl font-bold">
                            {translate("events.page_title")}
                        </h1>

                        <SelectInput
                            wrapperClass="w-1/2 md:w-1/3"
                            options={translate("events.sort_options")}
                            value={
                                filters.sort
                                    ? translate("events.sort_options").find(
                                          (option) =>
                                              option.value === filters.sort,
                                      )
                                    : null
                            }
                            onChange={(selected) =>
                                updateFilter("sort", selected.value)
                            }
                            placeholder={translate(
                                "events.sort_by_placeholder",
                            )}
                        />
                    </div>

                    <div className="h-[800px] overflow-y-auto my-5">
                        {!eventLoading && events.length === 0 ? (
                            <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                                <h2 className="text-2xl font-semibold text-base-content">
                                    {translate("events.no_events")}
                                </h2>
                                <p className="text-gray-500 mt-2 max-w-md text-center">
                                    {translate("events.no_events_details")}
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                    {events.map((event, index) => (
                                        <div
                                            key={event.id}
                                            className="hover:scale-[1.02] cursor-pointer transition-transform duration-150"
                                        >
                                            <EventCard
                                                event={event}
                                                translationFrom={translate(
                                                    "common.from",
                                                )}
                                                translationBuyTickets={translate(
                                                    "common.buy_tickets",
                                                )}
                                            />
                                        </div>
                                    ))}
                                </div>

                                {nextPageUrl && (
                                    <div
                                        ref={observerRef}
                                        className="h-10"
                                    ></div>
                                )}

                                {eventLoading && (
                                    <p className="flex items-center justify-center h-64">
                                        <span className="loading loading-bars loading-xl"></span>
                                    </p>
                                )}
                            </>
                        )}
                    </div>
                </main>
            </div>
        </>
    );
}

Events.layout = (page) => <AppLayout>{page}</AppLayout>;

export default Events;
