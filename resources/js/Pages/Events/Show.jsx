import AppLayout from "@/layouts/AppLayout";
import { Head, usePage } from "@inertiajs/react";
import { useEffect } from "react";
import EventDetailSection from "@/components/eventdetails/EventDetailSection";
import TicketFilters from "@/components/eventdetails/TicketFilters";
import TicketListingSidebar from "@/components/eventdetails/TicketListingSidebar";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useEventDetail from "@/hooks/useEventDetail";

function Show() {
    const { translate } = useSSRTranslations();
    const { slug, initialData } = usePage().props;
    const { event, getEventDetail, eventLoading, setFromSSR } =
        useEventDetail();

    useEffect(() => {
        if (initialData) {
            setFromSSR(initialData);
        } else if (slug) {
            getEventDetail(slug);
        }
    }, []);

    if (eventLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head>
                <title>{event.name}</title>
                <meta name="title" content={event.meta_title} />
                <meta name="keywords" content={event.meta_keywords} />
                <meta name="description" content={event.meta_description} />
            </Head>
            <div className="container mx-auto px-6 py-5">
                {/* Desktop Version */}
                <div className="hidden lg:block sticky top-0 z-10 mb-5">
                    <TicketFilters event={event} />
                </div>

                <div className="flex flex-col lg:flex-row gap-8 items-start">
                    {/* Event Details */}
                    <div className="lg:w-3/5 w-full bg-base-100 rounded-xl overflow-hidden shadow">
                        <EventDetailSection event={event} />

                        {event.image !== "" ? (
                            <img
                                src={event.image}
                                alt={event.image_alt || event.name}
                                className="w-full h-auto"
                            />
                        ) : (
                            <div className="bg-gray-200 w-full h-64 flex items-center justify-center">
                                <img
                                    src="/img/ticketgol-logo.png"
                                    alt={event.name}
                                />
                            </div>
                        )}

                        <div className="px-5 mb-5">
                            {/* About the Event */}

                            <div className="mt-10">
                                <h3 className="text-xl font-bold mb-2">
                                    {translate("events.about_the_event")}
                                </h3>
                                <p className="text-gray-700 leading-relaxed">
                                    {event.description}
                                </p>
                            </div>
                            {Object.entries(event.restrictions).length > 0 && (
                                <div className="mt-4 border-t pt-5">
                                    <h3 className="text-xl font-bold mb-2">
                                        {translate("events.restriction_text")}
                                    </h3>
                                    <ul className="list-disc leading-relaxed ml-5">
                                        {Object.entries(event.restrictions).map(
                                            ([id, name]) => (
                                                <li key={id}>{name}</li>
                                            ),
                                        )}
                                    </ul>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Tablet Version */}
                    <div className="block lg:hidden w-full">
                        <TicketFilters event={event} />
                    </div>

                    {/* Tickets listing with filters */}
                    <TicketListingSidebar />
                </div>
            </div>
        </>
    );
}

Show.layout = (page) => <AppLayout children={page} />;

export default Show;
