import { useEffect, useRef } from "react";
import { Head, usePage } from "@inertiajs/react";
import SellTicketEventCard from "@/components/events/SellTicketEventCard";
import useInfiniteScroll from "react-infinite-scroll-hook";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useSellTickets from "@/hooks/useSellTickets";
import AppLayout from "@/layouts/AppLayout";

import { Search } from "lucide-react";
import { useDebounce } from "@uidotdev/usehooks";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";

function SellTickets({ initialData }) {
    const { translate } = useSSRTranslations();

    const {
        events,
        eventLoading,
        getEventsList,
        nextPageUrl,
        filters,
        filterChanged,
        updateFilter,
        clearFilters,
        loadMoreEvents,
        setInitialDataFromProps,
    } = useSellTickets(initialData);

    const debouncedFilters = useDebounce(filters, 500);
    const isFirstRender = useRef(true);

    useEffect(() => {
        if (initialData) {
            setInitialDataFromProps(initialData);
            return;
        }

        getEventsList();
    }, []);

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        getEventsList();
    }, [debouncedFilters]);

    const [observerRef] = useInfiniteScroll({
        loading: eventLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreEvents,
        rootMargin: "100px",
    });

    return (
        <>
            <Head
                title={translate(
                    "events.sell_tickets_page_title",
                    "Sell Tickets",
                )}
            />
            <div className="container px-4 py-8 mx-auto">
                <div className="mx-auto max-w-4xl">
                    <div className="text-center mb-6">
                        <h1 className="text-2xl font-bold">
                            {translate("events.sell_tickets_page_title")}
                        </h1>
                        <p>
                            {translate("events.sell_tickets_page_description")}
                        </p>
                    </div>

                    <div className="bg-white p-5 shadow rounded-xl">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div className="relative w-full">
                                <Search className="absolute left-3 top-1/2 -translate-y-1/2 mt-1 text-gray-400 w-5 h-5" />
                                <TextInput
                                    value={filters.search}
                                    onChange={(e) =>
                                        updateFilter("search", e.target.value)
                                    }
                                    placeholder={translate(
                                        "events.sell_tickets_page_search_placeholder",
                                    )}
                                    className="pl-10"
                                />
                            </div>
                            <SelectInput
                                options={translate(
                                    "events.sell_tickets_sort_options",
                                )}
                                value={
                                    filters.sort
                                        ? translate(
                                              "events.sell_tickets_sort_options",
                                          ).find(
                                              (option) =>
                                                  option.value === filters.sort,
                                          )
                                        : null
                                }
                                onChange={(option) =>
                                    updateFilter("sort", option?.value || "")
                                }
                                placeholder={translate(
                                    "my_tickets.sort_by_placeholder",
                                )}
                            />
                        </div>
                        <div className="flex gap-6 justify-end mt-2">
                            <button
                                onClick={clearFilters}
                                className="btn btn-outline btn-sm"
                            >
                                {translate("common.clear", "Clear")}
                            </button>
                        </div>
                    </div>
                    <div className="space-y-4 my-5">
                        {!eventLoading && events.length === 0 ? (
                            <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                                <h2 className="text-2xl font-semibold text-base-content">
                                    {translate("events.no_events")}
                                </h2>
                                <p className="text-gray-500 mt-2 max-w-md text-center">
                                    {translate("events.no_events_details")}
                                </p>
                            </div>
                        ) : (
                            <>
                                {filterChanged ? (
                                    <p className="flex items-center justify-center h-64">
                                        <span className="loading loading-bars loading-xl"></span>
                                    </p>
                                ) : (
                                    <>
                                        {events.map((event) => (
                                            <SellTicketEventCard
                                                key={event.id}
                                                event={event}
                                                buttonTitle={translate(
                                                    "events.sell_tickets_btn",
                                                )}
                                            />
                                        ))}

                                        {nextPageUrl && (
                                            <div
                                                ref={observerRef}
                                                className="h-10"
                                            ></div>
                                        )}
                                    </>
                                )}
                                {eventLoading && !filterChanged && (
                                    <p className="flex items-center justify-center h-64">
                                        <span className="loading loading-bars loading-xl"></span>
                                    </p>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}

SellTickets.layout = (page) => <AppLayout>{page}</AppLayout>;

export default SellTickets;
