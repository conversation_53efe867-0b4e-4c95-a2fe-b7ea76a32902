import AppLayout from "@/layouts/AppLayout";
import { MapPin } from "lucide-react";
import { Head, usePage } from "@inertiajs/react";
import { useEffect } from "react";
import StadiumEventList from "@/components/stadiums/StadiumEventList";
import useStadiumDetail from "@/hooks/useStadiumDetail";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function Show() {
    const { translate } = useSSRTranslations();
    const { slug, initialData } = usePage().props;
    const { stadium, getStadiumDetail, stadiumLoading, setFromSSR } =
        useStadiumDetail();

    useEffect(() => {
        if (initialData) {
            setFromSSR(initialData);
        } else {
            getStadiumDetail(slug);
        }
    }, [initialData, slug, setFromSSR]);

    if (stadiumLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head>
                <title>{stadium.name}</title>
                <meta name="title" content={stadium.meta_title} />
                <meta name="keywords" content={stadium.meta_keywords} />
                <meta name="description" content={stadium.meta_description} />
            </Head>
            <div className="container mx-auto px-10 py-5">
                <div className="relative">
                    {stadium.image !== "" ? (
                        <img
                            src={stadium.image}
                            alt={stadium.image_alt || stadium.name}
                            className="w-full h-96 object-cover rounded-xl"
                        />
                    ) : (
                        <div className="bg-base-200 w-full h-96 flex items-center justify-center rounded-xl text-gray-500">
                            <img
                                src="/img/ticketgol-logo.png"
                                alt={stadium.name}
                                className="object-cover w-1/3"
                            />
                        </div>
                    )}
                    <div className="absolute inset-0 bg-black bg-opacity-65 flex flex-col rounded-xl justify-center items-center text-white text-center px-4">
                        <h1 className="text-xl sm:text-3xl font-bold mb-2">
                            {stadium.name}
                        </h1>
                        <div className="flex items-center text-warning font-medium">
                            <MapPin className="w-4 h-4 mr-2" />
                            <span className="text-base sm:text-lg">
                                {stadium.address_line_1},{" "}
                                {stadium.address_line_2}, {stadium.country.name}
                                , {stadium.postcode}
                            </span>
                        </div>
                        <p className="text-sm sm:text-base mt-2 max-w-xl">
                            {stadium.description}
                        </p>
                    </div>
                </div>

                <StadiumEventList />
            </div>
        </>
    );
}

Show.layout = (page) => <AppLayout children={page} />;

export default Show;
