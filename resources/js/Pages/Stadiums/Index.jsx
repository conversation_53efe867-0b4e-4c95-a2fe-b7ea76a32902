import { useEffect } from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";

import AppLayout from "@/layouts/AppLayout";
import StadiumCard from "@/components/stadiums/StadiumCard";
import StadiumSidebar from "@/components/stadiums/StadiumSidebar";
import SelectInput from "@/components/forms/SelectInput";
import useStadiums from "@/hooks/useStadiums";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function Stadiums({ initialData }) {
    const { translate } = useSSRTranslations();
    const {
        stadiums,
        filters,
        updateFilter,
        fetchOptions,
        loading,
        loadMoreStadiums,
        clearNextPageUrl,
        nextPageUrl,
        fetchStadiumsInitially,
        initializeWithSSRData,
    } = useStadiums();

    useEffect(() => {
        if (initialData) {
            // Initialize Redux state with SSR data
            initializeWithSSRData(initialData);
        } else {
            // Fallback to API calls if no SSR data
            clearNextPageUrl();
            fetchOptions();

            const timer = setTimeout(() => {
                fetchStadiumsInitially();
            }, 10);

            return () => clearTimeout(timer);
        }
    }, [initialData]);

    const [observerRef] = useInfiniteScroll({
        loading: loading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreStadiums,
        rootMargin: "100px",
    });

    return (
        <>
            <Head title={translate("stadiums.head_title", "Stadiums")} />
            <div className="container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8">
                <StadiumSidebar />
                <main className="md:w-3/4 w-full">
                    <div className="flex justify-between items-center mb-8">
                        <h1 className="text-2xl md:text-4xl font-bold">
                            {translate("stadiums.page_title", "All Stadiums")}
                        </h1>
                        <SelectInput
                            wrapperClass="w-1/2 md:w-1/3"
                            options={translate("stadiums.sort_options")}
                            value={
                                filters.sort
                                    ? translate("stadiums.sort_options").find(
                                          (option) =>
                                              option.value === filters.sort,
                                      )
                                    : null
                            }
                            onChange={(selected) =>
                                updateFilter("sort", selected.value)
                            }
                            placeholder={translate(
                                "stadiums.sort_by_placeholder",
                            )}
                        />
                    </div>

                    <div className="h-[800px] overflow-y-auto my-5">
                        {!loading && stadiums.length === 0 ? (
                            <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                                <h2 className="text-2xl font-semibold text-base-content">
                                    {translate(
                                        "stadiums.no_stadiums",
                                        "Sorry, no stadiums match your filters.",
                                    )}
                                </h2>
                                <p className="text-gray-500 mt-2 max-w-md text-center">
                                    {translate("stadiums.no_stadiums_details")}
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                    {stadiums.map((stadium, index) => (
                                        <Link
                                            key={`${stadium.id}-${index}`}
                                            href={route(
                                                "detail.show",
                                                stadium.slug,
                                            )}
                                            className="hover:scale-[1.02] transition-transform duration-150"
                                        >
                                            <StadiumCard stadium={stadium} />
                                        </Link>
                                    ))}
                                </div>

                                {nextPageUrl && (
                                    <div
                                        ref={observerRef}
                                        className="h-10"
                                    ></div>
                                )}

                                {loading && (
                                    <p className="flex items-center justify-center h-64">
                                        <span className="loading loading-bars loading-xl"></span>
                                    </p>
                                )}
                            </>
                        )}
                    </div>
                </main>
            </div>
        </>
    );
}

Stadiums.layout = (page) => <AppLayout>{page}</AppLayout>;

export default Stadiums;
