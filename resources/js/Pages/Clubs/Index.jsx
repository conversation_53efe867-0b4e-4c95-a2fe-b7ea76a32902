import { useEffect } from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";

import AppLayout from "@/layouts/AppLayout";
import ClubCard from "@/components/clubs/ClubCard";
import ClubSidebar from "@/components/clubs/ClubSidebar";
import SelectInput from "@/components/forms/SelectInput";
import useClubs from "@/hooks/useClubs";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function Clubs({ initialData }) {
    const { translate } = useSSRTranslations();
    const {
        clubs,
        filters,
        updateFilter,
        fetchOptions,
        loading,
        loadMoreClubs,
        clearNextPageUrl,
        nextPageUrl,
        fetchClubsInitially,
        initializeWithSSRData,
    } = useClubs(initialData);

    useEffect(() => {
        if (initialData) {
            // Initialize Redux state with SSR data
            initializeWithSSRData(initialData);
        } else {
            // Fallback to API calls if no SSR data
            clearNextPageUrl();
            fetchOptions();

            const timer = setTimeout(() => {
                fetchClubsInitially();
            }, 10);

            return () => clearTimeout(timer);
        }
    }, []);

    const [observerRef] = useInfiniteScroll({
        loading: loading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreClubs,
        rootMargin: "100px",
    });

    return (
        <>
            <Head title={translate("clubs.head_title", "Clubs")} />
            <div className="container mx-auto px-4 py-8 flex flex-col md:flex-row gap-8">
                <ClubSidebar />
                <main className="md:w-3/4 w-full">
                    <div className="flex justify-between items-center mb-8">
                        <h1 className="text-2xl md:text-4xl font-bold">
                            {translate("clubs.page_title", "All Clubs")}
                        </h1>
                        <SelectInput
                            wrapperClass="w-1/2 md:w-1/3"
                            options={translate("clubs.sort_options")}
                            value={
                                filters.sort
                                    ? translate("clubs.sort_options").find(
                                          (option) =>
                                              option.value === filters.sort,
                                      )
                                    : null
                            }
                            onChange={(selected) =>
                                updateFilter("sort", selected.value)
                            }
                            placeholder={translate("clubs.sort_by_placeholder")}
                        />
                    </div>

                    <div className="h-[800px] overflow-y-auto my-5">
                        {!loading && clubs.length === 0 ? (
                            <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                                <h2 className="text-2xl font-semibold text-base-content">
                                    {translate(
                                        "clubs.no_clubs",
                                        "Sorry, no clubs match your filters.",
                                    )}
                                </h2>
                                <p className="text-gray-500 mt-2 max-w-md text-center">
                                    {translate("clubs.no_clubs_details")}
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                    {clubs.map((club, index) => (
                                        <Link
                                            key={`${club.id}-${index}`}
                                            href={route(
                                                "detail.show",
                                                club.slug,
                                            )}
                                            className="hover:scale-[1.02] transition-transform duration-150"
                                        >
                                            <ClubCard club={club} />
                                        </Link>
                                    ))}
                                </div>

                                {nextPageUrl && (
                                    <div
                                        ref={observerRef}
                                        className="h-10"
                                    ></div>
                                )}

                                {loading && (
                                    <p className="flex items-center justify-center h-64">
                                        <span className="loading loading-bars loading-xl"></span>
                                    </p>
                                )}
                            </>
                        )}
                    </div>
                </main>
            </div>
        </>
    );
}

Clubs.layout = (page) => <AppLayout>{page}</AppLayout>;

export default Clubs;
