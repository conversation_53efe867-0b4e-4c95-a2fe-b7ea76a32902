import AppLayout from "@/layouts/AppLayout";
import { MapPin } from "lucide-react";
import { Head, usePage } from "@inertiajs/react";
import { useEffect, useState } from "react";
import ClubEventList from "@/components/clubs/ClubEventList";
import useClubDetail from "@/hooks/useClubDetail";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function Show() {
    const { slug, initialData } = usePage().props;
    const { translate } = useSSRTranslations();
    const { club, getClubDetail, clubLoading, setFromSSR } = useClubDetail();
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        if (initialData) {
            setFromSSR(initialData);
        } else if (slug) {
            getClubDetail(slug);
        }
    }, []);

    if (clubLoading) {
        return (
            <>
                <Head title="Loading..." />
                <div className="p-8 flex items-center justify-center h-96">
                    <span className="loading loading-bars loading-xl"></span>
                </div>
            </>
        );
    }

    return (
        <>
            <Head>
                <title>{club.name}</title>
                <meta name="title" content={club.meta_title} />
                <meta name="keywords" content={club.meta_keywords} />
                <meta name="description" content={club.meta_description} />
            </Head>
            <div className="container mx-auto px-10 py-5">
                <div className="relative">
                    {club.image !== "" ? (
                        <img
                            src={club.image}
                            alt={club.image_alt || club.name}
                            className="w-full h-96 object-cover rounded-xl"
                        />
                    ) : (
                        <div className="bg-base-200 w-full h-96 flex items-center justify-center rounded-xl text-gray-500">
                            <img
                                src="/img/ticketgol-logo.png"
                                alt={club.name}
                                className="object-cover w-1/3"
                            />
                        </div>
                    )}
                    <div className="absolute inset-0 bg-black bg-opacity-65 flex flex-col rounded-xl justify-center items-center text-white text-center px-4">
                        <h1 className="text-xl sm:text-3xl font-bold mb-2">
                            {club.name}
                        </h1>
                        <div className="flex items-center text-warning font-medium">
                            <MapPin className="w-4 h-4 mr-2" />
                            <span className="text-base sm:text-lg">
                                {club.country.name}
                            </span>
                        </div>
                        <p className="text-sm sm:text-base mt-2 max-w-xl">
                            {club.description}
                        </p>
                    </div>
                </div>
                <div className="mt-4">
                    <div
                        className={`collapse collapse-plus bg-base-100 border border-base-300 rounded-box
                            ${isOpen ? "collapse-open" : ""}`}
                        onClick={() => setIsOpen((prev) => !prev)}
                    >
                        <div className="collapse-title font-semibold flex justify-between items-center cursor-pointer">
                            <span>
                                {translate("clubs.about_text", "About the")}{" "}
                                {club.name}
                            </span>
                        </div>
                        <div className="collapse-content text-gray-700">
                            <div
                                className="prose"
                                dangerouslySetInnerHTML={{
                                    __html: club.detailed_description,
                                }}
                            />
                        </div>
                    </div>
                </div>

                <ClubEventList />
            </div>
        </>
    );
}

Show.layout = (page) => <AppLayout children={page} />;

export default Show;
