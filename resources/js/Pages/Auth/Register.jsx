import { Head, Link, useForm } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import AppLayout from "@/layouts/AppLayout";
import PrimaryButton from "@/components/buttons/PrimaryButton";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import PasswordInput from "@/components/forms/PasswordInput";

function Register({ countries, genders }) {
    const { translate } = useSSRTranslations();

    const { data, setData, post, processing, errors, reset } = useForm({
        name: "",
        surname: "",
        user_name: "",
        email: "",
        password: "",
        password_confirmation: "",
        phone: "",
        gender: "",
        address: "",
        city: "",
        country_id: "",
        zip: "",
    });

    const genderOptions = Object.entries(genders).map(([key, value]) => ({
        value: key,
        label: value,
    }));

    const countryOptions = countries.map((country) => ({
        value: country.id,
        label: country.translation.name,
    }));

    const submit = (e) => {
        e.preventDefault();

        post(route("register"), {
            onFinish: () => reset("password", "password_confirmation"),
        });
    };

    return (
        <>
            <Head title={translate("register.page_title")} />
            <div className="flex py-10 items-center justify-center">
                <div className="card w-full max-w-2xl bg-base-100 shadow-xl">
                    <div className="card-body">
                        <h5 className="card-title justify-center">
                            {translate("register.page_title")}
                        </h5>

                        <form onSubmit={submit}>
                            <div className="grid grid-cols-2 gap-4">
                                <TextInput
                                    id="name"
                                    name="name"
                                    value={data.name}
                                    placeholder={translate(
                                        "common.placeholder.name",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.name")}
                                    isFocused={true}
                                    onChange={(e) =>
                                        setData("name", e.target.value)
                                    }
                                    error={errors.name}
                                />

                                <TextInput
                                    id="surname"
                                    name="surname"
                                    value={data.surname}
                                    placeholder={translate(
                                        "common.placeholder.surname",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.surname")}
                                    onChange={(e) =>
                                        setData("surname", e.target.value)
                                    }
                                    error={errors.surname}
                                />

                                <TextInput
                                    id="user_name"
                                    name="user_name"
                                    value={data.user_name}
                                    placeholder={translate(
                                        "common.placeholder.user_name",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.user_name")}
                                    onChange={(e) =>
                                        setData("user_name", e.target.value)
                                    }
                                    error={errors.user_name}
                                />
                                <TextInput
                                    id="email"
                                    type="email"
                                    name="email"
                                    value={data.email}
                                    autoComplete="username"
                                    placeholder={translate(
                                        "common.placeholder.email",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.email")}
                                    onChange={(e) =>
                                        setData("email", e.target.value)
                                    }
                                    error={errors.email}
                                />
                                <TextInput
                                    id="phone"
                                    name="phone"
                                    type="tel"
                                    value={data.phone}
                                    placeholder={translate(
                                        "common.placeholder.phone",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.phone")}
                                    maxLength="15"
                                    onChange={(e) =>
                                        setData("phone", e.target.value)
                                    }
                                    error={errors.phone}
                                />
                                <SelectInput
                                    id="gender"
                                    name="gender"
                                    placeholder={translate(
                                        "common.placeholder.gender",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.gender")}
                                    options={genderOptions}
                                    value={genderOptions.find(
                                        (option) =>
                                            option.value === data.gender,
                                    )}
                                    onChange={(selectedOption) =>
                                        setData("gender", selectedOption.value)
                                    }
                                    error={errors.gender}
                                />
                                <TextInput
                                    id="address"
                                    name="address"
                                    value={data.address}
                                    placeholder={translate(
                                        "common.placeholder.address",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.address")}
                                    onChange={(e) =>
                                        setData("address", e.target.value)
                                    }
                                    error={errors.address}
                                />
                                <TextInput
                                    id="city"
                                    name="city"
                                    value={data.city}
                                    placeholder={translate(
                                        "common.placeholder.city",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.city")}
                                    onChange={(e) =>
                                        setData("city", e.target.value)
                                    }
                                    error={errors.city}
                                />

                                <SelectInput
                                    id="country_id"
                                    name="country_id"
                                    placeholder={translate(
                                        "common.placeholder.country",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.country")}
                                    options={countryOptions}
                                    value={countryOptions.find(
                                        (option) =>
                                            option.value === data.country_id,
                                    )}
                                    onChange={(selectedOption) =>
                                        setData(
                                            "country_id",
                                            selectedOption.value,
                                        )
                                    }
                                    error={errors.country_id}
                                />
                                <TextInput
                                    id="zip"
                                    name="zip"
                                    value={data.zip}
                                    placeholder={translate(
                                        "common.placeholder.zip",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.zip")}
                                    onChange={(e) =>
                                        setData("zip", e.target.value)
                                    }
                                    error={errors.zip}
                                />
                                <PasswordInput
                                    id="password"
                                    name="password"
                                    value={data.password}
                                    placeholder={translate(
                                        "common.placeholder.password",
                                    )}
                                    datarequired="true"
                                    label={translate("common.labels.password")}
                                    autoComplete="new-password"
                                    onChange={(e) =>
                                        setData("password", e.target.value)
                                    }
                                    error={errors.password} // Pass error message
                                />
                                <PasswordInput
                                    id="password_confirmation"
                                    name="password_confirmation"
                                    value={data.password_confirmation}
                                    placeholder={translate(
                                        "common.placeholder.password_confirmation",
                                    )}
                                    datarequired="true"
                                    label={translate(
                                        "common.labels.password_confirmation",
                                    )}
                                    autoComplete="new-password"
                                    onChange={(e) =>
                                        setData(
                                            "password_confirmation",
                                            e.target.value,
                                        )
                                    }
                                    error={errors.password_confirmation} // Pass error message
                                />
                            </div>

                            <div className="form-control mt-6 items-end">
                                <Link
                                    href={route("login")}
                                    className="rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                >
                                    {translate("common.already_registered")}
                                </Link>
                            </div>
                            <div className="form-control mt-6">
                                <PrimaryButton disabled={processing}>
                                    {translate("common.register_btn")}
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
}

Register.layout = (page) => <AppLayout children={page} />;

export default Register;
