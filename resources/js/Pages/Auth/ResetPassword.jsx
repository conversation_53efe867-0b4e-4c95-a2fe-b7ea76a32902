import { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

import AppLayout from "@/layouts/AppLayout";
import PasswordInput from "@/components/forms/PasswordInput";
import PrimaryButton from "@/components/buttons/PrimaryButton";
import TextInput from "@/components/forms/TextInput";

function ResetPassword({ token, email }) {
    const { translate } = useSSRTranslations();

    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const { data, setData, post, processing, errors, reset } = useForm({
        token: token,
        email: email,
        password: "",
        password_confirmation: "",
    });

    const submit = (e) => {
        e.preventDefault();

        post(route("password.store"), {
            onFinish: () => reset("password", "password_confirmation"),
        });
    };

    return (
        <>
            <Head title={translate("password.reset_page_title")} />
            <div className="flex py-10 items-center justify-center">
                <div className="card w-full max-w-md bg-base-100 shadow-xl">
                    <div className="card-body">
                        <h5 className="card-title justify-center">
                            {translate("password.reset_page_title")}
                        </h5>
                        <form onSubmit={submit}>
                            <div className="grid gap-4">
                                <TextInput
                                    id="email"
                                    type="email"
                                    name="email"
                                    value={data.email}
                                    autoComplete="username"
                                    onChange={(e) =>
                                        setData("email", e.target.value)
                                    }
                                    readOnly
                                    error={errors.email}
                                />

                                <PasswordInput
                                    id="password"
                                    name="password"
                                    value={data.password}
                                    placeholder={translate(
                                        "common.placeholder.password",
                                    )}
                                    autoComplete="new-password"
                                    isFocused={true}
                                    onChange={(e) =>
                                        setData("password", e.target.value)
                                    }
                                    error={errors.password}
                                />

                                <PasswordInput
                                    id="password_confirmation"
                                    name="password_confirmation"
                                    placeholder={translate(
                                        "common.placeholder.password_confirmation",
                                    )}
                                    value={data.password_confirmation}
                                    autoComplete="new-password"
                                    onChange={(e) =>
                                        setData(
                                            "password_confirmation",
                                            e.target.value,
                                        )
                                    }
                                    error={errors.password_confirmation}
                                />
                            </div>

                            <div className="mt-4 form-control">
                                <PrimaryButton disabled={processing}>
                                    {translate("password.reset_password_btn")}
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
}

ResetPassword.layout = (page) => <AppLayout children={page} />;

export default ResetPassword;
