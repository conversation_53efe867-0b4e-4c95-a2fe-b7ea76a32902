import { Head, Link, useForm } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

import AppLayout from "@/layouts/AppLayout";
import PrimaryButton from "@/components/buttons/PrimaryButton";

function VerifyEmail({ status }) {
    const { translate } = useSSRTranslations();

    const { post, processing } = useForm({});

    const submit = (e) => {
        e.preventDefault();

        post(route("verification.send"));
    };

    return (
        <>
            <Head title={translate("verification.page_title")} />
            <div className="flex py-10 items-center justify-center">
                <div className="card w-full max-w-md bg-base-100 shadow-xl">
                    <div className="card-body">
                        <h5 className="card-title justify-center">
                            {translate("verification.page_title")}
                        </h5>
                        <div className="mb-4 text-sm text-gray-600">
                            {translate("verification.page_description")}
                        </div>

                        {status === "verification-link-sent" && (
                            <div className="mb-4 text-sm font-medium text-green-600">
                                {translate("verification.link_sent_message")}
                            </div>
                        )}
                        <form onSubmit={submit}>
                            <div className="mt-4 flex items-center justify-between">
                                <PrimaryButton disabled={processing}>
                                    {translate("verification.resend_btn")}
                                </PrimaryButton>

                                <Link
                                    href={route("logout")}
                                    method="post"
                                    as="button"
                                    className="rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                >
                                    {translate("common.menu.logout")}
                                </Link>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
}

VerifyEmail.layout = (page) => <AppLayout children={page} />;

export default VerifyEmail;
