import React from "react";
import { Head, useForm } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

import AppLayout from "@/layouts/AppLayout";
import PrimaryButton from "@/components/buttons/PrimaryButton";
import TextInput from "@/components/forms/TextInput";

function ForgotPassword({ status }) {
    const { translate } = useSSRTranslations();

    const { data, setData, post, processing, errors } = useForm({
        email: "",
    });

    const submit = (e) => {
        e.preventDefault();

        post(route("password.email"));
    };

    return (
        <>
            <Head title={translate("password.forgot_page_title")} />
            <div className="flex py-10 items-center justify-center">
                <div className="card w-full max-w-md bg-base-100 shadow-xl">
                    <div className="card-body">
                        <h5 className="card-title justify-center">
                            {translate("password.forgot_page_title")}
                        </h5>
                        <div className="mb-4 text-sm text-gray-600">
                            {translate("password.forgot_page_description")}
                        </div>

                        {status && (
                            <div className="mb-4 text-sm font-medium text-green-600">
                                {status}
                            </div>
                        )}
                        <form onSubmit={submit}>
                            <TextInput
                                id="email"
                                type="email"
                                name="email"
                                value={data.email}
                                placeholder={translate(
                                    "common.placeholder.email",
                                )}
                                isFocused={true}
                                onChange={(e) =>
                                    setData("email", e.target.value)
                                }
                                error={errors.email}
                            />

                            <div className="mt-4 form-control">
                                <PrimaryButton disabled={processing}>
                                    {translate(
                                        "password.reset_password_link_btn",
                                    )}
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
}

ForgotPassword.layout = (page) => <AppLayout children={page} />;

export default ForgotPassword;
