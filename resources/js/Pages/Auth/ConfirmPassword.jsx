import { useState } from "react";

import { Head, useForm } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

import AppLayout from "@/layouts/AppLayout";
import PasswordInput from "@/components/forms/PasswordInput";
import PrimaryButton from "@/components/buttons/PrimaryButton";

function ConfirmPassword() {
    const { translate } = useSSRTranslations();

    const [showPassword, setShowPassword] = useState(false);
    const { data, setData, post, processing, errors, reset } = useForm({
        password: "",
    });

    const submit = (e) => {
        e.preventDefault();

        post(route("password.confirm"), {
            onFinish: () => reset("password"),
        });
    };

    return (
        <>
            <Head title={translate("password.confirm_page_title")} />
            <div className="flex py-10 items-center justify-center">
                <div className="card w-full max-w-md bg-base-100 shadow-xl">
                    <div className="card-body">
                        <h5 className="card-title justify-center">
                            {translate("password.confirm_page_title")}
                        </h5>

                        <div className="mb-4 text-sm text-gray-600">
                            {translate("password.confirm_page_description")}
                        </div>

                        <form onSubmit={submit}>
                            <PasswordInput
                                id="password"
                                name="password"
                                value={data.password}
                                placeholder={translate("common.password")}
                                autoComplete="current-password"
                                onChange={(e) =>
                                    setData("password", e.target.value)
                                }
                            />
                            <div className="form-control mt-6">
                                <PrimaryButton disabled={processing}>
                                    {translate("password.confirm_btn")}
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
}

ConfirmPassword.layout = (page) => <AppLayout children={page} />;

export default ConfirmPassword;
