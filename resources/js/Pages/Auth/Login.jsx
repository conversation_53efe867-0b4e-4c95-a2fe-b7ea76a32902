import { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

import AppLayout from "@/layouts/AppLayout";
import Checkbox from "@/components/forms/Checkbox";
import PasswordInput from "@/components/forms/PasswordInput";
import PrimaryButton from "@/components/buttons/PrimaryButton";
import TextInput from "@/components/forms/TextInput";

function Login({ status }) {
    const { translate } = useSSRTranslations();

    const [showPassword, setShowPassword] = useState(false);

    const getRedirectParam = () => {
        // Check if window is available (not during SSR)
        if (typeof window === "undefined") {
            return "/my-account";
        }

        const params = new URLSearchParams(window.location.search);
        return decodeURIComponent(params.get("redirect") ?? "/my-account");
    };
    const { data, setData, post, processing, errors, reset } = useForm({
        email: "",
        password: "",
        remember: false,
        redirect: getRedirectParam(),
    });

    const submit = (e) => {
        e.preventDefault();

        post(route("login"), {
            onFinish: () => reset("password"),
        });
    };

    return (
        <>
            <Head title={translate("login.page_title")} />
            <div className="flex py-10 items-center justify-center">
                <div className="card w-full max-w-md bg-base-100 shadow-xl">
                    <div className="card-body">
                        <h5 className="card-title justify-center">
                            {translate("login.page_title")}
                        </h5>
                        {status && (
                            <div className="mb-4 text-sm font-medium text-green-600">
                                {status}
                            </div>
                        )}
                        <form onSubmit={submit}>
                            <div className="grid gap-4">
                                <TextInput
                                    id="email"
                                    type="email"
                                    name="email"
                                    value={data.email}
                                    autoComplete="username"
                                    placeholder={translate(
                                        "common.placeholder.email",
                                    )}
                                    isFocused={true}
                                    onChange={(e) =>
                                        setData("email", e.target.value)
                                    }
                                    error={errors.email}
                                />

                                <PasswordInput
                                    id="password"
                                    name="password"
                                    value={data.password}
                                    placeholder={translate(
                                        "common.placeholder.password",
                                    )}
                                    autoComplete="current-password"
                                    onChange={(e) =>
                                        setData("password", e.target.value)
                                    }
                                    error={errors.password}
                                />
                            </div>

                            <div className="mt-4 flex justify-between items-center">
                                <label className="flex items-center">
                                    <Checkbox
                                        name="remember"
                                        checked={data.remember}
                                        onChange={(e) =>
                                            setData(
                                                "remember",
                                                e.target.checked,
                                            )
                                        }
                                    />
                                    <span className="ms-2 text-sm text-gray-600">
                                        {translate("login.remember_me")}
                                    </span>
                                </label>
                                <Link
                                    href={route("password.request")}
                                    className="label-text-alt link link-hover"
                                >
                                    {translate("login.forgot_pass_text")}
                                </Link>
                            </div>

                            <div className="form-control mt-6">
                                <PrimaryButton disabled={processing}>
                                    {translate("login.page_title")}
                                </PrimaryButton>
                            </div>

                            <div className="divider">
                                {translate("login.or_text")}
                            </div>

                            <p className="text-center">
                                {translate("login.dont_have_account")}
                                <Link
                                    href={route("register")}
                                    className="link link-primary ms-2"
                                >
                                    {translate("common.register_btn")}
                                </Link>
                            </p>
                        </form>
                    </div>
                </div>
            </div>
        </>
    );
}

Login.layout = (page) => <AppLayout children={page} />;

export default Login;
