export default function LeagueCard({ league }) {
    return (
        <div className="card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure className="relative h-48 bg-gray-200">
                {league.image !== "" ? (
                    <img
                        src={league.image}
                        alt={league.image_alt || league.name}
                        className="w-full h-full object-cover"
                    />
                ) : (
                    <img src="/img/ticketgol-logo.png" alt={league.name} />
                )}
            </figure>
            <div className="card-body">
                <div
                    className="tooltip tooltip-neutral w-fit text-left"
                    data-tip={league.name}
                    tabIndex={0}
                >
                    <h2 className="card-title line-clamp-1 max-w-xs">
                        {league.name}
                    </h2>
                </div>
            </div>
        </div>
    );
}
