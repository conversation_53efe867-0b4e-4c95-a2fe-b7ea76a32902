import { Link } from "@inertiajs/react";
import {
    ChevronsUp,
    Facebook,
    Twitter,
    Instagram,
    Youtube,
    Mail,
} from "lucide-react";
import { useState, useEffect } from "react";

export default function Footer() {
    const [showButton, setShowButton] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            setShowButton(window.scrollY > 300);
        };
        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: "smooth" });
    };

    return (
        <footer className="bg-neutral text-neutral-content">
            <div className="container mx-auto px-4">
                <div className="footer py-10">
                    <div>
                        <span className="footer-title">Company</span>
                        <Link href="/about" className="link link-hover">
                            About us
                        </Link>
                        <Link href="/contact" className="link link-hover">
                            Contact
                        </Link>
                        <Link href="/careers" className="link link-hover">
                            Careers
                        </Link>
                        <Link href="/press" className="link link-hover">
                            Press kit
                        </Link>
                    </div>
                    <div>
                        <span className="footer-title">Legal</span>
                        <Link href="/terms" className="link link-hover">
                            Terms of use
                        </Link>
                        <Link href="/privacy" className="link link-hover">
                            Privacy policy
                        </Link>
                        <Link href="/cookie" className="link link-hover">
                            Cookie policy
                        </Link>
                    </div>
                    <div>
                        <span className="footer-title">Social</span>
                        <div className="flex gap-4">
                            <a href="#" className="btn btn-ghost btn-square">
                                <Facebook className="w-5 h-5" />
                            </a>
                            <a href="#" className="btn btn-ghost btn-square">
                                <Twitter className="w-5 h-5" />
                            </a>
                            <a href="#" className="btn btn-ghost btn-square">
                                <Instagram className="w-5 h-5" />
                            </a>
                            <a href="#" className="btn btn-ghost btn-square">
                                <Youtube className="w-5 h-5" />
                            </a>
                        </div>
                    </div>
                    <div>
                        <span className="footer-title">Newsletter</span>
                        <div className="form-control w-full md:w-80">
                            <label className="label">
                                <span className="label-text text-neutral-content">
                                    Stay updated with our latest events!
                                </span>
                            </label>
                            <div className="join">
                                <input
                                    type="text"
                                    placeholder="Enter your email"
                                    className="input input-bordered join-item w-full"
                                />
                                <button className="btn btn-primary join-item">
                                    <Mail className="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="footer footer-center p-4 border-t border-base-300">
                    <div>
                        <p>Copyright © 2025 TicketGol - All rights reserved</p>
                    </div>
                </div>
                <div
                    className={`fixed bottom-6 right-6 transition-opacity duration-300 ${
                        showButton
                            ? "opacity-100"
                            : "opacity-0 pointer-events-none"
                    }`}
                >
                    <button
                        onClick={scrollToTop}
                        className="flex items-center gap-2 px-4 py-2 rounded-full
                           bg-gradient-to-r from-amber-500 to-amber-600
                           text-white shadow-lg hover:from-amber-400 hover:to-amber-500
                           focus:outline-none focus:ring-2 focus:ring-amber-300
                           active:scale-95 transition"
                    >
                        <ChevronsUp className="w-5 h-5" />
                        <span className="text-sm font-medium">TOP</span>
                    </button>
                </div>
            </div>
        </footer>
    );
}
