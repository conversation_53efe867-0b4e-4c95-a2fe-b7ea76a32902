import { Link } from "@inertiajs/react";
import { Facebook, Twitter, Instagram, Youtube, Mail } from "lucide-react";

export default function Footer() {
    return (
        <footer className="bg-neutral text-neutral-content">
            <div className="container mx-auto px-4">
                <div className="footer py-10">
                    <div>
                        <span className="footer-title">Company</span>
                        <Link href="/about" className="link link-hover">
                            About us
                        </Link>
                        <Link href="/contact" className="link link-hover">
                            Contact
                        </Link>
                        <Link href="/careers" className="link link-hover">
                            Careers
                        </Link>
                        <Link href="/press" className="link link-hover">
                            Press kit
                        </Link>
                    </div>
                    <div>
                        <span className="footer-title">Legal</span>
                        <Link href="/terms" className="link link-hover">
                            Terms of use
                        </Link>
                        <Link href="/privacy" className="link link-hover">
                            Privacy policy
                        </Link>
                        <Link href="/cookie" className="link link-hover">
                            Cookie policy
                        </Link>
                    </div>
                    <div>
                        <span className="footer-title">Social</span>
                        <div className="flex gap-4">
                            <a href="#" className="btn btn-ghost btn-square">
                                <Facebook className="w-5 h-5" />
                            </a>
                            <a href="#" className="btn btn-ghost btn-square">
                                <Twitter className="w-5 h-5" />
                            </a>
                            <a href="#" className="btn btn-ghost btn-square">
                                <Instagram className="w-5 h-5" />
                            </a>
                            <a href="#" className="btn btn-ghost btn-square">
                                <Youtube className="w-5 h-5" />
                            </a>
                        </div>
                    </div>
                    <div>
                        <span className="footer-title">Newsletter</span>
                        <div className="form-control w-full md:w-80">
                            <label className="label">
                                <span className="label-text text-neutral-content">
                                    Stay updated with our latest events!
                                </span>
                            </label>
                            <div className="join">
                                <input
                                    type="text"
                                    placeholder="Enter your email"
                                    className="input input-bordered join-item w-full"
                                />
                                <button className="btn btn-primary join-item">
                                    <Mail className="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="footer footer-center p-4 border-t border-base-300">
                    <div>
                        <p>Copyright © 2025 TicketGol - All rights reserved</p>
                    </div>
                </div>
            </div>
        </footer>
    );
}
