import { router } from "@inertiajs/react";
import toast from "react-hot-toast";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useTicketSell from "@/hooks/useTicketSell";

export default function ReviewStep() {
    const { translate } = useSSRTranslations();
    const { prevStep, nextStep, formData, updateFormData } = useTicketSell();

    const handleConfirmClick = async (e) => {
        e.preventDefault();

        if (!formData.terms_agreed) {
            toast.error(
                translate(
                    "sell.conditions_terms",
                    "Please read and agree to our terms and conditions",
                ),
            );
            return;
        }

        try {
            const { data } = await axios.post(
                route("api.tickets.store"),
                formData,
            );
            if (data.success) {
                toast.success(data.message);
                router.visit(route("dashboard"));
            }
        } catch (error) {
            if (error.response?.status === 422 && error.response.data?.errors) {
                toast.error(Object.values(error.response.data?.errors)[0]?.[0]);
            } else {
                toast.error(translate("common.something_wrong"));
            }
        }
    };

    return (
        <>
            <div>
                <h2 className="text-xl font-bold mb-3">
                    {translate("sell.step_4_title")}
                </h2>
                <div className="space-y-3">
                    <div className="text-sm text-gray-700">
                        <span className="font-bold">
                            {translate("sell.labels.quantity")} :
                        </span>{" "}
                        {formData.quantity}
                    </div>
                    <div className="text-sm text-gray-700">
                        <span className="font-bold">
                            {translate("sell.labels.price")} :
                        </span>{" "}
                        {formData.price}
                    </div>
                    <div className="text-sm text-gray-700">
                        <span className="font-bold">
                            {translate("sell.labels.face_value_price")} :
                        </span>{" "}
                        {formData.face_value_price}
                    </div>
                    <div className="text-sm text-gray-700">
                        <span className="font-bold">
                            {translate("sell.labels.sector_id")} :
                        </span>{" "}
                        {formData.sector_name}
                    </div>

                    {formData.ticket_rows !== "" && (
                        <div className="text-sm text-gray-700">
                            <span className="font-bold">
                                {translate("sell.labels.ticket_rows")} :
                            </span>{" "}
                            {formData.ticket_rows}
                        </div>
                    )}
                    {formData.ticket_seats !== "" && (
                        <div className="text-sm text-gray-700">
                            <span className="font-bold">
                                {translate("sell.labels.ticket_seats")} :
                            </span>{" "}
                            {formData.ticket_seats}
                        </div>
                    )}

                    <div className="text-sm text-gray-700">
                        <span className="font-bold">
                            {translate("sell.labels.ticket_type")} :
                        </span>{" "}
                        {translate(
                            `enums.ticket_types.${formData.ticket_type}`,
                        )}
                    </div>
                    <div className="text-sm text-gray-700">
                        <span className="font-bold">
                            {translate("sell.labels.quantity_split_type")} :
                        </span>{" "}
                        {translate(
                            `enums.ticket_quantity_split_types.${formData.quantity_split_type}`,
                        )}
                    </div>
                    <div className="divider"></div>
                    <div className="flex items-center text-sm text-gray-700 cursor-pointer">
                        <input
                            type="checkbox"
                            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                            checked={formData.terms_agreed}
                            onChange={(e) =>
                                updateFormData("terms_agreed", e.target.checked)
                            }
                        />
                        <span className="ml-2">
                            {translate(
                                "sell.read_agree_text",
                                "I have read and agree to the",
                            )}{" "}
                            <a href="#" className="link link-primary">
                                {translate(
                                    "sell.terms_conditions_text",
                                    "terms & conditions",
                                )}
                            </a>
                        </span>
                    </div>
                </div>
                <div className="flex justify-between mt-4">
                    <button className="btn btn-outline" onClick={prevStep}>
                        {translate("sell.back_btn", "Back")}
                    </button>
                    <button
                        className="btn btn-primary"
                        onClick={handleConfirmClick}
                    >
                        {translate("sell.confirm_create_btn")}
                    </button>
                </div>
            </div>
        </>
    );
}
