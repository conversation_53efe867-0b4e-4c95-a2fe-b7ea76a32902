import FilterCheckbox from "@/components/forms/FilterCheckbox";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useTicketSell from "@/hooks/useTicketSell";

export default function RestrictionsStep() {
    const { translate } = useSSRTranslations();
    const { prevStep, nextStep, configurations, formData, updateFormData } =
        useTicketSell();

    const restrictions = configurations.restrictions;

    return (
        <>
            <div>
                <h2 className="text-xl font-bold mb-3">
                    {translate("sell.step_3_title")}
                </h2>
                <form className="space-y-4">
                    <div className="form-control mt-2">
                        <label className="label text-sm font-medium pl-0">
                            {translate("sell.labels.restrictions")}
                        </label>
                        {Object.keys(restrictions).map((key) => (
                            <FilterCheckbox
                                key={key}
                                label={restrictions[key]}
                                value={key}
                                checked={formData.restrictions.includes(key)}
                                onChange={(value) => {
                                    updateFormData("restrictions", value);
                                }}
                            />
                        ))}
                    </div>
                </form>
                <div className="flex justify-between mt-4">
                    <button className="btn btn-outline" onClick={prevStep}>
                        {translate("sell.back_btn", "Back")}
                    </button>
                    <button className="btn btn-primary" onClick={nextStep}>
                        {translate("sell.continue_btn")}
                    </button>
                </div>
            </div>
        </>
    );
}
