import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import useTicketSell from "@/hooks/useTicketSell";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useValidation from "@/hooks/useValidation";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

export default function QuantityPriceStep() {
    const { translate } = useSSRTranslations();
    const { event, nextStep, formData, configurations, updateFormData } =
        useTicketSell();

    const validationSchema = {
        quantity: [
            "required",
            "number",
            { rule: "min", value: 1 },
            { rule: "max", value: configurations.max_quantity_per_ticket },
        ],
        price: [
            "required",
            "number",
            { rule: "min", value: 1 },
            { rule: "max", value: configurations.max_price_limit },
        ],
        face_value_price: [
            "required",
            "number",
            { rule: "min", value: 1 },
            { rule: "max", value: configurations.max_price_limit },
        ],
        sector_id: ["required"],
        ticket_rows: [{ rule: "maxlength", value: 100 }],
        ticket_seats: [{ rule: "maxlength", value: 200 }],
    };

    const { errors, validate, validateField } = useValidation(validationSchema);

    const sectorOptions = prepareOptionsFromEnum(event.stadium_sectors);

    const subTotal =
        formData.quantity && formData.price
            ? formData.quantity * formData.price
            : 0;
    const serviceCharge =
        Math.round(
            formData.quantity *
                formData.price *
                configurations.service_charge_rate *
                100,
        ) / 100;
    const tax = Math.round(serviceCharge * configurations.tax_rate * 100) / 100;
    const grandTotal = subTotal + serviceCharge + tax;

    const handleContinue = (e) => {
        e.preventDefault();
        const isValid = validate(formData);
        if (!isValid) return;
        nextStep();
    };

    return (
        <>
            <div>
                <h2 className="text-xl font-bold mb-3">
                    {translate("sell.step_1_title")}
                </h2>
                <form className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <TextInput
                            id="quantity"
                            type="number"
                            value={formData.quantity}
                            min="1"
                            max={configurations.max_quantity_per_ticket}
                            label={translate("sell.labels.quantity")}
                            placeholder={translate("sell.placeholder.quantity")}
                            datarequired="true"
                            onChange={(e) =>
                                updateFormData("quantity", e.target.value)
                            }
                            error={errors?.quantity}
                        />
                        <TextInput
                            id="price"
                            type="number"
                            value={formData.price}
                            label={translate("sell.labels.price")}
                            min="0"
                            max={configurations.max_price_limit}
                            step="0.01"
                            placeholder={translate("sell.placeholder.price")}
                            datarequired="true"
                            onChange={(e) =>
                                updateFormData("price", e.target.value)
                            }
                            onBlur={(e) => {
                                updateFormData(
                                    "price",
                                    parseFloat(e.target.value).toFixed(2),
                                );
                                validateField(
                                    "price",
                                    formData.price,
                                    formData,
                                );
                            }}
                            error={errors?.price}
                        />
                        <TextInput
                            id="face_value_price"
                            type="number"
                            value={formData.face_value_price}
                            label={translate("sell.labels.face_value_price")}
                            min="0"
                            max={configurations.max_price_limit}
                            step="0.01"
                            placeholder={translate(
                                "sell.placeholder.face_value_price",
                            )}
                            datarequired="true"
                            onChange={(e) =>
                                updateFormData(
                                    "face_value_price",
                                    e.target.value,
                                )
                            }
                            onBlur={(e) => {
                                updateFormData(
                                    "face_value_price",
                                    parseFloat(e.target.value).toFixed(2),
                                );
                                validateField(
                                    "face_value_price",
                                    formData.face_value_price,
                                    formData,
                                );
                            }}
                            error={errors?.face_value_price}
                        />
                        <SelectInput
                            id="sector"
                            label={translate("sell.labels.sector_id")}
                            placeholder={translate(
                                "sell.placeholder.sector_id",
                            )}
                            options={sectorOptions}
                            datarequired="true"
                            value={sectorOptions.find(
                                (opt) => opt.value === formData.sector_id,
                            )}
                            onChange={(option) => {
                                updateFormData(
                                    "sector_id",
                                    option?.value || "",
                                );
                                updateFormData(
                                    "sector_name",
                                    option?.label || "",
                                );
                                validateField(
                                    "sector_id",
                                    option?.value,
                                    formData,
                                );
                            }}
                            error={errors?.sector_id}
                        />
                        <TextInput
                            id="ticket_rows"
                            value={formData.ticket_rows}
                            label={translate("sell.labels.ticket_rows")}
                            placeholder={translate(
                                "sell.placeholder.ticket_rows",
                            )}
                            onChange={(e) =>
                                updateFormData("ticket_rows", e.target.value)
                            }
                            onBlur={(e) =>
                                validateField(
                                    "ticket_rows",
                                    formData.ticket_rows,
                                    formData,
                                )
                            }
                            error={errors?.ticket_rows}
                        />
                        <TextInput
                            id="ticket_seats"
                            value={formData.ticket_seats}
                            label={translate("sell.labels.ticket_seats")}
                            placeholder={translate(
                                "sell.placeholder.ticket_seats",
                            )}
                            onChange={(e) =>
                                updateFormData("ticket_seats", e.target.value)
                            }
                            onBlur={(e) =>
                                validateField(
                                    "ticket_seats",
                                    formData.ticket_seats,
                                    formData,
                                )
                            }
                            error={errors?.ticket_seats}
                        />
                    </div>
                </form>
                <div className="mt-5 w-full">
                    <div className="bg-base-200 rounded-lg p-4 text-sm text-gray-800">
                        <div className="flex justify-between mb-1">
                            <span>{translate("sell.your_earning_text")}</span>
                            <span>€{subTotal.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between mb-1">
                            <span>{translate("sell.service_charge_text")}</span>
                            <span>€{serviceCharge.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                            <span>{translate("sell.tax_text")}</span>
                            <span>€{tax.toFixed(2)}</span>
                        </div>
                        <div className="divider m-0" />
                        <div className="flex justify-between font-semibold text-base mt-2">
                            <span>{translate("sell.final_price_text")}</span>
                            <span>€{grandTotal.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
                <button
                    className="btn btn-primary mt-4 float-right"
                    onClick={handleContinue}
                >
                    {translate("sell.continue_btn")}
                </button>
            </div>
        </>
    );
}
