import TextInput from "@/components/forms/TextInput";
import RadioInput from "@/components/forms/RadioInput";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useTicketSell from "@/hooks/useTicketSell";
import useValidation from "@/hooks/useValidation";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

const validationSchema = {
    ticket_type: ["required"],
    quantity_split_type: ["required"],
    description: ["required", { rule: "maxlength", value: 255 }],
};
export default function TypeDetailStep() {
    const { translate } = useSSRTranslations();
    const { event, prevStep, formData, updateFormData, nextStep } =
        useTicketSell();

    const { errors, validate, validateField } = useValidation(validationSchema);

    const ticketTypeOptions = prepareOptionsFromEnum(
        translate("enums.ticket_types"),
    );
    const ticketSplitTypeOptions = prepareOptionsFromEnum(
        translate("enums.ticket_quantity_split_types"),
    );

    const handleContinue = (e) => {
        e.preventDefault();
        const isValid = validate(formData);
        if (!isValid) return;
        nextStep();
    };

    return (
        <>
            <div>
                <h2 className="text-xl font-bold mb-3">
                    {translate("sell.step_2_text")}
                </h2>
                <form className="space-y-4">
                    <TextInput
                        id="description"
                        type="textarea"
                        value={formData.description}
                        label={translate("sell.labels.description")}
                        placeholder={translate("sell.placeholder.description")}
                        datarequired="true"
                        onChange={(e) =>
                            updateFormData("description", e.target.value)
                        }
                        onBlur={(e) =>
                            validateField(
                                "description",
                                formData.description,
                                formData,
                            )
                        }
                        error={errors?.description}
                    />
                    <RadioInput
                        label={translate("sell.labels.ticket_type")}
                        checked={formData.ticket_type}
                        onChange={(e) => {
                            updateFormData("ticket_type", e.target.value);
                            validateField(
                                "ticket_type",
                                e.target.value,
                                formData,
                            );
                        }}
                        datarequired="true"
                        options={ticketTypeOptions}
                        error={errors?.ticket_type}
                    />
                    <RadioInput
                        label={translate("sell.labels.quantity_split_type")}
                        checked={formData.quantity_split_type}
                        onChange={(e) => {
                            updateFormData(
                                "quantity_split_type",
                                e.target.value,
                            );
                            validateField(
                                "quantity_split_type",
                                e.target.value,
                                formData,
                            );
                        }}
                        datarequired="true"
                        options={ticketSplitTypeOptions}
                        error={errors?.quantity_split_type}
                    />
                </form>
                <div className="flex justify-between mt-4">
                    <button className="btn btn-outline" onClick={prevStep}>
                        {translate("sell.back_btn", "Back")}
                    </button>
                    <button
                        className="btn btn-primary"
                        onClick={handleContinue}
                    >
                        {translate("sell.continue_btn", "Continue")}
                    </button>
                </div>
            </div>
        </>
    );
}
