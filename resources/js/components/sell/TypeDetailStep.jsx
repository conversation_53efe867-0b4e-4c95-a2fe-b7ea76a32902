import toast from "react-hot-toast";
import TextInput from "@/components/forms/TextInput";
import RadioInput from "@/components/forms/RadioInput";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useTicketSell from "@/hooks/useTicketSell";
import useValidation from "@/hooks/useValidation";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

export default function TypeDetailStep() {
    const { translate } = useSSRTranslations();
    const { event, prevStep, formData, updateFormData, nextStep } =
        useTicketSell();

    const validationSchema = {
        ticket_type: ["required"],
        quantity_split_type: ["required"],
        description: ["required", { rule: "maxlength", value: 255 }],
        sell_in_multiples: [
            "number",
            { rule: "min", value: 1 },
            {
                rule: "max",
                value: formData.quantity,
            },
        ],
    };

    if (formData.quantity_split_type === "in_multiple") {
        validationSchema.sell_in_multiples.unshift("required");
    }

    const { errors, validate, validateField } = useValidation(validationSchema);

    const ticketTypeOptions = prepareOptionsFromEnum(
        translate("enums.ticket_types"),
    );
    const ticketSplitTypeOptions = prepareOptionsFromEnum(
        translate("enums.ticket_quantity_split_types"),
    );

    const handleContinue = (e) => {
        e.preventDefault();
        const isValid = validate(formData);
        if (!isValid) return;

        if (
            formData.quantity_split_type === "in_multiple" &&
            formData.quantity % formData.sell_in_multiples !== 0
        ) {
            toast.error(translate("sell.sell_in_multiples_error"));
            return;
        }
        nextStep();
    };

    return (
        <>
            <div>
                <h2 className="text-xl font-bold mb-3">
                    {translate("sell.step_2_text")}
                </h2>
                <form className="space-y-4">
                    <TextInput
                        id="description"
                        type="textarea"
                        value={formData.description}
                        label={translate("sell.labels.description")}
                        placeholder={translate("sell.placeholder.description")}
                        datarequired="true"
                        onChange={(e) =>
                            updateFormData("description", e.target.value)
                        }
                        onBlur={(e) =>
                            validateField(
                                "description",
                                formData.description,
                                formData,
                            )
                        }
                        error={errors?.description}
                    />
                    <RadioInput
                        label={translate("sell.labels.ticket_type")}
                        checked={formData.ticket_type}
                        onChange={(e) => {
                            updateFormData("ticket_type", e.target.value);
                            validateField(
                                "ticket_type",
                                e.target.value,
                                formData,
                            );
                        }}
                        datarequired="true"
                        options={ticketTypeOptions}
                        error={errors?.ticket_type}
                    />
                    <RadioInput
                        label={translate("sell.labels.quantity_split_type")}
                        checked={formData.quantity_split_type}
                        onChange={(e) => {
                            updateFormData(
                                "quantity_split_type",
                                e.target.value,
                            );
                            updateFormData("sell_in_multiples", "");
                            validateField(
                                "quantity_split_type",
                                e.target.value,
                                formData,
                            );
                        }}
                        datarequired="true"
                        options={ticketSplitTypeOptions}
                        error={errors?.quantity_split_type}
                    />
                    {formData.quantity_split_type === "in_multiple" && (
                        <TextInput
                            id="sell_in_multiples"
                            type="number"
                            value={formData.sell_in_multiples}
                            label={translate("sell.labels.sell_in_multiples")}
                            placeholder={translate(
                                "sell.placeholder.sell_in_multiples",
                            )}
                            datarequired="true"
                            onChange={(e) =>
                                updateFormData(
                                    "sell_in_multiples",
                                    e.target.value,
                                )
                            }
                            min={1}
                            max={formData.quantity}
                            onBlur={(e) =>
                                validateField(
                                    "sell_in_multiples",
                                    formData.sell_in_multiples,
                                    formData,
                                )
                            }
                            error={errors?.sell_in_multiples}
                        />
                    )}
                </form>
                <div className="flex justify-between mt-4">
                    <button className="btn btn-outline" onClick={prevStep}>
                        {translate("sell.back_btn", "Back")}
                    </button>
                    <button
                        className="btn btn-primary"
                        onClick={handleContinue}
                    >
                        {translate("sell.continue_btn", "Continue")}
                    </button>
                </div>
            </div>
        </>
    );
}
