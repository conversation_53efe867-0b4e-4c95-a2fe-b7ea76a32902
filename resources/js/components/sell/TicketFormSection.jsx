import QuantityPriceStep from "@/components/sell/QuantityPriceStep";
import TypeDetailStep from "@/components/sell/TypeDetailStep";
import RestrictionsStep from "@/components/sell/RestrictionsStep";
import ReviewStep from "@/components/sell/ReviewStep";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useTicketSell from "@/hooks/useTicketSell";

export default function TicketFormSection() {
    const { translate } = useSSRTranslations();
    const { step } = useTicketSell();

    return (
        <>
            <ul className="steps mb-5 w-full">
                <li className={`step ${step >= 1 ? "step-primary" : ""}`}>
                    {translate("sell.step_1_title")}
                </li>
                <li className={`step ${step >= 2 ? "step-primary" : ""}`}>
                    {translate("sell.step_2_title")}
                </li>
                <li className={`step ${step >= 3 ? "step-primary" : ""}`}>
                    {translate("sell.step_3_title")}
                </li>
                <li className={`step ${step >= 4 ? "step-primary" : ""}`}>
                    {translate("sell.step_4_title")}
                </li>
            </ul>
            <div className="divider mt-0 mb-2"></div>
            {step === 1 && <QuantityPriceStep />}

            {step === 2 && <TypeDetailStep />}

            {step === 3 && <RestrictionsStep />}

            {step === 4 && <ReviewStep />}
        </>
    );
}
