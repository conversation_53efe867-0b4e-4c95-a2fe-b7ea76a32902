import { Calendar, MapPin } from "lucide-react";

export default function TempEventCard({ title, date, location, image, price }) {
    return (
        <div className="card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure className="relative h-48">
                <img
                    src={image}
                    alt={title}
                    className="w-full h-full object-cover"
                />
                <div className="absolute top-4 right-4 badge badge-warning">
                    {price}
                </div>
            </figure>
            <div className="card-body">
                <h2 className="card-title">{title}</h2>
                <div className="flex items-center text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span className="text-sm">{date}</span>
                </div>
                <div className="flex items-center text-gray-600">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span className="text-sm">{location}</span>
                </div>
                <div className="card-actions justify-end mt-4">
                    <button className="btn btn-primary">Buy Tickets</button>
                </div>
            </div>
        </div>
    );
}
