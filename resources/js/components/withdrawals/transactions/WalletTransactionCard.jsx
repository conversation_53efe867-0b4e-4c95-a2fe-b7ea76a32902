import { Eye } from "lucide-react";
import { convertToLocalTimezone } from "@/helpers/dateTimeUtils";
import { getStatusBadgeClass } from "@/helpers/badgeClassUtils";
import { Link } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

const WalletTransactionCard = ({ transaction }) => {
    const { translate } = useSSRTranslations();

    return (
        <div className="card bg-base-100 shadow-md border border-base-300">
            <div className="card-body p-5">
                <div className="flex justify-between items-start flex-wrap gap-2 border-b pb-3 mb-2">
                    <div className="flex flex-col min-[400px]:flex-row min-[400px]:items-center gap-2">
                        <h2 className="text-base sm:text-lg font-semibold">
                            {translate("my_withdrawals.labels.transaction")} #
                            {transaction.transaction_no}
                        </h2>
                        <span
                            className={`px-3 py-1 rounded-full text-sm border w-fit ${getStatusBadgeClass(transaction.status.color)}`}
                        >
                            {transaction.status.label}
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link
                            className="rounded-md bg-info/10 text-info hover:bg-info/20 p-2 transition"
                            href={route(
                                "my-account.withdrawals.wallet-transaction",
                                transaction.transaction_no,
                            )}
                        >
                            <Eye className="w-4 h-4" />
                        </Link>
                    </div>
                </div>

                <div className="flex grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm text-base-content/70 text-center sm:text-left">
                    <p>
                        <span className="font-medium text-base-content">
                            {translate(
                                "my_withdrawals.labels.transaction_type",
                            )}
                            :
                        </span>{" "}
                        <span
                            className={`px-3 py-[2px] rounded-full text-sm border w-fit ${getStatusBadgeClass(transaction.transaction_type.color)}`}
                        >
                            {transaction.transaction_type.label}
                        </span>
                    </p>
                    {transaction.order && (
                        <p>
                            <span className="font-medium text-base-content">
                                {translate("my_withdrawals.labels.order")} #:
                            </span>{" "}
                            {transaction.order.order_no}
                        </p>
                    )}

                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.total")}:
                        </span>{" "}
                        €{transaction.total_amount}{" "}
                        <span
                            className={`px-3 py-[2px] rounded-full text-sm border w-fit ml-2 ${getStatusBadgeClass(transaction.entry_type.color)}`}
                        >
                            {transaction.entry_type.label}
                        </span>
                    </p>
                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.withdrawn")}:
                        </span>{" "}
                        €{transaction.withdrawn_amount}
                    </p>
                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.remaining")}:
                        </span>{" "}
                        €{transaction.remained_amount}
                    </p>
                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.created_at")}:
                        </span>{" "}
                        {convertToLocalTimezone(
                            transaction.created_at,
                        ).toFormattedDateTime()}
                    </p>
                </div>
            </div>
        </div>
    );
};

export default WalletTransactionCard;
