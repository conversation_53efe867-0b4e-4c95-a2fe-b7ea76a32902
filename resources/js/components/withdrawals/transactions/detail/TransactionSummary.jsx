import React, { useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import InfoCard from "@/components/utils/InfoCard";
import { formatDate } from "@/helpers/formatUtils";
import { getStatusBadgeClass } from "@/helpers/badgeClassUtils";

function TransactionSummary({ transaction }) {
    const { translate } = useSSRTranslations();

    return (
        <>
            <InfoCard
                title={translate("my_withdrawals.transaction_summary_title")}
                className="mt-4"
            >
                <div className="flex flex-col gap-4 text-sm sm:text-base">
                    <div className="space-y-2">
                        <div className="flex flex-col sm:flex-row sm:justify-between pb-1 gap-2">
                            <span>
                                <b>
                                    {translate(
                                        "my_withdrawals.labels.transaction_type",
                                    )}
                                    :{" "}
                                </b>
                                <span
                                    className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeClass(transaction.transaction_type.color)}`}
                                >
                                    {transaction.transaction_type.label}
                                </span>
                            </span>
                            <span>
                                <b>
                                    {translate("my_withdrawals.labels.status")}
                                    :{" "}
                                </b>
                                <span
                                    className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeClass(transaction.status.color)}`}
                                >
                                    {transaction.status.label}
                                </span>
                            </span>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:justify-between pb-1 gap-2">
                            <span>
                                <b>
                                    {translate("my_withdrawals.labels.total")}
                                    :{" "}
                                </b>
                                €{transaction.total_amount}{" "}
                                <span
                                    className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeClass(transaction.entry_type.color)}`}
                                >
                                    {transaction.entry_type.label}
                                </span>
                            </span>
                            <span>
                                <b>
                                    {translate(
                                        "my_withdrawals.labels.withdrawn",
                                    )}
                                    :{" "}
                                </b>
                                €{transaction.withdrawn_amount}
                            </span>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:justify-between pb-1 gap-2">
                            <span>
                                <b>
                                    {translate(
                                        "my_withdrawals.labels.remaining",
                                    )}
                                    :{" "}
                                </b>
                                €{transaction.remained_amount}
                            </span>
                            <span>
                                <b>
                                    {translate(
                                        "my_withdrawals.labels.created_at",
                                    )}
                                    :{" "}
                                </b>
                                {formatDate(transaction.created_at)}
                            </span>
                        </div>
                        {transaction.note && (
                            <div className="flex flex-col sm:flex-row sm:justify-between pb-1 gap-2">
                                <span>
                                    <b>
                                        {translate(
                                            "my_withdrawals.labels.transaction_note",
                                        )}
                                        :{" "}
                                    </b>
                                    {transaction.note}
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            </InfoCard>
        </>
    );
}

export default TransactionSummary;
