import { Search, Filter } from "lucide-react";
import { useEffect, useRef } from "react";
import { useDebounce } from "@uidotdev/usehooks";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useWalletTransactions from "@/hooks/useWalletTransactions";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import DatePicker from "@/components/forms/DatePicker";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

const WalletTransactionFilters = () => {
    const { translate } = useSSRTranslations();

    const { filters, updateFilter, clearFilters, getWalletTransactions } =
        useWalletTransactions();

    const debouncedFilters = useDebounce(filters, 500);
    const isFirstRender = useRef(true);
    const today = new Date();

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        getWalletTransactions();
    }, [debouncedFilters]);

    const statusOptions = [
        { label: translate("my_withdrawals.labels.all_status"), value: "" },
        ...prepareOptionsFromEnum(
            translate("enums.wallet_transaction_statuses"),
        ),
    ];

    const transactionTypeOptions = [
        {
            label: translate("my_withdrawals.labels.all_transaction_types"),
            value: "",
        },
        ...prepareOptionsFromEnum(translate("enums.wallet_transaction_types")),
    ];

    const entryTypeOptions = [
        {
            label: translate("my_withdrawals.labels.all_entry_types"),
            value: "",
        },
        ...prepareOptionsFromEnum(translate("enums.wallet_entry_types")),
    ];

    return (
        <div className="border-b pb-5">
            <div className="relative w-full mb-3">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                <TextInput
                    value={filters.search}
                    onChange={(e) => updateFilter("search", e.target.value)}
                    placeholder={translate("my_withdrawals.placeholder.search")}
                    className="pl-10"
                />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 sm:grid-cols-2 gap-3">
                <DatePicker
                    value={filters.date_from}
                    className="form-control mt-2"
                    onChange={(value) => updateFilter("date_from", value)}
                    placeholder={translate(
                        "my_withdrawals.placeholder.from_date",
                    )}
                    maxDate={filters.date_to || today}
                />

                <DatePicker
                    className="form-control mt-2"
                    value={filters.date_to}
                    onChange={(value) => updateFilter("date_to", value)}
                    placeholder={translate(
                        "my_withdrawals.placeholder.to_date",
                    )}
                    minDate={filters.date_from || undefined}
                    maxDate={today}
                />

                <SelectInput
                    options={transactionTypeOptions}
                    value={transactionTypeOptions.find(
                        (opt) => opt.value === filters.transaction_type,
                    )}
                    onChange={(option) =>
                        updateFilter("transaction_type", option?.value || "")
                    }
                    placeholder={translate(
                        "my_withdrawals.placeholder.transaction_type",
                    )}
                />
                <SelectInput
                    options={entryTypeOptions}
                    value={entryTypeOptions.find(
                        (opt) => opt.value === filters.entry_type,
                    )}
                    onChange={(option) =>
                        updateFilter("entry_type", option?.value || "")
                    }
                    placeholder={translate(
                        "my_withdrawals.placeholder.entry_type",
                    )}
                />

                <SelectInput
                    options={statusOptions}
                    value={statusOptions.find(
                        (opt) => opt.value === filters.status,
                    )}
                    onChange={(option) =>
                        updateFilter("status", option?.value || "")
                    }
                    placeholder={translate("my_withdrawals.placeholder.status")}
                />

                <SelectInput
                    options={translate("my_withdrawals.wallet_sort_options")}
                    value={
                        filters.sort
                            ? translate(
                                  "my_withdrawals.wallet_sort_options",
                              ).find((option) => option.value === filters.sort)
                            : null
                    }
                    onChange={(option) =>
                        updateFilter("sort", option?.value || "")
                    }
                    placeholder={translate(
                        "my_withdrawals.placeholder.sort_by",
                    )}
                />
            </div>

            <div className="flex gap-6 justify-end mt-5">
                <button
                    onClick={clearFilters}
                    className="btn btn-outline btn-sm"
                >
                    {translate("common.clear", "Clear")}
                </button>
            </div>
        </div>
    );
};

export default WalletTransactionFilters;
