import React, { useState, useEffect } from "react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useWalletTransactions from "@/hooks/useWalletTransactions";
import WalletTransactionFilters from "./WalletTransactionFilters";
import WalletTransactionCard from "./WalletTransactionCard";

export default function WalletTransactions() {
    const { translate } = useSSRTranslations();

    const {
        transactions,
        isLoading,
        filterChanged,
        getWalletTransactions,
        nextPageUrl,
        loadMoreWalletTransactions,
    } = useWalletTransactions();

    useEffect(() => {
        getWalletTransactions();
    }, []);

    const [observerRef] = useInfiniteScroll({
        loading: isLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreWalletTransactions,
        rootMargin: "100px",
    });

    return (
        <>
            <div className="flex justify-between items-center border-b pb-3 mb-6">
                <h1 className="text-md sm:text-xl font-bold">
                    {translate("my_withdrawals.wallet_transactions_tab_title")}
                </h1>
            </div>
            <WalletTransactionFilters />
            <div className="space-y-4 max-h-[70vh] overflow-y-auto my-5">
                {!isLoading && transactions.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                        <h2 className="text-2xl font-semibold text-base-content">
                            {translate("my_withdrawals.no_transactions")}
                        </h2>
                        <p className="text-gray-500 mt-2 max-w-md text-center">
                            {translate(
                                "my_withdrawals.no_transactions_details",
                            )}
                        </p>
                    </div>
                ) : (
                    <>
                        {filterChanged ? (
                            <p className="flex items-center justify-center h-64">
                                <span className="loading loading-bars loading-xl"></span>
                            </p>
                        ) : (
                            <>
                                {transactions.map((transaction) => (
                                    <WalletTransactionCard
                                        key={transaction.id}
                                        transaction={transaction}
                                    />
                                ))}

                                {nextPageUrl && (
                                    <div
                                        ref={observerRef}
                                        className="h-10"
                                    ></div>
                                )}
                            </>
                        )}

                        {isLoading && !filterChanged && (
                            <p className="flex items-center justify-center h-64">
                                <span className="loading loading-bars loading-xl"></span>
                            </p>
                        )}
                    </>
                )}
            </div>
        </>
    );
}
