import { Search, Filter } from "lucide-react";
import { useEffect, useRef } from "react";
import { useDebounce } from "@uidotdev/usehooks";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useWithdrawals from "@/hooks/useWithdrawals";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import DatePicker from "@/components/forms/DatePicker";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

const WithdrawalFilters = () => {
    const { translate } = useSSRTranslations();

    const { filters, updateFilter, clearFilters, getWithdrawals } =
        useWithdrawals();

    const debouncedFilters = useDebounce(filters, 500);
    const isFirstRender = useRef(true);
    const today = new Date();

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        getWithdrawals();
    }, [debouncedFilters]);

    const statusOptions = [
        { label: translate("my_withdrawals.labels.all_status"), value: "" },
        ...prepareOptionsFromEnum(translate("enums.withdrawal_statuses")),
    ];

    return (
        <div className="border-b pb-5">
            <div className="relative w-full mb-3">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                <TextInput
                    value={filters.search}
                    onChange={(e) => updateFilter("search", e.target.value)}
                    placeholder={translate(
                        "my_withdrawals.placeholder.withdrawal_search",
                    )}
                    className="pl-10"
                />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <DatePicker
                    value={filters.date_from}
                    className="form-control mt-2"
                    onChange={(value) => updateFilter("date_from", value)}
                    placeholder={translate(
                        "my_withdrawals.placeholder.from_date",
                    )}
                    maxDate={filters.date_to || today}
                />

                <DatePicker
                    className="form-control mt-2"
                    value={filters.date_to}
                    onChange={(value) => updateFilter("date_to", value)}
                    placeholder={translate(
                        "my_withdrawals.placeholder.to_date",
                    )}
                    minDate={filters.date_from || undefined}
                    maxDate={today}
                />

                <SelectInput
                    options={statusOptions}
                    value={statusOptions.find(
                        (opt) => opt.value === filters.status,
                    )}
                    onChange={(option) =>
                        updateFilter("status", option?.value || "")
                    }
                    placeholder={translate("my_withdrawals.placeholder.status")}
                />

                <SelectInput
                    options={translate(
                        "my_withdrawals.withdrawal_sort_options",
                    )}
                    value={
                        filters.sort
                            ? translate(
                                  "my_withdrawals.withdrawal_sort_options",
                              ).find((option) => option.value === filters.sort)
                            : null
                    }
                    onChange={(option) =>
                        updateFilter("sort", option?.value || "")
                    }
                    placeholder={translate(
                        "my_withdrawals.placeholder.sort_by",
                    )}
                />
            </div>

            <div className="flex gap-6 justify-end mt-5">
                <button
                    onClick={clearFilters}
                    className="btn btn-outline btn-sm"
                >
                    {translate("common.clear", "Clear")}
                </button>
            </div>
        </div>
    );
};

export default WithdrawalFilters;
