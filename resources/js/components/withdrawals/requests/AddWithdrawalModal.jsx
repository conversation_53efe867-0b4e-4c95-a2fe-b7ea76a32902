import { useEffect } from "react";
import toast from "react-hot-toast";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useWithdrawals from "@/hooks/useWithdrawals";
import useValidation from "@/hooks/useValidation";
import { Banknote } from "lucide-react";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";

export default function AddWithdrawalModal({ open, onClose, configurations }) {
    const { translate } = useSSRTranslations();
    const {
        configurationsLoading,
        getWithdrawals,
        formData,
        updateFormData,
        clearFormData,
    } = useWithdrawals();

    const validationSchema = {
        amount: [
            "required",
            "number",
            { rule: "min", value: configurations.min_withdrawal_limit },
            {
                rule: "max",
                value: Math.min(
                    configurations.balance,
                    configurations.max_withdrawal_limit,
                ),
            },
        ],
        payout_method: ["required"],
    };

    const { errors, validate, validateField } = useValidation(validationSchema);

    const handleCloseClick = () => {
        clearFormData();
        onClose();
    };

    const handleSaveClick = async (e) => {
        e.preventDefault();
        const isValid = validate(formData);
        if (!isValid) return;

        try {
            const { data } = await axios.post(
                route("api.withdrawals.store"),
                formData,
            );
            if (data.success) {
                toast.success(data.message);
                onClose();
                getWithdrawals();
                clearFormData();
            }
        } catch (error) {
            if (error.response?.status === 422 && error.response.data?.errors) {
                toast.error(Object.values(error.response.data?.errors)[0]?.[0]);
            } else {
                toast.error(translate("common.something_wrong"));
            }
        }
    };

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            {!configurationsLoading && (
                <div className="modal-box max-w-lg">
                    <button
                        className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                        onClick={handleCloseClick}
                    >
                        ✕
                    </button>

                    <div className="flex justify-center mb-5">
                        <div className="bg-success/20 text-success rounded-full p-2">
                            <Banknote className="w-6 h-6" />
                        </div>
                    </div>
                    <h3 className="text-lg font-bold text-center mb-3">
                        {translate("my_withdrawals.withdrawal_add_btn")}
                    </h3>
                    <div className="text-gray-500 text-sm mt-2 mb-2">
                        <strong>
                            {translate("my_withdrawals.labels.note", "Note")}:
                        </strong>{" "}
                        {translate("my_withdrawals.add_withdrawal_note")}
                    </div>
                    <form className="space-y-4">
                        <div className="grid grid-cols-1 gap-x-4">
                            <TextInput
                                id="amount"
                                type="number"
                                value={formData.amount}
                                label={translate(
                                    "my_withdrawals.labels.amount",
                                )}
                                placeholder={translate(
                                    "my_withdrawals.placeholder.amount",
                                )}
                                datarequired="true"
                                onChange={(e) => {
                                    updateFormData("amount", e.target.value);
                                    validateField(
                                        "amount",
                                        e.target.value,
                                        formData,
                                    );
                                }}
                                onBlur={(e) =>
                                    validateField(
                                        "amount",
                                        e.target.value,
                                        formData,
                                    )
                                }
                                error={errors?.amount}
                            />
                            <SelectInput
                                options={configurations.payoutMethodOptions}
                                label={translate(
                                    "my_withdrawals.labels.payout_method",
                                )}
                                value={
                                    formData.payout_method
                                        ? configurations.payoutMethodOptions.find(
                                              (opt) =>
                                                  opt.value ===
                                                  formData.payout_method,
                                          )
                                        : null
                                }
                                onChange={(option) =>
                                    updateFormData(
                                        "payout_method",
                                        option?.value || "",
                                    )
                                }
                                datarequired={true}
                                placeholder={translate(
                                    "my_withdrawals.placeholder.payout_method",
                                )}
                                error={errors?.payout_method}
                                menuPortalTarget={document.body}
                            />
                        </div>
                        <div className="modal-action gap-2">
                            <button
                                className="btn btn-primary btn-sm"
                                onClick={handleSaveClick}
                            >
                                {translate("common.save_btn")}
                            </button>
                        </div>
                    </form>
                </div>
            )}
        </div>
    );
}
