import { Trash2 } from "lucide-react";
import { convertToLocalTimezone } from "@/helpers/dateTimeUtils";
import { getStatusBadgeClass } from "@/helpers/badgeClassUtils";
import { Link } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

const WithdrawalCard = ({ withdrawal, onDeleteClick }) => {
    const { translate } = useSSRTranslations();

    return (
        <div className="card bg-base-100 shadow-md border border-base-300">
            <div className="card-body p-5">
                <div className="flex justify-between items-start flex-wrap gap-2 border-b pb-3 mb-2">
                    <div className="flex flex-col min-[400px]:flex-row min-[400px]:items-center gap-2">
                        <h2 className="text-base sm:text-lg font-semibold">
                            {translate("my_withdrawals.labels.withdrawal")} #
                            {withdrawal.withdraw_no}
                        </h2>
                        <span
                            className={`px-3 py-1 rounded-full text-sm border w-fit ${getStatusBadgeClass(withdrawal.status.color)}`}
                        >
                            {withdrawal.status.label}
                        </span>
                    </div>
                    {withdrawal.status.value === "pending" && (
                        <div className="flex items-center gap-2">
                            <button
                                className="rounded-md bg-error/10 text-error hover:bg-error/20 p-2 transition"
                                onClick={() => onDeleteClick(withdrawal)}
                            >
                                <Trash2 className="w-4 h-4" />
                            </button>
                        </div>
                    )}
                </div>

                <div className="flex grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm text-base-content/70 text-center sm:text-left">
                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.amount")} :
                        </span>{" "}
                        €{withdrawal.amount}
                    </p>
                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.bank_name")} :
                        </span>{" "}
                        {withdrawal.payout_method.bank_name}
                    </p>
                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.account_number")}{" "}
                            :
                        </span>{" "}
                        {withdrawal.payout_method.account_number}
                    </p>
                    {withdrawal.payment_reference_number && (
                        <p>
                            <span className="font-medium text-base-content">
                                {translate(
                                    "my_withdrawals.labels.payment_reference",
                                )}{" "}
                                # :
                            </span>{" "}
                            {withdrawal.payment_reference_number}
                        </p>
                    )}
                    {withdrawal.approved_at && (
                        <p>
                            <span className="font-medium text-base-content">
                                {translate("my_withdrawals.labels.approved_at")}
                                :
                            </span>{" "}
                            {convertToLocalTimezone(
                                withdrawal.approved_at,
                            ).toFormattedDateTime()}
                        </p>
                    )}
                    {withdrawal.paid_at && (
                        <p>
                            <span className="font-medium text-base-content">
                                {translate("my_withdrawals.labels.paid_at")}:
                            </span>{" "}
                            {convertToLocalTimezone(
                                withdrawal.paid_at,
                            ).toFormattedDateTime()}
                        </p>
                    )}
                    <p>
                        <span className="font-medium text-base-content">
                            {translate("my_withdrawals.labels.created_at")}:
                        </span>{" "}
                        {convertToLocalTimezone(
                            withdrawal.created_at,
                        ).toFormattedDateTime()}
                    </p>
                </div>

                {withdrawal.note && (
                    <div className="flex gap-3 text-sm text-base-content/70 text-center sm:text-left">
                        <p>
                            <span className="font-medium text-base-content">
                                {translate("my_withdrawals.labels.note")}:
                            </span>{" "}
                            {withdrawal.note}
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default WithdrawalCard;
