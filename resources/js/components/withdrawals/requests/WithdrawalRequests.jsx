import React, { useState, useEffect } from "react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useWithdrawals from "@/hooks/useWithdrawals";
import WithdrawalFilters from "./WithdrawalFilters";
import WithdrawalCard from "./WithdrawalCard";
import AddWithdrawalModal from "./AddWithdrawalModal";
import DeleteWithdrawalModal from "./DeleteWithdrawalModal";
import toast from "react-hot-toast";

export default function WithdrawalRequests() {
    const { translate } = useSSRTranslations();

    const [showAddWithdrawalModal, setShowAddWithdrawalModal] = useState(false);
    const [selectedWithdrawal, setSelectedWithdrawal] = useState(null);

    const {
        withdrawals,
        isLoading,
        filterChanged,
        getWithdrawals,
        nextPageUrl,
        loadMoreWithdrawals,
        configurations,
        getConfigurationsData,
    } = useWithdrawals();

    useEffect(() => {
        getConfigurationsData();
        getWithdrawals();
    }, []);

    const [observerRef] = useInfiniteScroll({
        loading: isLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreWithdrawals,
        rootMargin: "100px",
    });

    const handleAddWithdrwalClick = async () => {
        try {
            const { data } = await axios.get(
                route("api.withdrawals.check-can-withdraw"),
            );
            if (data.success) {
                if (data.can_withdraw) {
                    setShowAddWithdrawalModal(true);
                } else {
                    toast.error(data.withdraw_message);
                }
            }
        } catch (error) {
            toast.error(translate("common.something_wrong"));
        }
    };

    const handleDeleteClick = (withdrawal) => {
        setSelectedWithdrawal(withdrawal);
    };

    return (
        <>
            {configurations.balance >= 0 && (
                <h2 className="flex justify-end text-lg font-semibold mb-2">
                    {translate("my_withdrawals.balance_text")}:{" "}
                    <span
                        className={`ml-2 ${configurations.balance < 0 ? "text-red-600" : "text-green-600"}`}
                    >
                        €{Number(configurations.balance).toFixed(2)}
                    </span>
                </h2>
            )}
            <div className="flex justify-between items-center border-b pb-3 mb-6">
                <h1 className="text-md sm:text-xl font-bold">
                    {translate("my_withdrawals.withdarwal_tab_title")}
                </h1>
                <div
                    className="btn btn-sm btn-primary"
                    onClick={handleAddWithdrwalClick}
                >
                    {translate("my_withdrawals.withdrawal_add_btn")}
                </div>
            </div>

            <WithdrawalFilters />

            <div className="space-y-4 max-h-[70vh] overflow-y-auto my-5">
                {!isLoading && withdrawals.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                        <h2 className="text-2xl font-semibold text-base-content">
                            {translate("my_withdrawals.no_withdrawals")}
                        </h2>
                        <p className="text-gray-500 mt-2 max-w-md text-center">
                            {translate(
                                "my_withdrawals.no_transactions_details",
                            )}
                        </p>
                    </div>
                ) : (
                    <>
                        {filterChanged ? (
                            <p className="flex items-center justify-center h-64">
                                <span className="loading loading-bars loading-xl"></span>
                            </p>
                        ) : (
                            <>
                                {withdrawals.map((withdrawal) => (
                                    <WithdrawalCard
                                        key={withdrawal.id}
                                        withdrawal={withdrawal}
                                        onDeleteClick={handleDeleteClick}
                                    />
                                ))}

                                {nextPageUrl && (
                                    <div
                                        ref={observerRef}
                                        className="h-10"
                                    ></div>
                                )}
                            </>
                        )}

                        {isLoading && !filterChanged && (
                            <p className="flex items-center justify-center h-64">
                                <span className="loading loading-bars loading-xl"></span>
                            </p>
                        )}
                    </>
                )}
            </div>

            <AddWithdrawalModal
                open={showAddWithdrawalModal}
                onClose={() => setShowAddWithdrawalModal(false)}
                configurations={configurations}
            />
            <DeleteWithdrawalModal
                open={!!selectedWithdrawal}
                onClose={() => setSelectedWithdrawal(null)}
                withdrawal={selectedWithdrawal}
            />
        </>
    );
}
