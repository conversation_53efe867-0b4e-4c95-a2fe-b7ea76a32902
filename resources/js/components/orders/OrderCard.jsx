import React, { forwardRef } from "react";
import { Link } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { formatDate } from "@/helpers/formatUtils";
import { Calendar, Eye, Download, MapPin } from "lucide-react";
import { formatCurrency } from "@/helpers/formatUtils";
import { getStatusBadgeClass } from "@/helpers/badgeClassUtils";
import EventDateTime from "@/components/eventdetails/EventDateTime";

const OrderCard = forwardRef(({ order }, ref) => {
    const { translate } = useSSRTranslations();

    return (
        <div
            ref={ref}
            className="overflow-hidden bg-white rounded-xl border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-lg hover:border-blue-300 max-w-4xl mx-auto"
        >
            {/* Header with Order Info and Status */}
            <div className="p-4 sm:p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                        <h2 className="text-lg sm:text-xl font-bold text-gray-900">
                            {translate("order.order_number", "Order")} #
                            {order.order_no}
                        </h2>
                        <span
                            className={`self-start px-3 py-1 rounded-full text-xs font-semibold border ${getStatusBadgeClass(order.status.color)}`}
                        >
                            {order.status.label}
                        </span>
                    </div>
                    <div className="text-left sm:text-right">
                        <div className="text-xl sm:text-2xl font-bold text-gray-900">
                            {formatCurrency(order.total_price, order.currency)}
                        </div>
                        <div className="text-sm text-gray-500">
                            {order.quantity}{" "}
                            {order.quantity === 1
                                ? translate("ticket.ticket_text", "ticket")
                                : translate("ticket.tickets_text", "tickets")}
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content Area */}
            <div className="p-4 sm:p-6">
                <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
                    {/* Event Image */}
                    <div className="flex-shrink-0 w-full lg:w-40">
                        <div className="relative group">
                            <figure className="overflow-hidden relative w-full h-48 lg:h-32 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg">
                                {order.ticket.event?.image ? (
                                    <img
                                        src={order.ticket.event.image}
                                        alt={
                                            order.ticket.event?.image_alt ||
                                            order.ticket.event?.name ||
                                            translate(
                                                "events.event_image",
                                                "Event Image",
                                            )
                                        }
                                        className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                                    />
                                ) : (
                                    <div className="flex justify-center items-center h-full">
                                        <img
                                            className="w-48 opacity-60"
                                            src="/img/ticketgol-logo.png"
                                            alt={
                                                order.ticket.event?.name ||
                                                translate(
                                                    "events.event_image",
                                                    "Event Image",
                                                )
                                            }
                                        />
                                    </div>
                                )}
                            </figure>
                        </div>
                    </div>

                    {/* Event & Order Details */}
                    <div className="flex-1 space-y-4">
                        {/* Event Information */}
                        <div>
                            <div className="flex justify-between">
                                <h3 className="mb-3 text-lg sm:text-xl font-semibold text-gray-900 line-clamp-2">
                                    {order.ticket.event?.name ||
                                        translate(
                                            "common.not_available",
                                            "N/A",
                                        )}
                                </h3>

                                {order.ticket.type && (
                                    <span
                                        className={`badge badge-outline capitalize badge-primary`}
                                    >
                                        {order.ticket.type}
                                    </span>
                                )}
                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                                {order.ticket.event?.date &&
                                    order.ticket.event?.time && (
                                        <div className="flex gap-2 items-start">
                                            <Calendar className="w-4 h-4 text-gray-500 flex-shrink-0 mt-0.5" />
                                            <div className="text-gray-600">
                                                <EventDateTime
                                                    event={order.ticket.event}
                                                />
                                            </div>
                                        </div>
                                    )}

                                {order.ticket.event?.stadium && (
                                    <div className="flex gap-2 items-start">
                                        <MapPin className="w-4 h-4 text-gray-500 flex-shrink-0 mt-0.5" />
                                        <div className="text-gray-600">
                                            <div className="text-xs font-medium tracking-wide text-gray-500 uppercase mb-1">
                                                {translate(
                                                    "stadiums.stadium",
                                                    "Stadium",
                                                )}
                                            </div>
                                            <span className="line-clamp-2">
                                                {
                                                    order.ticket.event.stadium
                                                        .name
                                                }
                                            </span>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Teams Information */}
                            {(order.ticket.event?.home_club ||
                                order.ticket.event?.guest_club) && (
                                <div className="pt-4 mt-4 border-t border-gray-200">
                                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
                                        {order.ticket.event?.home_club && (
                                            <div className="text-center flex-1">
                                                <div className="mb-1 text-xs text-gray-600 uppercase tracking-wide">
                                                    {translate(
                                                        "common.home",
                                                        "Home",
                                                    )}
                                                </div>
                                                <div className="font-semibold text-blue-600 text-sm sm:text-base line-clamp-1">
                                                    {
                                                        order.ticket.event
                                                            .home_club
                                                    }
                                                </div>
                                            </div>
                                        )}
                                        {order.ticket.event?.home_club &&
                                            order.ticket.event?.guest_club && (
                                                <div className="text-lg sm:text-xl font-bold text-gray-400 px-2">
                                                    VS
                                                </div>
                                            )}
                                        {order.ticket.event?.guest_club && (
                                            <div className="text-center flex-1">
                                                <div className="mb-1 text-xs text-gray-600 uppercase tracking-wide">
                                                    {translate(
                                                        "common.guest",
                                                        "Guest",
                                                    )}
                                                </div>
                                                <div className="font-semibold text-red-600 text-sm sm:text-base line-clamp-1">
                                                    {
                                                        order.ticket.event
                                                            .guest_club
                                                    }
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Order Date */}
                        <div className="flex gap-2 items-center text-sm text-gray-600 pt-2">
                            <Calendar className="w-4 h-4 flex-shrink-0" />
                            <span>
                                {order.purchase_date ? (
                                    <>
                                        {translate(
                                            "order.purchased",
                                            "Purchased",
                                        )}
                                        : {formatDate(order.purchase_date)}
                                    </>
                                ) : (
                                    <>
                                        {translate(
                                            "order.created_at",
                                            "Created",
                                        )}
                                        : {formatDate(order.created_at)}
                                    </>
                                )}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 pt-4 mt-4 border-t border-gray-200">
                    <Link
                        href={route("my-account.order-detail", {
                            id: order.id,
                        })}
                        className="flex-1 inline-flex justify-center items-center gap-2 px-4 py-3 text-sm font-medium text-white bg-blue-600 rounded-lg transition-colors duration-200 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                        <Eye className="w-4 h-4" />
                        {translate("order.view_details", "View Details")}
                    </Link>
                </div>
            </div>
        </div>
    );
});

OrderCard.displayName = "OrderCard";

export default OrderCard;
