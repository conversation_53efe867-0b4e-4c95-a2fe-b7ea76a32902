import React from "react";
import { Search, Filter } from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useOrderStatuses from "@/hooks/useOrderStatuses";
import SelectInput from "@/components/forms/SelectInput";
import TextInput from "@/components/forms/TextInput";
import DatePicker from "@/components/forms/DatePicker";
import useOrders from "@/hooks/useOrders";

const OrderFilters = ({ filters, isLoading }) => {
    const { translate } = useSSRTranslations();
    const { setFilter, fetchOrders, clearFilters } = useOrders();
    const { statuses: statusOptions, isLoading: statusesLoading } =
        useOrderStatuses();

    const handleSearch = (e) => {
        e.preventDefault();
        fetchOrders();
    };

    return (
        <form onSubmit={handleSearch}>
            <div className="mb-6 border-b pb-5">
                <div className="flex flex-col gap-4 mb-4 md:flex-row">
                    {/* Search Bar */}
                    <div className="flex-1">
                        <div className="relative">
                            <TextInput
                                type="text"
                                value={filters.search}
                                onChange={(e) =>
                                    setFilter("search", e.target.value)
                                }
                                placeholder={translate(
                                    "orders.search",
                                    "Search orders...",
                                )}
                                className="pr-10"
                            />
                            <button
                                type="submit"
                                className="absolute right-2 top-1/2 text-gray-500 transform -translate-y-1/2 hover:text-primary"
                            >
                                <Search size={18} className="mr-3" />
                            </button>
                        </div>
                    </div>

                    {/* Status Filter */}
                    <div className="w-full md:w-1/3">
                        <SelectInput
                            options={statusOptions}
                            value={statusOptions.find(
                                (opt) => opt.value === filters.status,
                            )}
                            onChange={(option) =>
                                setFilter("status", option?.value || "")
                            }
                            placeholder={translate(
                                "order.filter_by_status",
                                "Filter by Status",
                            )}
                            isLoading={statusesLoading}
                            menuPortalTarget={
                                typeof document !== "undefined"
                                    ? document.body
                                    : null
                            }
                        />
                    </div>
                </div>

                {/* Date Filter */}
                <div className="flex flex-col gap-4 mb-4 md:flex-row">
                    <div className="flex-1">
                        <DatePicker
                            value={filters.date_from}
                            onChange={(value) => setFilter("date_from", value)}
                            placeholder={translate(
                                "order.select_date_from",
                                "Select from date",
                            )}
                            maxDate={filters.date_to || undefined}
                        />
                    </div>
                    <div className="flex-1">
                        <DatePicker
                            value={filters.date_to}
                            onChange={(value) => setFilter("date_to", value)}
                            placeholder={translate(
                                "order.select_date_to",
                                "Select to date",
                            )}
                            minDate={filters.date_from || undefined}
                        />
                    </div>
                </div>

                {/* Filter Actions */}
                <div className="flex flex-col md:flex-row gap-6 justify-end">
                    <button
                        onClick={clearFilters}
                        className="btn btn-outline btn-sm"
                        disabled={isLoading}
                    >
                        {translate("common.clear", "Clear")}
                    </button>
                    <button
                        type="submit"
                        className="btn btn-primary btn-sm"
                        disabled={isLoading}
                    >
                        <Filter size={16} className="mr-1" />
                        {translate("common.apply_filters", "Apply Filters")}
                    </button>
                </div>
            </div>
        </form>
    );
};

export default OrderFilters;
