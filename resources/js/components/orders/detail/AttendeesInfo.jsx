import React from "react";
import InfoCard from "@/components/utils/InfoCard";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { formatDate } from "@/helpers/formatUtils";

function AttendeesInfo({ attendees }) {
    const { translate } = useSSRTranslations();

    return (
        <InfoCard
            title={translate("order.attendees.title", "Attendees Information")}
            icon={
                <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                </svg>
            }
        >
            <div className="overflow-x-auto">
                <table className="overflow-hidden min-w-full rounded-lg divide-y divide-gray-200">
                    <thead className="text-black">
                        <tr>
                            <th className="px-6 py-4 text-xs font-medium tracking-wider text-left uppercase">
                                {translate("order.attendees.name", "Name")}
                            </th>
                            <th className="px-6 py-4 text-xs font-medium tracking-wider text-left uppercase">
                                {translate("order.attendees.email", "Email")}
                            </th>
                            <th className="px-6 py-4 text-xs font-medium tracking-wider text-left uppercase">
                                {translate("order.attendees.gender", "Gender")}
                            </th>
                            <th className="px-6 py-4 text-xs font-medium tracking-wider text-left uppercase">
                                {translate(
                                    "order.attendees.dob",
                                    "Date of Birth",
                                )}
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {attendees.map((attendee, index) => (
                            <tr
                                key={attendee.id || index}
                                className="transition-colors duration-200 hover:bg-blue-50"
                            >
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex gap-2 items-center">
                                        <div className="flex justify-center items-center w-8 h-8 bg-blue-100 rounded-full">
                                            <span className="text-sm font-medium text-blue-600">
                                                {attendee.name
                                                    ?.charAt(0)
                                                    ?.toUpperCase()}
                                            </span>
                                        </div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {attendee.name}
                                        </div>
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-500">
                                        {attendee.email}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {attendee.gender}
                                    </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-500">
                                        {attendee.dob
                                            ? formatDate(attendee.dob)
                                            : "N/A"}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </InfoCard>
    );
}

export default AttendeesInfo;
