import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import InfoCard from "@/components/utils/InfoCard";
import InfoItem from "@/components/utils/InfoItem";
import { Building, MapPin, MailOpen, Globe } from "lucide-react";

function StadiumInfo({ stadium }) {
    const { translate } = useSSRTranslations();

    return (
        <InfoCard title={translate("stadiums.info", "Stadium Information")}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <InfoItem
                    label={translate("stadiums.name", "Stadium Name")}
                    value={stadium.name}
                    highlight={true}
                    icon={<Building size={18} />}
                />
                <InfoItem
                    label={translate("stadiums.address", "Address")}
                    value={
                        <>
                            {stadium.address_line_1}
                            {stadium.address_line_2 ? "," : ""}
                            {stadium.address_line_2}
                        </>
                    }
                    icon={<MapPin size={18} />}
                />
                <InfoItem
                    label={translate("stadiums.postcode", "Postcode")}
                    value={stadium.postcode}
                    icon={<MailOpen size={18} />}
                />
                <InfoItem
                    label={translate("stadiums.country", "Country")}
                    value={stadium.country}
                    icon={<Globe size={18} />}
                />
            </div>
        </InfoCard>
    );
}

export default StadiumInfo;
