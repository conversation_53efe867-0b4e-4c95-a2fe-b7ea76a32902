import React, { useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import InfoCard from "@/components/utils/InfoCard";
import { formatCurrency, formatDate } from "@/helpers/formatUtils";
import { getStatusBadgeClass } from "@/helpers/badgeClassUtils";
import { CircleAlert, Download } from "lucide-react";
import DownloadTicketsModal from "./DownloadTicketsModal";
import DisputeOrderModal from "./DisputeOrderModal";

function OrderSummary({ order }) {
    const { translate } = useSSRTranslations();
    const [showDownloadModal, setShowDownloadModal] = useState(false);
    const [downloadMarked, setDownloadMarked] = useState(
        !!order.ticket_downloaded_at,
    );
    const [showDisputeModal, setShowDisputeModal] = useState(false);

    const handleDownloadPopup = async () => {
        if (!downloadMarked) {
            try {
                const { data } = await axios.post(
                    route("api.orders.mark-tickets-downloaded"),
                    { order_id: order.id },
                );
                if (data.success) {
                    setDownloadMarked(true);
                }
            } catch (error) {
                console.log(error);
            }
        }
        setShowDownloadModal(true);
    };

    const handleDownloadInvoice = () => {
        // Check if window is available (not during SSR)
        if (typeof window !== "undefined") {
            window.open(
                route("api.orders.download-invoice", { orderId: order.id }),
                "_blank",
            );
        }
    };

    return (
        <>
            <InfoCard title={translate("order.summary_title", "Order Summary")}>
                <div className="flex gap-3 items-center mb-5">
                    <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
                        #{order.order_no}
                    </h2>
                    <span
                        className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeClass(order.status.color)}`}
                    >
                        {order.status.label}
                    </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Left Column - Order Info */}
                    <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                        <h3 className="mb-4 font-medium text-gray-800 text-lg border-b border-gray-200 pb-2">
                            {translate(
                                "order.price_breakdown",
                                "Price Breakdown",
                            )}
                        </h3>

                        <div className="space-y-3">
                            {/* Unit Price */}
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">
                                    {translate(
                                        "order.unit_price",
                                        "Unit Price",
                                    )}
                                </span>
                                <span className="font-medium">
                                    {formatCurrency(order.price)}
                                </span>
                            </div>

                            {/* Quantity */}
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">
                                    {translate("order.quantity", "Quantity")}
                                </span>
                                <span className="font-medium">
                                    {order.quantity}
                                </span>
                            </div>

                            {/* Subtotal (Price × Quantity) */}
                            <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                                <span className="text-sm font-medium text-gray-700">
                                    {translate("order.subtotal", "Subtotal")}
                                </span>
                                <span className="font-medium">
                                    {formatCurrency(order.total_price)}
                                </span>
                            </div>

                            {/* Service Charge */}
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">
                                    {translate(
                                        "order.labels.service_charge",
                                        "Service Charge",
                                    )}
                                </span>
                                <span className="font-medium">
                                    {formatCurrency(
                                        order.service_charge_amount || 0,
                                    )}
                                </span>
                            </div>

                            {/* Tax (if applicable) */}
                            {order.tax_amount > 0 && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-600">
                                        {translate("order.tax", "Tax")}
                                    </span>
                                    <span className="font-medium">
                                        {formatCurrency(order.tax_amount)}
                                    </span>
                                </div>
                            )}

                            {order.refund_amount > 0 && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-600">
                                        {translate(
                                            "order.labels.refund",
                                            "Refund",
                                        )}
                                    </span>
                                    <span className="font-medium">
                                        -{formatCurrency(order.refund_amount)}
                                    </span>
                                </div>
                            )}

                            {/* Grand Total */}
                            <div className="flex justify-between items-center pt-3 mt-2 border-t border-gray-300">
                                <span className="font-medium text-gray-800">
                                    {translate(
                                        "ticket.grand_total_text",
                                        "Grand Total",
                                    )}
                                </span>
                                <span className="text-xl font-bold text-green-600">
                                    {formatCurrency(
                                        order.grand_total - order.refund_amount,
                                    )}
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Right Column - Price Details */}
                    <div className="space-y-4">
                        <div className="flex flex-wrap justify-between gap-4 text-sm text-gray-600">
                            <span>
                                <strong>
                                    {translate("order.created_at", "Created")}:
                                </strong>{" "}
                                {formatDate(order.created_at)}
                            </span>
                            {order.purchase_date && (
                                <span>
                                    <strong>
                                        {translate(
                                            "order.purchase_date",
                                            "Purchased",
                                        )}
                                        :
                                    </strong>{" "}
                                    {formatDate(order.purchase_date)}
                                </span>
                            )}
                        </div>

                        {order.latestStatusChange.reason && (
                            <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                                <span>
                                    <strong>Status Change Reason:</strong>{" "}
                                    <span
                                        dangerouslySetInnerHTML={{
                                            __html: order.latestStatusChange
                                                .reason,
                                        }}
                                    ></span>
                                </span>
                            </div>
                        )}

                        {/* Ticket Information - Highlighted */}
                        {order.ticket?.ticket_type && (
                            <div className="flex flex-col gap-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div className="flex gap-2 items-center">
                                    <span className="text-sm font-medium text-blue-700">
                                        {translate(
                                            "order.ticket_type",
                                            "Ticket Type",
                                        )}
                                        :
                                    </span>
                                    <span className="text-lg font-bold text-blue-900">
                                        {order.ticket.ticket_type}
                                    </span>
                                </div>
                            </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex flex-wrap gap-3 mt-4">
                            <button
                                className="btn btn-outline btn-sm btn-primary w-full min-[530px]:w-48"
                                onClick={handleDownloadInvoice}
                            >
                                <Download className="w-4 h-4" />
                                {translate(
                                    "order.download_invoice",
                                    "Download Invoice",
                                )}
                            </button>
                            {(order.status.value === "shipped" ||
                                order.status.value === "completed") && (
                                <button
                                    className="btn btn-outline btn-sm text-info w-full min-[530px]:w-48 hover:text-white hover:bg-info border-info hover:border-info"
                                    onClick={handleDownloadPopup}
                                >
                                    <Download className="w-4 h-4" />
                                    {translate(
                                        "order.download_ticket",
                                        "Download Tickets",
                                    )}
                                </button>
                            )}

                            {order.status.value === "shipped" ||
                                (order.status.value === "canceled" && (
                                    <button
                                        onClick={() =>
                                            setShowDisputeModal(true)
                                        }
                                        className="btn btn-outline btn-sm w-full min-[530px]:w-48 text-warning hover:text-white hover:bg-warning border-warning hover:border-warning"
                                    >
                                        <CircleAlert className="w-4 h-4" />
                                        {translate(
                                            "order.open_dispute",
                                            "Open Dispute",
                                        )}
                                    </button>
                                ))}
                        </div>
                    </div>
                </div>
            </InfoCard>

            <DisputeOrderModal
                open={showDisputeModal}
                onClose={() => setShowDisputeModal(false)}
                order={order}
            />

            <DownloadTicketsModal
                open={showDownloadModal}
                onClose={() => setShowDownloadModal(false)}
                order={order}
            />
        </>
    );
}

export default OrderSummary;
