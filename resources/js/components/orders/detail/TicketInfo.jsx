import React from "react";
import InfoCard from "@/components/utils/InfoCard";
import InfoItem from "@/components/utils/InfoItem";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { formatCurrency } from "@/helpers/formatUtils";
import {
    Ticket,
    Tag,
    Banknote,
    DollarSign,
    Hash,
    Blocks,
    Rows3,
    Armchair,
    FileText,
    User,
    Mail,
    Phone,
    Building,
    Building2,
    Globe,
} from "lucide-react";

function TicketInfo({ ticket }) {
    const { translate } = useSSRTranslations();
    return (
        <InfoCard title={translate("ticket.info", "Ticket Information")}>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                <InfoItem
                    label={translate("ticket.type", "Ticket Type")}
                    value={ticket.ticket_type}
                    icon={<Tag size={18} />}
                />
                <InfoItem
                    label={translate("ticket.price", "Price")}
                    value={
                        ticket.price ? `${formatCurrency(ticket.price)}` : null
                    }
                    highlight={true}
                    icon={<Banknote size={18} />}
                />
                <InfoItem
                    label={translate("ticket.quantity", "Quantity")}
                    value={ticket.quantity}
                    icon={<Hash size={18} />}
                />
                {ticket.sector && (
                    <InfoItem
                        label={translate("ticket.sector", "Sector")}
                        value={ticket.sector.name}
                        highlight={true}
                        icon={<Blocks size={18} />}
                    />
                )}
                {ticket.ticket_rows && (
                    <InfoItem
                        label={translate("ticket.rows", "Rows")}
                        value={ticket.ticket_rows}
                        highlight={true}
                        icon={<Rows3 size={18} />}
                    />
                )}
                {ticket.ticket_seats && (
                    <InfoItem
                        label={translate("ticket.seats", "Seats")}
                        value={ticket.ticket_seats}
                        highlight={true}
                        icon={<Armchair size={18} />}
                    />
                )}
                {ticket.description && (
                    <InfoItem
                        label={translate("ticket.description", "Description")}
                        value={ticket.description}
                        className="lg:col-span-3"
                        icon={<FileText size={18} />}
                    />
                )}
            </div>

            {/* Seller Information */}
            {ticket.seller && (
                <div className="pt-6 mt-6 border-t border-gray-200">
                    <h4 className="flex gap-2 items-center mb-4 font-semibold text-gray-900">
                        <span>
                            <User size={18} />
                        </span>
                        {translate(
                            "order.details.ticket.seller_info",
                            "Seller Information",
                        )}
                    </h4>
                    <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <InfoItem
                                label={translate(
                                    "ticket.seller_name",
                                    "Seller Name",
                                )}
                                value={ticket.seller.name}
                                icon={<User size={18} />}
                            />
                            <InfoItem
                                label={translate(
                                    "ticket.seller_email",
                                    "Seller Email",
                                )}
                                value={ticket.seller.email}
                                icon={<Mail size={18} />}
                            />
                            {ticket.seller.phone && (
                                <InfoItem
                                    label={translate(
                                        "ticket.seller_phone",
                                        "Phone",
                                    )}
                                    value={ticket.seller.phone}
                                    icon={<Phone size={18} />}
                                />
                            )}
                            {ticket.seller.company && (
                                <InfoItem
                                    label={translate(
                                        "ticket.seller_company",
                                        "Company",
                                    )}
                                    value={ticket.seller.company}
                                    icon={<Building size={18} />}
                                />
                            )}
                            {(ticket.seller.address ||
                                ticket.seller.city ||
                                ticket.seller.country) && (
                                <InfoItem
                                    label={translate(
                                        "ticket.seller_address",
                                        "Address",
                                    )}
                                    value={
                                        [
                                            ticket.seller.address &&
                                                `${ticket.seller.address}, `,
                                            ticket.seller.city &&
                                                `${ticket.seller.city}, `,
                                            ticket.seller.country,
                                        ]
                                            .filter(Boolean)
                                            .join("")
                                            .replace(/,\s*$/, "") // Remove trailing comma if exists
                                    }
                                    icon={<MapPin size={18} />}
                                />
                            )}
                            {ticket.seller.description && (
                                <InfoItem
                                    label={translate(
                                        "ticket.seller_description",
                                        "Description",
                                    )}
                                    value={ticket.seller.description}
                                    icon={<FileText size={18} />}
                                />
                            )}
                        </div>
                    </div>
                </div>
            )}
        </InfoCard>
    );
}

export default TicketInfo;
