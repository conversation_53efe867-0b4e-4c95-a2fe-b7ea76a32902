import { useState } from "react";
import { <PERSON>Alert } from "lucide-react";
import axios from "axios";
import toast from "react-hot-toast";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useValidation from "@/hooks/useValidation";
import TextInput from "@/components/forms/TextInput";

export default function DisputeOrderModal({ open, order, onClose }) {
    const { translate } = useSSRTranslations();

    const [reason, setReason] = useState("");
    const [actionDisabled, setActionDisabled] = useState(false);

    const validationSchema = {
        reason: ["required", { rule: "maxlength", value: 400 }],
    };
    const { errors, validate, validateField } = useValidation(validationSchema);

    const handleOpenDispute = async () => {
        const isValid = validate({ reason });
        if (isValid) {
            const params = {
                order_id: order.id,
                status: "on_dispute",
                reason: reason,
            };

            setActionDisabled(true);
            try {
                const { data } = await axios.post(
                    route("api.orders.open-dispute"),
                    params,
                );
                if (data.success) {
                    window.location.reload();
                }
            } catch (error) {
                if (
                    error.response?.status === 422 &&
                    error.response.data?.errors
                ) {
                    toast.error(
                        Object.values(error.response.data?.errors)[0]?.[0],
                    );
                } else if (error.response?.status === 404) {
                    toast.error(error.response.data.message);
                } else {
                    toast.error(translate("common.something_wrong"));
                }
            } finally {
                setActionDisabled(false);
            }
        }
    };

    const handleOnClose = () => {
        setReason("");
        setActionDisabled(false);
        onClose();
    };

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            <div className="modal-box max-w-xl">
                <button
                    className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                    onClick={handleOnClose}
                >
                    ✕
                </button>
                <div className="flex justify-center mb-2">
                    <div className="bg-warning/20 text-warning rounded-full p-2">
                        <TriangleAlert className="w-6 h-6" />
                    </div>
                </div>
                <h3 className="text-lg font-bold text-center mb-2">
                    {translate("order.open_dispute", "Open Dispute")}
                </h3>
                <p className="text-center mb-6">
                    {translate("order.open_dispute_confirm_text")}
                </p>

                <div className="text-gray-500 text-sm mt-2">
                    <strong>{translate("my_sales.note_text")}</strong>{" "}
                    {translate("order.open_dispute_note")}
                </div>

                <TextInput
                    id="reason"
                    type="textarea"
                    value={reason}
                    label={translate(
                        "order.labels.dispute_reason",
                        "Dispute Reason",
                    )}
                    placeholder={translate(
                        "order.labels.dispute_reason_placeholder",
                    )}
                    datarequired="true"
                    onChange={(e) => {
                        setReason(e.target.value);
                        validateField("reason", e.target.value, { reason });
                    }}
                    onBlur={(e) =>
                        validateField("reason", e.target.value, { reason })
                    }
                    error={errors?.reason}
                />

                <div className="modal-action gap-2">
                    <button
                        onClick={handleOpenDispute}
                        className="btn btn-primary btn-sm"
                        disabled={actionDisabled}
                    >
                        {translate("order.open_dispute", "Open Dispute")}
                    </button>
                </div>
            </div>
        </div>
    );
}
