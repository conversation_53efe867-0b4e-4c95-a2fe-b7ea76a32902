import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import InfoCard from "@/components/utils/InfoCard";
import { AlertTriangle } from "lucide-react";

function RestrictionsInfo({ restrictions }) {
    const { translate } = useSSRTranslations();

    const getRestrictionBadgeClass = (type) => {
        switch (type) {
            case "event":
                return "bg-purple-100 text-purple-800 border-purple-200";
            case "ticket":
                return "bg-indigo-100 text-indigo-800 border-indigo-200";
        }
    };

    return (
        <InfoCard
            title={translate(
                "order.restrictions_title",
                "Event & Ticket Terms",
            )}
            icon={
                <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                </svg>
            }
        >
            <div className="space-y-4">
                {restrictions.map((restriction, index) => (
                    <div
                        key={restriction.id || index}
                        className="flex gap-4 items-start p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200 shadow-sm"
                    >
                        <div className="flex-shrink-0">
                            <span
                                className={`inline-flex capitalize items-center px-3 py-1 rounded-full text-xs font-medium border ${getRestrictionBadgeClass(
                                    restriction.type,
                                )}`}
                            >
                                {restriction.type_label || restriction.type}
                            </span>
                        </div>
                        <div className="flex-1">
                            <h5 className="mb-1 font-semibold text-gray-900">
                                {restriction.name}
                            </h5>
                        </div>
                        <div className="text-orange-500">
                            <AlertTriangle size={20} />
                        </div>
                    </div>
                ))}
            </div>
        </InfoCard>
    );
}

export default RestrictionsInfo;
