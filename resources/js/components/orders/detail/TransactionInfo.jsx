import React from "react";
import InfoCard from "@/components/utils/InfoCard";
import InfoItem from "@/components/utils/InfoItem";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { formatCurrency, formatDate } from "@/helpers/formatUtils";
import { CreditCard, Banknote, CheckCircle, RefreshCw } from "lucide-react";

function TransactionInfo({ transactions }) {
    const { translate } = useSSRTranslations();

    return (
        <InfoCard
            title={translate(
                "order.transactions.title",
                "Payment & Transaction Details",
            )}
        >
            <div className="space-y-6">
                {transactions.map((transaction, index) => (
                    <div
                        key={transaction.id || index}
                        className="p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-gray-200 shadow-sm"
                    >
                        <div className="flex justify-between items-center mb-4">
                            <h4 className="flex gap-2 items-center font-semibold text-gray-900">
                                <span>
                                    <CreditCard size={18} />
                                </span>
                                {translate(
                                    "order.transaction.title",
                                    "Transaction",
                                )}{" "}
                                #{index + 1}
                            </h4>
                        </div>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                            <InfoItem
                                label={`${translate("order.transaction.type", "Transaction Type")}`}
                                value={transaction.transaction_type.toUpperCase()}
                            />
                            <InfoItem
                                label={translate(
                                    "order.transaction.amount",
                                    "Amount",
                                )}
                                value={`${formatCurrency(transaction.total_amount, transaction.currency_code)}`}
                                highlight={true}
                                icon={<Banknote size={18} />}
                            />
                            {transaction.transaction_type === "purchase" && (
                                <>
                                    <InfoItem
                                        label={translate(
                                            "order.transaction.payment_method",
                                            "Payment Method",
                                        )}
                                        value={transaction.payment_method_type}
                                        icon={<CreditCard size={18} />}
                                    />
                                    <InfoItem
                                        label={translate(
                                            "order.transaction.paid_at",
                                            "Paid At",
                                        )}
                                        value={
                                            transaction.paid_at
                                                ? formatDate(
                                                      transaction.paid_at,
                                                  )
                                                : null
                                        }
                                        icon={<CheckCircle size={18} />}
                                    />
                                    {transaction.card_brand && (
                                        <InfoItem
                                            label={translate(
                                                "order.transaction.card_details",
                                                "Card Details",
                                            )}
                                            value={`${transaction.card_brand} **** ${transaction.card_last_four}`}
                                            icon={<CreditCard size={18} />}
                                        />
                                    )}
                                </>
                            )}
                            {transaction.transaction_type === "refund" && (
                                <InfoItem
                                    label={translate(
                                        "order.transaction.refunded_at",
                                        "Refunded At",
                                    )}
                                    value={formatDate(transaction.refunded_at)}
                                    icon={<RefreshCw size={18} />}
                                />
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </InfoCard>
    );
}

export default TransactionInfo;
