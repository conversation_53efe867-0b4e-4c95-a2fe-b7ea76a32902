import { Eye, MapPin } from "lucide-react";
import { formatDate } from "@/helpers/formatUtils";
import { getStatusBadgeClass } from "@/helpers/badgeClassUtils";
import { Link } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

const SalesOrderCard = ({ order }) => {
    const { translate } = useSSRTranslations();

    return (
        <div className="card bg-base-100 shadow-md border border-base-300">
            <div className="card-body p-5">
                <div className="flex justify-between items-start flex-wrap gap-2 border-b pb-3">
                    <div className="flex flex-col min-[400px]:flex-row min-[400px]:items-center gap-2">
                        <h2 className="text-base sm:text-lg font-semibold">
                            {translate("my_sales.labels.order")} #
                            {order.order_no}
                        </h2>
                        <span
                            className={`px-3 py-1 rounded-full text-sm border w-fit ${getStatusBadgeClass(order.status.color)}`}
                        >
                            {order.status.label}
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link
                            className="rounded-md bg-info/10 text-info hover:bg-info/20 p-2 transition"
                            href={route(
                                "my-account.sales.detail",
                                order.order_no,
                            )}
                        >
                            <Eye className="w-4 h-4" />
                        </Link>
                    </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 mt-2">
                    <figure className="relative h-20 bg-gray-200 object-cover rounded border border-base-300 mx-auto md:mx-0">
                        {order.event.image !== "" ? (
                            <img
                                src={order.event.image}
                                alt={
                                    order.event.image_alt?.alt ||
                                    order.event.name
                                }
                                className="w-24 h-full object-cover"
                            />
                        ) : (
                            <img
                                src="/img/ticketgol-logo.png"
                                alt={order.event.name}
                                className="w-24"
                            />
                        )}
                    </figure>
                    <div className="flex-1 space-y-2 text-sm text-base-content/70 text-center sm:text-left">
                        <p>
                            <span className="font-medium text-base-content">
                                {translate("my_sales.labels.event")}:
                            </span>{" "}
                            {order.event.name}
                        </p>
                        <p>
                            <span className="font-medium text-base-content">
                                {translate("my_sales.labels.venue")}:
                            </span>{" "}
                            {order.event.stadium.name}
                        </p>

                        <div className="flex grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("my_sales.labels.ticket_no")}:
                                </span>{" "}
                                #{order.ticket.ticket_no}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("my_sales.labels.tickets")}:
                                </span>{" "}
                                {order.quantity}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("my_sales.labels.total_price")}:
                                </span>{" "}
                                €{order.total_price}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("my_sales.labels.buyer_name")}:
                                </span>{" "}
                                {order.buyer.name}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("my_sales.labels.event_date")}:
                                </span>{" "}
                                {formatDate(order.event.date)}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("my_sales.labels.purchase_date")}
                                    :
                                </span>{" "}
                                {formatDate(order.purchase_date)}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SalesOrderCard;
