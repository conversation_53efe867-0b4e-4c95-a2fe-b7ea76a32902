import { useState } from "react";
import { Tickets } from "lucide-react";
import axios from "axios";
import toast from "react-hot-toast";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import FileUploadWithPreview from "@/components/forms/FileUploadWithPreview";

export default function ReUploadTicketsModal({ open, order, onClose }) {
    const { translate } = useSSRTranslations();

    const [ticketFiles, setTicketFiles] = useState([]);
    const [additionalDoc, setAdditionalDoc] = useState(null);
    const [actionDisabled, setActionDisabled] = useState(false);

    const handleUploadDocuments = async () => {
        const formData = new FormData();
        formData.append("order_id", order.id);

        if (ticketFiles.length) {
            ticketFiles.forEach((file, idx) => {
                formData.append(`tickets[${idx}]`, file);
            });
        }
        if (additionalDoc) {
            formData.append("additionalDoc", additionalDoc);
        }

        setActionDisabled(true);
        try {
            const { data } = await axios.post(
                route("api.orders.upload-tickets"),
                formData,
                {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                },
            );
            if (data.success) {
                window.location.reload();
            }
        } catch (error) {
            if (error.response?.status === 422 && error.response.data?.errors) {
                toast.error(Object.values(error.response.data?.errors)[0]?.[0]);
            } else if (error.response?.status === 404) {
                toast.error(error.response.data.message);
            } else {
                toast.error(translate("common.something_wrong"));
            }
        } finally {
            setActionDisabled(false);
        }
    };

    const handleOnClose = () => {
        setTicketFiles([]);
        setAdditionalDoc(null);
        setActionDisabled(false);
        onClose();
    };

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            <div className="modal-box max-w-2xl">
                <button
                    className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                    onClick={handleOnClose}
                >
                    ✕
                </button>
                <div className="flex justify-center mb-2">
                    <div className="bg-warning/20 text-warning rounded-full p-2">
                        <Tickets className="w-6 h-6" />
                    </div>
                </div>
                <h3 className="text-lg font-bold text-center mb-2">
                    {translate("my_sales.reupload_title_text")}
                </h3>

                <div className="mt-2">
                    <div className="text-gray-500 text-sm">
                        <strong>{translate("my_sales.note_text")}</strong>{" "}
                        {translate("my_sales.reupload_note_text")}
                    </div>
                    <div className="mt-4 space-y-4">
                        <FileUploadWithPreview
                            label={translate("my_sales.labels.upload_tickets")}
                            files={ticketFiles}
                            setFiles={setTicketFiles}
                            maxFiles={order.quantity}
                        />

                        <FileUploadWithPreview
                            label={translate("my_sales.labels.additional_doc")}
                            files={additionalDoc}
                            setFiles={setAdditionalDoc}
                            maxFiles={1}
                            multiple={false}
                        />
                    </div>
                </div>

                <div className="modal-action gap-2">
                    <button
                        onClick={handleUploadDocuments}
                        className="btn btn-primary btn-sm"
                        disabled={actionDisabled}
                    >
                        {translate("my_sales.upload_btn", "Upload")}
                    </button>
                </div>
            </div>
        </div>
    );
}
