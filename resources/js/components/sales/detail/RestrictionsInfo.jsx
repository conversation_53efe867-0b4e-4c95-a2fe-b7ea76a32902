import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import InfoCard from "@/components/utils/InfoCard";
import { AlertTriangle } from "lucide-react";

function RestrictionsInfo({ eventRestrictions, ticketRestrictions }) {
    const { translate } = useSSRTranslations();

    return (
        <InfoCard
            title={translate(
                "ticket.event_ticket_term_text",
                "Event & Ticket Terms",
            )}
            className="mt-5"
        >
            <div className="space-y-3">
                {eventRestrictions.length > 0 && (
                    <div className="pt-1">
                        <p className="font-bold mb-1 border-b pb-2">
                            {translate("ticket.event_term_text", "Event Terms")}
                        </p>
                        <ul className="gap-4 px-4 list-disc">
                            {eventRestrictions.map((restriction) => (
                                <li key={restriction.id}>{restriction.name}</li>
                            ))}
                        </ul>
                    </div>
                )}

                {ticketRestrictions.length > 0 && (
                    <div
                        className={
                            eventRestrictions.length > 0 ? `pt-3` : `pt-1`
                        }
                    >
                        <p className="font-bold mb-1 border-b pb-2">
                            {translate(
                                "ticket.ticket_term_text",
                                "Ticket Terms",
                            )}
                        </p>
                        <ul className="gap-4 px-4 list-disc">
                            {ticketRestrictions.map((restriction) => (
                                <li key={restriction.id}>{restriction.name}</li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
        </InfoCard>
    );
}

export default RestrictionsInfo;
