import React from "react";
import InfoCard from "@/components/utils/InfoCard";
import InfoItem from "@/components/utils/InfoItem";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { formatCurrency } from "@/helpers/formatUtils";
import {
    Ticket,
    Tag,
    DollarSign,
    Blocks,
    Rows3,
    Armchair,
    FileText,
} from "lucide-react";

function TicketInfo({ ticket }) {
    const { translate } = useSSRTranslations();
    return (
        <InfoCard
            title={translate("ticket.info", "Ticket Information")}
            className="mt-5"
        >
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                <InfoItem
                    label={translate("ticket.ticket_number", "Ticket Number")}
                    value={`#${ticket.ticket_no}`}
                    highlight={true}
                    icon={<Ticket size={18} />}
                />
                <InfoItem
                    label={translate("ticket.type", "Ticket Type")}
                    value={ticket.ticket_type?.label}
                    icon={<Tag size={18} />}
                />
                <InfoItem
                    label={translate("ticket.face_value_price", "Face Value")}
                    value={
                        ticket.face_value_price
                            ? `${formatCurrency(ticket.face_value_price)}`
                            : null
                    }
                    icon={<DollarSign size={18} />}
                />
                {ticket.sector && (
                    <InfoItem
                        label={translate("ticket.sector", "Sector")}
                        value={ticket.sector.name}
                        highlight={true}
                        icon={<Blocks size={18} />}
                    />
                )}
                {ticket.ticket_rows && (
                    <InfoItem
                        label={translate("ticket.rows", "Rows")}
                        value={ticket.ticket_rows}
                        highlight={true}
                        icon={<Rows3 size={18} />}
                    />
                )}
                {ticket.ticket_seats && (
                    <InfoItem
                        label={translate("ticket.seats", "Seats")}
                        value={ticket.ticket_seats}
                        highlight={true}
                        icon={<Armchair size={18} />}
                    />
                )}
                {ticket.description && (
                    <InfoItem
                        label={translate("ticket.description", "Description")}
                        value={ticket.description}
                        className="lg:col-span-3"
                        icon={<FileText size={18} />}
                    />
                )}
            </div>
        </InfoCard>
    );
}

export default TicketInfo;
