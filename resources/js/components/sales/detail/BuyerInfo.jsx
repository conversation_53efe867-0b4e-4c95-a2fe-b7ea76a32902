import React from "react";
import InfoCard from "@/components/utils/InfoCard";
import InfoItem from "@/components/utils/InfoItem";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { User, Mail, Phone, Home } from "lucide-react";

function BuyerInfo({ buyer }) {
    const { translate } = useSSRTranslations();

    return (
        <InfoCard
            title={translate(
                "order.details.buyer.information",
                "Buyer Information",
            )}
            className="mt-5"
        >
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                <InfoItem
                    label={translate("order.details.buyer.name", "Name")}
                    value={buyer.name}
                    icon={<User size={18} />}
                />
                <InfoItem
                    label={translate("order.details.buyer.email", "Email")}
                    value={buyer.email}
                    icon={<Mail size={18} />}
                />
                <InfoItem
                    label={translate("order.details.buyer.phone", "Phone")}
                    value={buyer.phone}
                    icon={<Phone size={18} />}
                />
                {buyer.address && (
                    <InfoItem
                        label={translate(
                            "order.details.buyer.address",
                            "Address",
                        )}
                        value={
                            <>
                                {buyer.address}
                                {buyer.city ? ", " : ""}
                                {buyer.city}
                                {buyer.country ? ", " : ""}
                                {buyer.country}
                                {", "}
                                {buyer.zip}
                            </>
                        }
                        className="lg:col-span-3"
                        icon={<Home size={18} />}
                    />
                )}
            </div>
        </InfoCard>
    );
}

export default BuyerInfo;
