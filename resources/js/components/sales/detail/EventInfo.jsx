import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import InfoCard from "@/components/utils/InfoCard";
import InfoItem from "@/components/utils/InfoItem";
import EventDateTime from "@/components/eventdetails/EventDateTime";
import { formatDate } from "@/helpers/formatUtils";
import {
    Target,
    CalendarDays,
    Clock,
    Globe,
    Flag,
    Trophy,
    Tag,
    Building,
    MapPin,
    Dribbble,
    House,
    Plane,
} from "lucide-react";

function EventInfo({ event }) {
    const { translate } = useSSRTranslations();

    return (
        <InfoCard
            title={translate("events.info", "Event Information")}
            className="mt-5"
        >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-1">
                    <div className="relative aspect-w-16 aspect-h-9 rounded-xl overflow-hidden bg-gray-100 shadow-lg">
                        {event.image ? (
                            <img
                                src={event.image || "/img/ticketgol-logo.png"}
                                alt={event.image_alt || event.name}
                                className="w-full h-48 object-cover rounded-xl"
                            />
                        ) : (
                            <img
                                src="/img/ticketgol-logo.png"
                                alt={event.name}
                                className="w-full h-48 object-contain object-center rounded-xl"
                            />
                        )}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>
                </div>

                <div className="lg:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <InfoItem
                            label={translate("events.name", "Event Name")}
                            value={event.name}
                            className="md:col-span-2"
                            highlight={true}
                            icon={<Target size={18} />}
                        />
                        <InfoItem
                            label={translate("events.category", "Category")}
                            value={event.category?.label}
                            icon={<Tag size={18} />}
                        />
                        <InfoItem
                            label={translate("events.date", "Date")}
                            value={<EventDateTime event={event} />}
                            icon={<CalendarDays size={18} />}
                            highlight={true}
                        />
                        <InfoItem
                            label={translate("stadiums.name", "Stadium Name")}
                            value={event.stadium.name}
                            highlight={true}
                            icon={<Building size={18} />}
                        />
                        <InfoItem
                            label={translate("event.venue", "Venue")}
                            value={
                                <>
                                    {event.stadium.address_line_1}
                                    {event.stadium.address_line_2 ? "," : ""}
                                    {event.stadium.address_line_2}
                                    {", "}
                                    {event.stadium.country}
                                    {", "}
                                    {event.stadium.postcode}
                                </>
                            }
                            highlight={true}
                            icon={<MapPin size={18} />}
                        />

                        {event.league && (
                            <InfoItem
                                label={translate("events.league", "League")}
                                value={event.league.name}
                                icon={<Trophy size={18} />}
                            />
                        )}
                    </div>

                    {(event.home_club || event.guest_club) && (
                        <div className="mt-6 pt-6 border-t border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {event.home_club && (
                                    <InfoItem
                                        label={translate(
                                            "events.home_club",
                                            "Home Club",
                                        )}
                                        value={event.home_club.name}
                                        icon={<House size={18} />}
                                    />
                                )}
                                {event.guest_club && (
                                    <InfoItem
                                        label={translate(
                                            "events.guest_club",
                                            "Guest Club",
                                        )}
                                        value={event.guest_club.name}
                                        icon={<Plane size={18} />}
                                    />
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </InfoCard>
    );
}

export default EventInfo;
