import useSSRTranslations from "@/hooks/useSSRTranslations";
import { Tickets, Download } from "lucide-react";
import FileListItem from "@/components/utils/FileListItem";

export default function ViewTicketsModal({ open, order, onClose }) {
    const { translate } = useSSRTranslations();

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            <div className="modal-box max-w-md">
                <button
                    className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                    onClick={onClose}
                >
                    ✕
                </button>

                <div className="flex justify-center mb-5">
                    <div className="bg-info/20 text-info rounded-full p-2">
                        <Tickets className="w-6 h-6" />
                    </div>
                </div>
                <h3 className="text-lg font-bold text-center mb-3">
                    {translate("my_sales.view_tickets_btn")}
                </h3>

                <h3 className="text-sm font-bold mb-2">
                    {translate("my_sales.labels.tickets", "Tickets")}
                </h3>
                <ul className="mt-4 space-y-3 max-h-[300px] overflow-auto">
                    {order.ticket_files.map((file, idx) => (
                        <FileListItem key={idx} file={file} />
                    ))}
                </ul>

                {order.additional_doc && order.additional_doc.length > 0 && (
                    <>
                        <h3 className="text-sm font-bold mt-5 mb-2">
                            {translate(
                                "my_sales.labels.additional_document",
                                "Additional Document",
                            )}
                        </h3>

                        <ul className="mt-4 space-y-3 max-h-[300px] overflow-auto">
                            {order.additional_doc.map((file, idx) => (
                                <FileListItem key={idx} file={file} />
                            ))}
                        </ul>
                    </>
                )}
            </div>
        </div>
    );
}
