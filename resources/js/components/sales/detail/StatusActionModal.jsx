import { useState } from "react";
import { TriangleAlert } from "lucide-react";
import axios from "axios";
import toast from "react-hot-toast";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import TextInput from "@/components/forms/TextInput";
import FileUploadWithPreview from "@/components/forms/FileUploadWithPreview";

export default function StatusActionModal({
    open,
    order,
    onClose,
    status,
    statusLabel,
}) {
    const { translate } = useSSRTranslations();

    const [reason, setReason] = useState("");
    const [ticketFiles, setTicketFiles] = useState([]);
    const [additionalDoc, setAdditionalDoc] = useState(null);
    const [actionDisabled, setActionDisabled] = useState(false);

    const needsReasonStatuses = ["canceled", "under_review", "on_dispute"];
    const showReasonField = needsReasonStatuses.includes(status);

    const handleUpdateStatus = async () => {
        if (showReasonField && reason === "") {
            toast.error(translate("my_sales.reason_validation_msg"));
            return;
        }

        if (status === "shipped" && ticketFiles.length === 0) {
            toast.error(translate("my_sales.tickets_validation_msg"));
            return;
        }

        const formData = new FormData();
        formData.append("order_id", order.id);
        formData.append("status", status);
        formData.append("reason", reason);

        if (ticketFiles.length) {
            ticketFiles.forEach((file, idx) => {
                formData.append(`tickets[${idx}]`, file);
            });
        }
        if (additionalDoc) {
            formData.append("additionalDoc", additionalDoc);
        }

        setActionDisabled(true);
        try {
            const { data } = await axios.post(
                route("api.orders.update-status"),
                formData,
                {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                },
            );
            if (data.success) {
                window.location.reload();
            }
        } catch (error) {
            if (error.response?.status === 422 && error.response.data?.errors) {
                toast.error(Object.values(error.response.data?.errors)[0]?.[0]);
            } else if (error.response?.status === 404) {
                toast.error(error.response.data.message);
            } else {
                toast.error(translate("common.something_wrong"));
            }
        } finally {
            setActionDisabled(false);
        }
    };

    const handleOnClose = () => {
        setReason("");
        setTicketFiles([]);
        setAdditionalDoc(null);
        setActionDisabled(false);
        onClose();
    };

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            {status && (
                <div
                    className={`modal-box ${status === "confirmed" ? "max-w-sm" : "max-w-2xl"}`}
                >
                    <button
                        className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                        onClick={handleOnClose}
                    >
                        ✕
                    </button>
                    <div className="flex justify-center mb-2">
                        <div className="bg-warning/20 text-warning rounded-full p-2">
                            <TriangleAlert className="w-6 h-6" />
                        </div>
                    </div>
                    <h3 className="text-lg font-bold text-center mb-2">
                        {translate("my_sales.update_status_text")}
                    </h3>
                    <p className="text-center mb-6">
                        {translate("my_sales.update_status_confirm_text")}{" "}
                        {statusLabel}?
                    </p>

                    {status === "canceled" && (
                        <div className="text-gray-500 text-sm mt-2">
                            <strong>{translate("my_sales.note_text")}</strong>{" "}
                            {translate("my_sales.cancellation_note")}
                        </div>
                    )}

                    {status === "under_review" && (
                        <div className="text-gray-500 text-sm mt-2">
                            <strong>{translate("my_sales.note_text")}</strong>{" "}
                            {translate("my_sales.under_review_note")}
                        </div>
                    )}

                    {status === "on_dispute" && (
                        <div className="text-gray-500 text-sm mt-2">
                            <strong>{translate("my_sales.note_text")}</strong>{" "}
                            {translate("my_sales.on_dispute_note")}
                        </div>
                    )}

                    {showReasonField && (
                        <TextInput
                            id="reason"
                            type="textarea"
                            value={reason}
                            label={translate("my_sales.labels.reason")}
                            placeholder={translate(
                                "my_sales.labels.reason_placeholer",
                            )}
                            datarequired="true"
                            onChange={(e) => setReason(e.target.value)}
                        />
                    )}

                    {status === "shipped" && (
                        <div className="mt-2">
                            <div className="text-gray-500 text-sm">
                                <strong>
                                    {translate("my_sales.note_text")}
                                </strong>{" "}
                                {translate("my_sales.shipped_note")}
                            </div>
                            <div className="mt-4 space-y-4">
                                <FileUploadWithPreview
                                    label={translate(
                                        "my_sales.labels.upload_tickets",
                                    )}
                                    files={ticketFiles}
                                    setFiles={setTicketFiles}
                                    maxFiles={order.quantity}
                                />

                                <FileUploadWithPreview
                                    label={translate(
                                        "my_sales.labels.additional_doc",
                                    )}
                                    files={additionalDoc}
                                    setFiles={setAdditionalDoc}
                                    maxFiles={1}
                                    multiple={false}
                                />
                            </div>
                        </div>
                    )}

                    <div className="modal-action gap-2">
                        <button
                            onClick={handleUpdateStatus}
                            className="btn btn-primary btn-sm"
                            disabled={actionDisabled}
                        >
                            {translate("my_sales.mark_as_text", "Mark as")}{" "}
                            {statusLabel}
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}
