import React, { useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import InfoCard from "@/components/utils/InfoCard";
import { formatDate } from "@/helpers/formatUtils";
import { getStatusBadgeClass } from "@/helpers/badgeClassUtils";

function OrderSummary({ order }) {
    const { translate } = useSSRTranslations();

    return (
        <>
            <InfoCard
                title={translate("my_sales.order_summary_title")}
                className="mt-4"
            >
                <div className="flex flex-col gap-4 text-sm sm:text-base">
                    <div className="space-y-2">
                        <div className="flex flex-col sm:flex-row sm:justify-between pb-1 gap-2">
                            <span>
                                <b>{translate("my_sales.labels.ticket_no")}:</b>{" "}
                                #{order.ticket.ticket_no}
                            </span>
                            <span>
                                <b>
                                    {translate("my_sales.labels.order_status")}
                                    :{" "}
                                </b>
                                <span
                                    className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeClass(order.status.color)}`}
                                >
                                    {order.status.label}
                                </span>
                            </span>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:justify-between pb-1 gap-2">
                            <span>
                                <b>
                                    {translate("my_sales.labels.purchase_date")}
                                    :
                                </b>{" "}
                                {formatDate(order.purchase_date)}
                            </span>
                            <span>
                                <b>
                                    {translate(
                                        "my_sales.labels.payment_status",
                                    )}
                                    :{" "}
                                </b>
                                <span
                                    className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadgeClass(
                                        order.payment_status.color,
                                    )}`}
                                >
                                    {order.payment_status.label}
                                </span>
                            </span>
                        </div>
                        {order.latestStatusChange.reason && (
                            <div className="flex flex-col sm:flex-row sm:justify-between pb-1 gap-2">
                                <span>
                                    <b>
                                        {translate(
                                            "my_sales.labels.status_change_reason",
                                            "Status Change Reason",
                                        )}
                                        :
                                    </b>{" "}
                                    <span
                                        dangerouslySetInnerHTML={{
                                            __html: order.latestStatusChange
                                                .reason,
                                        }}
                                    ></span>
                                </span>
                            </div>
                        )}
                    </div>
                    <div className="bg-base-200 rounded-lg p-4 mt-2">
                        <div className="flex justify-between text-sm sm:text-base border-b pb-2 mb-2">
                            <span>
                                {translate("my_sales.labels.total_tickets")}
                            </span>
                            <span>
                                {order.quantity} x €{order.price}
                            </span>
                        </div>
                        <div className="flex justify-between mb-1">
                            <span>
                                {translate("my_sales.labels.total_price")}
                            </span>
                            <span>€{order.total_price}</span>
                        </div>
                        <div className="flex justify-between mb-1">
                            <span>
                                {translate(
                                    "ticket.service_charge_text",
                                    "Service Charge",
                                )}
                            </span>
                            <span>€{order.service_charge_amount}</span>
                        </div>
                        <div className="flex justify-between mb-2 border-b pb-2">
                            <span>{translate("ticket.tax_text", "Tax")}</span>
                            <span>€{order.tax_amount}</span>
                        </div>
                        <div className="flex justify-between font-semibold text-base mt-2">
                            <span>
                                {translate(
                                    "ticket.grand_total_text",
                                    "Grand Total",
                                )}
                            </span>
                            <span>€{order.grand_total}</span>
                        </div>
                    </div>
                    <div className="bg-base-200 rounded-lg p-4 mt-2">
                        <div className="flex justify-between">
                            <span>
                                {translate("my_sales.labels.your_earning")}
                            </span>
                            {order.status.value === "canceled" ? (
                                <span>€0.00</span>
                            ) : (
                                <span>€{order.total_price}</span>
                            )}
                        </div>
                        {order.penalty_amount !== 0 && (
                            <div className="flex justify-between mt-2">
                                <span>
                                    {translate(
                                        "my_sales.labels.order_cancel_penalty",
                                    )}
                                </span>
                                <span>-€{order.penalty_amount}</span>
                            </div>
                        )}
                        <div className="flex justify-between font-semibold text-base border-t pt-2 mt-2">
                            <span>
                                {translate(
                                    "my_sales.labels.your_total_earning",
                                )}
                            </span>
                            {order.status.value === "canceled" ? (
                                order.penalty_amount > 0 ? (
                                    <span>-€{order.penalty_amount}</span>
                                ) : (
                                    <span>€0.00</span>
                                )
                            ) : (
                                <span>€{order.total_price}</span>
                            )}
                        </div>
                    </div>
                </div>
            </InfoCard>
        </>
    );
}

export default OrderSummary;
