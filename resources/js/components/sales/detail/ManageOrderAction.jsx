import { useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import StatusActionModal from "@/components/sales/detail/StatusActionModal";
import ViewTicketsModal from "@/components/sales/detail/ViewTicketsModal";
import ReUploadTicketsModal from "@/components/sales/detail/ReUploadTicketsModal";

function ManageOrderAction({ order }) {
    const { translate } = useSSRTranslations();

    const [selectedStatus, setSelectedStatus] = useState(null);
    const [viewTickets, setViewTickets] = useState(false);
    const [reuploadTickets, setReuploadTickets] = useState(false);

    const handleUpdateStatusClick = (status) => {
        setSelectedStatus(status);
    };

    const handleViewTicketsClick = () => {
        setViewTickets(true);
    };

    const handleReuploadTicketsClick = () => {
        setReuploadTickets(true);
    };

    const closeActionModal = () => {
        setSelectedStatus(null);
    };

    const closeViewTicketsModal = () => {
        setViewTickets(false);
    };

    const closeReuploadTicketsModal = () => {
        setReuploadTickets(false);
    };

    return (
        <div className="flex flex-col sm:flex-row sm:justify-end mt-4 gap-2">
            {Object.keys(order.status_actions).length > 0 && (
                <>
                    <div className="dropdown dropdown-bottom dropdown-end">
                        <div
                            tabIndex={0}
                            role="button"
                            className="btn btn-outline btn-sm btn-primary w-full sm:w-auto"
                        >
                            {translate(
                                "my_sales.update_status_btn",
                                "Update Order Status",
                            )}
                        </div>
                        <ul
                            tabIndex={0}
                            className="dropdown-content menu bg-base-200 rounded-box z-1 w-52 p-2 shadow-sm"
                        >
                            {Object.entries(order.status_actions).map(
                                ([value, label]) => (
                                    <li key={value}>
                                        <a
                                            onClick={() =>
                                                handleUpdateStatusClick(value)
                                            }
                                        >
                                            {translate(
                                                "my_sales.mark_as_text",
                                                "Mark as",
                                            )}{" "}
                                            {label}
                                        </a>
                                    </li>
                                ),
                            )}
                        </ul>
                    </div>
                    <StatusActionModal
                        open={!!selectedStatus}
                        order={order}
                        onClose={closeActionModal}
                        status={selectedStatus}
                        statusLabel={order.status_actions[selectedStatus]}
                    />
                </>
            )}

            {["shipped", "on_dispute", "completed"].includes(
                order.status.value,
            ) && (
                <>
                    <div
                        className="btn btn-outline btn-sm w-full sm:w-auto text-info hover:text-white hover:bg-info border-info hover:border-info"
                        onClick={handleViewTicketsClick}
                    >
                        {translate("my_sales.view_tickets_btn", "View Tickets")}
                    </div>
                    <ViewTicketsModal
                        open={!!viewTickets}
                        order={order}
                        onClose={closeViewTicketsModal}
                    />
                </>
            )}

            {!order.ticket_downloaded_at &&
                order.status.value === "shipped" && (
                    <>
                        <div
                            className="btn btn-outline btn-sm w-full sm:w-auto text-warning hover:text-white hover:bg-warning border-warning hover:border-warning"
                            onClick={handleReuploadTicketsClick}
                        >
                            {translate(
                                "my_sales.reupload_tickets_btn",
                                "Re-upload Tickets",
                            )}
                        </div>
                        <ReUploadTicketsModal
                            open={!!reuploadTickets}
                            order={order}
                            onClose={closeReuploadTicketsModal}
                        />
                    </>
                )}
        </div>
    );
}

export default ManageOrderAction;
