import React from "react";
import InfoCard from "@/components/utils/InfoCard";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { formatDate } from "@/helpers/formatUtils";

function AttendeesInfo({ attendees }) {
    const { translate } = useSSRTranslations();

    return (
        <InfoCard
            title={translate("order.attendees.title", "Attendees Information")}
            className="mt-5"
        >
            <div className="overflow-x-auto">
                <table className="overflow-hidden min-w-full rounded-lg divide-y divide-gray-200">
                    <thead className="text-black">
                        <tr>
                            <th className="px-5 py-4 text-xs font-medium tracking-wider text-left uppercase">
                                {translate("order.attendees.name", "Name")}
                            </th>
                            <th className="px-5 py-4 text-xs font-medium tracking-wider text-left uppercase">
                                {translate("order.attendees.email", "Email")}
                            </th>
                            <th className="px-5 py-4 text-xs font-medium tracking-wider text-left uppercase">
                                {translate("order.attendees.gender", "Gender")}
                            </th>
                            <th className="px-5 py-4 text-xs font-medium tracking-wider text-right uppercase">
                                {translate(
                                    "order.attendees.dob",
                                    "Date of Birth",
                                )}
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {attendees.map((attendee, index) => (
                            <tr
                                key={attendee.id || index}
                                className="transition-colors duration-200 hover:bg-blue-50"
                            >
                                <td className="px-5 py-4 whitespace-nowrap">
                                    <div className="flex gap-2 items-center">
                                        <div className="text-sm font-medium text-gray-900">
                                            {attendee.name}
                                        </div>
                                    </div>
                                </td>
                                <td className="px-5 py-4 whitespace-nowrap">
                                    {attendee.email}
                                </td>
                                <td className="px-5 py-4 whitespace-nowrap capitalize">
                                    {attendee.gender}
                                </td>
                                <td className="px-5 py-4 whitespace-nowrap text-right">
                                    {attendee.dob
                                        ? formatDate(attendee.dob)
                                        : "N/A"}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </InfoCard>
    );
}

export default AttendeesInfo;
