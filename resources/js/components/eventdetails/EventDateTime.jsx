import useSSRTranslations from "@/hooks/useSSRTranslations";
import {
    convertToTimezone,
    convertToLocalTimezone,
} from "@/helpers/dateTimeUtils";

export default function EventDateTime({ event }) {
    const { translate } = useSSRTranslations();

    const timeInBaseTimezone = convertToTimezone(event.time, event.timezone);
    const timeInUserTimezone = convertToLocalTimezone(event.time);

    const isSameTz =
        timeInBaseTimezone.getShortTimezoneName() ===
        timeInUserTimezone.getShortTimezoneName();

    return (
        <span>
            <b>
                {convertToLocalTimezone(event.date).toDateString()} |{" "}
                {timeInUserTimezone.toTimeString("h:mm A")}{" "}
                {timeInUserTimezone.getShortTimezoneName()}
            </b>
            {!isSameTz && (
                <i>
                    {" "}
                    ({timeInBaseTimezone.toTimeString("h:mm A")}{" "}
                    {timeInBaseTimezone.getShortTimezoneName()}{" "}
                    {translate("common.local", "Local")})
                </i>
            )}
        </span>
    );
}
