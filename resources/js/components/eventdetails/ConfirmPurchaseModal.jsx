import { ClockAlert } from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function ConfirmPurchaseModal({
    open,
    onClose,
    onConfirm,
    btnDisabled,
    tempReservationMinutes,
}) {
    const { translate } = useSSRTranslations();
    const message = translate("events.confirm_purchase_desc").replace(
        "{{minutes}}",
        tempReservationMinutes,
    );

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            <div className="modal-box max-w-sm">
                <button
                    className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                    onClick={onClose}
                >
                    ✕
                </button>
                <div className="flex justify-center mb-4">
                    <ClockAlert className="w-12 h-12 text-primary" />
                </div>
                <h3 className="text-lg font-bold text-center mb-2">
                    {translate("events.confirm_purchase_txt")}
                </h3>
                <p className="text-center mb-6">{message}</p>

                <div className="modal-action gap-2">
                    <button
                        onClick={onConfirm}
                        disabled={btnDisabled}
                        className="btn btn-primary btn-sm"
                    >
                        {translate("events.proceed_btn")}
                    </button>
                </div>
            </div>
        </div>
    );
}
