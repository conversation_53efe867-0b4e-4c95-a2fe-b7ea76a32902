import { useState, useEffect } from "react";
import { router, usePage } from "@inertiajs/react";
import axios from "axios";
import toast from "react-hot-toast";
import useEventDetail from "@/hooks/useEventDetail";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import SelectInput from "@/components/forms/SelectInput";
import ConfirmPurchaseModal from "@/components/eventdetails/ConfirmPurchaseModal";
import ActiveReservationModal from "@/components/eventdetails/ActiveReservationModal";

export default function BuyTicketModal({ open, onClose, ticket }) {
    const { auth } = usePage().props;
    const { event, filters } = useEventDetail();
    const { translate } = useSSRTranslations();

    const isVisible = open && ticket;

    const ticketsOptions =
        ticket && event
            ? Array.from({ length: ticket.remain_qty }, (_, i) => {
                  const quantity = i + 1;
                  const remaining = ticket.remain_qty - quantity;
                  const isValid = (() => {
                      switch (ticket.quantity_split_type) {
                          case event.quantitySplitEnums.SINGLE:
                              return quantity === 1;
                          case event.quantitySplitEnums.AVOID_ONE:
                              return remaining !== 1;
                          case event.quantitySplitEnums.AVOID_ODD:
                              return remaining % 2 === 0;
                          case event.quantitySplitEnums.AVOID_ONE_THREE:
                              return remaining !== 1 && remaining !== 3;
                          case event.quantitySplitEnums.ANY:
                          default:
                              return true;
                      }
                  })();
                  return isValid
                      ? {
                            label: `${quantity} ${translate("events.tickets")}`,
                            value: quantity,
                        }
                      : null;
              }).filter(Boolean)
            : [];

    const defaultSelected =
        ticketsOptions.find((opt) => opt.value === filters.quantity)?.value ??
        ticketsOptions.at(-1)?.value ??
        null;

    const [purchaseQuantity, setPurchaseQuantity] = useState(defaultSelected);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [showActiveReservationModal, setShowActiveReservationModal] =
        useState(false);
    const [reservationId, setReservationId] = useState(null);
    const [btnDisabled, setBtnDisabled] = useState(false);
    const [formErrors, setFormErrors] = useState({});

    useEffect(() => {
        setPurchaseQuantity(defaultSelected);
        setFormErrors({});
    }, [defaultSelected]);

    const handlePurchaseQuantityChange = (e) => {
        setPurchaseQuantity(parseInt(e.value));
    };

    const handleBuyNowClick = async () => {
        if (!auth.user) {
            const { url } = usePage();
            return router.visit(route("login", { redirect: url }));
        }

        try {
            const { data } = await axios.get(
                route("api.reservations.check-active"),
            );
            if (data.success) {
                if (data.is_active) {
                    setShowActiveReservationModal(true);
                    setReservationId(data.reservationId);
                } else {
                    setShowConfirmModal(true);
                }
            }
        } catch (error) {
            toast.error(translate("common.something_wrong"));
        }
    };

    const handleConfirmPurchase = async () => {
        const params = {
            ticket_id: ticket.id,
            quantity: purchaseQuantity,
            price: ticket.price,
        };
        setBtnDisabled(true);
        try {
            const { data } = await axios.post(
                route("api.reservations.create"),
                params,
            );
            if (data.success) {
                router.visit(
                    route("ticket.checkout", data.temp_ticket_reservation_id),
                );
            }
        } catch (error) {
            setBtnDisabled(false);
            if (error.response?.status === 422 && error.response.data?.errors) {
                setFormErrors(error.response.data?.errors);
            } else if (error.response?.status === 400) {
                toast.error(error.response.data.message);
            } else {
                toast.error(translate("common.something_wrong"));
            }
            setShowConfirmModal(false);
        }
    };

    return (
        <>
            <div className={`modal ${isVisible ? "modal-open" : ""}`}>
                <div className="modal-box max-w-sm">
                    {ticket && (
                        <>
                            <button
                                className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                                onClick={onClose}
                            >
                                ✕
                            </button>
                            <h2 className="mb-2 text-xl font-bold">
                                {ticket.sector_name}
                            </h2>
                            <p
                                className={`badge badge-outline capitalize badge-${ticket.ticket_type.color}`}
                            >
                                {ticket.ticket_type.label}
                            </p>
                            <div className="divider"></div>
                            <div className="mb-4">
                                <p className="mb-2 font-semibold">
                                    {translate("events.ticket_price_text")}
                                </p>
                                <p className="mb-4 text-cyan-600">
                                    €{ticket.price}
                                </p>
                                <SelectInput
                                    label={translate(
                                        "events.quantity_placeholder",
                                    )}
                                    options={ticketsOptions}
                                    value={ticketsOptions.find(
                                        (option) =>
                                            option.value === purchaseQuantity,
                                    )}
                                    onChange={handlePurchaseQuantityChange}
                                    error={formErrors?.quantity}
                                    menuPortalTarget={
                                        typeof document !== "undefined"
                                            ? document.body
                                            : null
                                    }
                                />
                            </div>
                            <div className="modal-action gap-2">
                                <button
                                    onClick={handleBuyNowClick}
                                    className="btn btn-primary btn-sm"
                                >
                                    {translate("events.buy_now")}
                                </button>
                            </div>
                        </>
                    )}
                </div>
            </div>

            <ConfirmPurchaseModal
                open={showConfirmModal}
                onClose={() => setShowConfirmModal(false)}
                onConfirm={handleConfirmPurchase}
                btnDisabled={btnDisabled}
                tempReservationMinutes={event.tempReservationMinutes || 15}
            />

            {reservationId && (
                <ActiveReservationModal
                    open={showActiveReservationModal}
                    onClose={() => setShowActiveReservationModal(false)}
                    reservationId={reservationId}
                />
            )}
        </>
    );
}
