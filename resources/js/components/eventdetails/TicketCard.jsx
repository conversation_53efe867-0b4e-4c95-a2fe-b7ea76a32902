export default function TicketCard({ ticket, ticketsText, onBuyClick }) {
    return (
        <div className="card bg-base-100 shadow-sm border p-4 cursor-pointer">
            <div
                className="flex justify-between items-center"
                onClick={() => onBuyClick(ticket)}
            >
                <div>
                    <p className="font-medium mb-1">{ticket.sector_name}</p>
                    <p className="text-sm text-gray-500 mb-1">
                        {ticket.remain_qty} {ticketsText}
                    </p>
                    <p
                        className={`badge badge-outline capitalize badge-${ticket.ticket_type.color}`}
                    >
                        {ticket.ticket_type.label}
                    </p>
                </div>
                <p className="text-lg font-bold">€{ticket.price}</p>
            </div>
        </div>
    );
}
