import { Calendar, MapPin, ShieldCheck, House, HousePlus } from "lucide-react";
import { <PERSON> } from "@inertiajs/react";
import EventDateTime from "@/components/eventdetails/EventDateTime";

export default function EventDetailSection({ event }) {
    return (
        <div className="px-5 mb-5">
            <h2 className="mt-5 text-2xl font-bold leading-tight">
                {event.name}
                <span className="ml-4 badge badge-dash badge-neutral badge-outline badge-lg">
                    {event.category.label}
                </span>
            </h2>

            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700">
                <Calendar className="w-4 h-4" />
                <EventDateTime event={event} />
            </div>
            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                <MapPin className="w-4 h-4" />
                <Link href={route("detail.show", event.stadium.slug)}>
                    <span>
                        <b>{event.stadium.name},</b>{" "}
                        {event.stadium.address_line_1},{" "}
                        {event.stadium.address_line_2},{" "}
                        {event.stadium.country.name}, {event.stadium.postcode}
                    </span>
                </Link>
            </div>
            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                <ShieldCheck className="w-4 h-4" />
                <Link href={route("detail.show", event.league.slug)}>
                    <span className="font-bold">{event.league.name}</span>
                </Link>
            </div>
            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                <House className="w-4 h-4" />
                <Link href={route("detail.show", event.home_club.slug)}>
                    <span className="font-bold">{event.home_club.name}</span>
                </Link>
            </div>
            <div className="mt-3 flex items-center gap-2 text-sm text-gray-700 hover:underline">
                <HousePlus className="w-4 h-4" />
                <Link href={route("detail.show", event.guest_club.slug)}>
                    <span className="font-bold">{event.guest_club.name}</span>
                </Link>
            </div>
        </div>
    );
}
