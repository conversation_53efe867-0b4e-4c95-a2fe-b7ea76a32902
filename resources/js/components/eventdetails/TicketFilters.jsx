import useEventDetail from "@/hooks/useEventDetail";
import RangeSlider from "@/components/forms/RangeSlider";
import SelectInput from "@/components/forms/SelectInput";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

export default function TicketFilters({ event }) {
    const { translate } = useSSRTranslations();
    const { filters, updateTicketFilter, clearTicketFilters } =
        useEventDetail();

    const quantityOptions = [
        { label: translate("events.all_tickets"), value: "" },
        ...Array.from({ length: event.max_quantity }, (_, i) => ({
            label: `${i + 1} ${translate("events.tickets")}`,
            value: i + 1,
        })),
    ];

    const sectorOptions = [
        { label: translate("events.all_sectors"), value: "" },
        ...prepareOptionsFromEnum(event.stadium_sectors),
    ];

    const ticketTypeOptions = [
        { label: translate("events.all_ticket_types"), value: "" },
        ...prepareOptionsFromEnum(translate("enums.ticket_types")),
    ];

    return (
        <div className="relative bg-white p-4 shadow flex rounded-xl flex-wrap gap-4">
            <div className="w-full md:w-auto flex-1 min-w-[180px] pr-5">
                <RangeSlider
                    key={0}
                    values={filters.priceRange}
                    onChange={(values) =>
                        updateTicketFilter("priceRange", values)
                    }
                    min={0}
                    max={event.max_price}
                    step={1}
                />
            </div>
            <div className="w-full md:w-auto flex-1 min-w-[180px]">
                <SelectInput
                    options={quantityOptions}
                    value={quantityOptions.find(
                        (opt) => opt.value === filters.quantity,
                    )}
                    onChange={(option) =>
                        updateTicketFilter("quantity", option?.value || "")
                    }
                    placeholder={translate("events.quantity_placeholder")}
                    menuPortalTarget={
                        typeof document !== "undefined" ? document.body : null
                    }
                />
            </div>
            <div className="w-full md:w-auto flex-1 min-w-[180px]">
                <SelectInput
                    options={sectorOptions}
                    value={sectorOptions.find(
                        (opt) => opt.value === filters.sector,
                    )}
                    onChange={(option) =>
                        updateTicketFilter("sector", option?.value || "")
                    }
                    placeholder={translate("events.sector_placeholder")}
                    menuPortalTarget={
                        typeof document !== "undefined" ? document.body : null
                    }
                />
            </div>
            <div className="w-full md:w-auto flex-1 min-w-[180px]">
                <SelectInput
                    options={ticketTypeOptions}
                    value={ticketTypeOptions.find(
                        (opt) => opt.value === filters.ticketType,
                    )}
                    onChange={(option) =>
                        updateTicketFilter("ticketType", option?.value || "")
                    }
                    placeholder={translate("events.ticket_type_placeholder")}
                    menuPortalTarget={
                        typeof document !== "undefined" ? document.body : null
                    }
                />
            </div>
            <div className="w-full md:w-auto flex-none">
                <button
                    className="btn btn-primary mt-2"
                    onClick={clearTicketFilters}
                >
                    {translate("events.reset_filters")}
                </button>
            </div>
        </div>
    );
}
