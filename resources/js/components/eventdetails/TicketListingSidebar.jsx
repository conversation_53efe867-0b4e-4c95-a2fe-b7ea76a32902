import { useEffect, useState } from "react";
import { Link, usePage } from "@inertiajs/react";
import SelectInput from "@/components/forms/SelectInput";
import TicketCard from "@/components/eventdetails/TicketCard";
import BuyTicketModal from "@/components/eventdetails/BuyTicketModal";
import useInfiniteScroll from "react-infinite-scroll-hook";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useEventDetail from "@/hooks/useEventDetail";

export default function TicketListingSidebar() {
    const { translate } = useSSRTranslations();
    const { slug } = usePage().props;

    const [selectedTicket, setSelectedTicket] = useState(null);

    const {
        tickets,
        filters,
        getEventTickets,
        ticketsLoading,
        totalTickets,
        nextPageUrl,
        loadMoreEventTickets,
        updateTicketFilter,
        clearNextPageUrl,
    } = useEventDetail();

    useEffect(() => {
        if (ticketsLoading) return;

        clearNextPageUrl();
        getEventTickets();
    }, [filters]);

    const [observerRef] = useInfiniteScroll({
        loading: ticketsLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreEventTickets,
        rootMargin: "100px",
    });

    const handleBuyClick = (ticket) => {
        setSelectedTicket(ticket);
    };

    const closeBuyModal = () => {
        setSelectedTicket(null);
    };

    return (
        <>
            <div className="lg:w-2/5 w-full bg-base-100 shadow-md p-6 rounded-xl space-y-4 md:max-h-screen md:overflow-y-auto md:sticky md:top-[120px]">
                <div className="flex justify-end w-full">
                    <Link
                        href={route("ticket.sell", slug)}
                        className="btn btn-outline btn-primary btn-sm float-right"
                    >
                        {translate("events.sell_tickets_btn", "Sell Tickets")}
                    </Link>
                </div>
                <div className="flex items-center justify-between w-full">
                    <span className="font-medium mt-2">
                        {translate("events.total_listing")} : {totalTickets}
                    </span>

                    <div className="flex items-center gap-2 min-w-[200px]">
                        <span className="whitespace-nowrap mt-2 max-xl:hidden">
                            {translate("events.sort_by_placeholder")}
                        </span>
                        <div className="flex-1">
                            {(() => {
                                const sortOptions = translate(
                                    "events.tickets_sort_options",
                                );
                                const isValidOptions =
                                    Array.isArray(sortOptions);

                                return (
                                    <SelectInput
                                        options={
                                            isValidOptions ? sortOptions : []
                                        }
                                        value={
                                            filters.sort && isValidOptions
                                                ? sortOptions.find(
                                                      (option) =>
                                                          option.value ===
                                                          filters.sort,
                                                  )
                                                : null
                                        }
                                        onChange={(selected) =>
                                            updateTicketFilter(
                                                "sort",
                                                selected.value,
                                            )
                                        }
                                        placeholder=""
                                        menuPortalTarget={
                                            typeof document !== "undefined"
                                                ? document.body
                                                : null
                                        }
                                    />
                                );
                            })()}
                        </div>
                    </div>
                </div>
                <div className="space-y-4 max-h-[600px] overflow-y-auto pr-2">
                    {!ticketsLoading && tickets.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-96 bg-base-100 rounded-xl shadow-sm">
                            <h2 className="text-2xl font-semibold text-base-content">
                                {translate("events.no_tickets")}
                            </h2>
                            <p className="text-gray-500 mt-2 max-w-md text-center">
                                {translate("events.no_tickets_details")}
                            </p>
                        </div>
                    ) : (
                        <>
                            {tickets.map((ticket) => (
                                <TicketCard
                                    ticket={ticket}
                                    key={ticket.id}
                                    ticketsText={translate("events.tickets")}
                                    onBuyClick={handleBuyClick}
                                />
                            ))}
                            {nextPageUrl && (
                                <div ref={observerRef} className="h-10"></div>
                            )}

                            {ticketsLoading && (
                                <p className="flex items-center justify-center h-32">
                                    <span className="loading loading-bars loading-xl"></span>
                                </p>
                            )}
                        </>
                    )}
                </div>
            </div>
            <BuyTicketModal
                open={!!selectedTicket}
                onClose={closeBuyModal}
                ticket={selectedTicket}
            />
        </>
    );
}
