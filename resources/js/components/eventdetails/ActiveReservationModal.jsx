import { Clock } from "lucide-react";
import { Link } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function ActiveReservationModal({
    open,
    onClose,
    reservationId,
}) {
    const { translate } = useSSRTranslations();

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            <div className="modal-box max-w-sm">
                <button
                    className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                    onClick={onClose}
                >
                    ✕
                </button>
                <div className="flex justify-center mb-4">
                    <Clock className="w-12 h-12 text-primary" />
                </div>
                <h3 className="text-lg font-bold text-center mb-2">
                    {translate("events.transaction_in_progress_title")}
                </h3>
                <p className="text-center mb-6">
                    {translate("events.transaction_in_progress_message")}
                </p>

                <div className="modal-action gap-2">
                    <Link
                        href={route("ticket.checkout", reservationId)}
                        className="btn btn-primary btn-sm"
                    >
                        {translate("events.continue_btn")}
                    </Link>
                </div>
            </div>
        </div>
    );
}
