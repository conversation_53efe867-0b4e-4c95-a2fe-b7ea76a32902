import { Download } from "lucide-react";

export default function FileListItem({ file }) {
    const isImageFile = (file) => file.mime_type?.startsWith("image/");
    const isPdfFile = (file) => file.mime_type === "application/pdf";

    return (
        <li className="flex items-center justify-between border rounded p-2 bg-gray-50">
            <div className="flex items-center gap-3">
                {isImageFile(file) ? (
                    <img
                        src={file.original_url}
                        alt={file.name}
                        className="w-12 h-12 object-cover rounded border"
                    />
                ) : isPdfFile(file) ? (
                    <div className="w-12 h-12 flex items-center justify-center bg-red-100 text-red-500 rounded font-semibold text-xs">
                        PDF
                    </div>
                ) : (
                    <div className="w-12 h-12 flex items-center justify-center bg-gray-200 text-gray-500 rounded font-semibold text-xs">
                        FILE
                    </div>
                )}

                <span className="text-sm text-gray-700 truncate max-w-[120px]">
                    {file.name}
                </span>
            </div>

            <a
                href={file.original_url}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-sm btn-outline btn-neutral flex items-center"
                download
            >
                <Download className="w-4 h-4" />
            </a>
        </li>
    );
}
