import React, { useEffect } from "react";
import { usePage } from "@inertiajs/react";
import toast, { Toaster } from "react-hot-toast";

function FlashToast() {
    const { flash } = usePage().props;

    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }

        if (flash.error) {
            toast.error(flash.error);
        }
    }, [flash.success, flash.error]);

    return (
        <Toaster
            position="top-right"
            duration={2000}
            containerStyle={{
                top: 100,
                left: 20,
                bottom: 20,
                right: 50,
            }}
        />
    );
}

export default FlashToast;
