import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function InfoItem({ label, value, className = "", icon, highlight = false }) {
    const { translate } = useSSRTranslations();

    return (
        <div className={`${className}`}>
            <p className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-2">
                {icon && (
                    <span
                        className={`${highlight ? "text-primary-600" : "text-gray-500"}`}
                    >
                        {icon}
                    </span>
                )}
                {label}:
            </p>
            <p
                className={`font-medium ${highlight ? "text-primary-600" : "text-gray-900"}`}
            >
                {value || translate("common.not_available", "N/A")}
            </p>
        </div>
    );
}

export default InfoItem;
