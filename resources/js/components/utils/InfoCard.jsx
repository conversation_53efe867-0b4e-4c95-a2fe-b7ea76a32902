import React from "react";

function InfoCard({ title, children, className = "" }) {
    return (
        <>
            <div
                className={`bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden ${className}`}
            >
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">
                        {title}
                    </h3>
                </div>
                <div className="p-6">{children}</div>
            </div>
        </>
    );
}

export default InfoCard;
