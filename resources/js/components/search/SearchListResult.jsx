import { Link } from "@inertiajs/react";
import { searchResultTypeIcon, highlight } from "@/helpers/searchResult";
import dayjs from "dayjs";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function SearchListResult({ result, query }) {
    const { translate } = useSSRTranslations();
    return (
        <li>
            <Link
                href={route(`detail.show`, result.localized_slug.slug)}
                className="flex items-center gap-3 p-3 hover:bg-gray-50 transition"
            >
                <span className="text-gray-600">
                    {searchResultTypeIcon(result.type)}
                </span>
                <div>
                    <div className="text-gray-800">
                        {highlight(result.name, query)}
                    </div>
                    <div className="text-sm text-gray-400 capitalize">
                        {result.type.toLowerCase() === "event"
                            ? dayjs(result.date).format("DD/MM/YYYY")
                            : translate(`common.search_types.${result.type}`)}
                    </div>
                </div>
            </Link>
        </li>
    );
}
