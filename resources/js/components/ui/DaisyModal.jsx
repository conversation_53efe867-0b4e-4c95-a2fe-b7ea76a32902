import React, { useEffect } from "react";
import { X } from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function DaisyModal({
    children,
    isOpen,
    onClose,
    title,
    size = "md",
    showCloseButton = true,
    modalId = "daisyui-modal",
}) {
    const { translate } = useSSRTranslations();

    // Handle escape key press
    useEffect(() => {
        // Check if document is available (not during SSR)
        if (typeof document === "undefined") {
            return;
        }

        const handleEscapeKey = (e) => {
            if (e.key === "Escape" && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener("keydown", handleEscapeKey);
        }

        return () => {
            document.removeEventListener("keydown", handleEscapeKey);
        };
    }, [isOpen, onClose]);

    // Map size to DaisyUI modal size classes
    const sizeClasses =
        {
            xs: "max-w-xs",
            sm: "max-w-sm",
            md: "max-w-md",
            lg: "max-w-lg",
            xl: "max-w-xl",
            "2xl": "max-w-2xl",
            "3xl": "max-w-3xl",
            "4xl": "max-w-4xl",
            "5xl": "max-w-5xl",
            full: "max-w-full",
        }[size] || "max-w-md";

    if (!isOpen) return null;

    return (
        <dialog
            id={modalId}
            className={`modal modal-open z-50`}
            onClose={onClose}
        >
            <div className={`modal-box ${sizeClasses} shadow-lg`}>
                {showCloseButton && (
                    <button
                        onClick={onClose}
                        className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                        aria-label={translate("common.close", "Close")}
                    >
                        <X className="w-5 h-5" />
                    </button>
                )}

                {title && <h3 className="font-bold text-lg mb-4">{title}</h3>}

                <div className="modal-content">{children}</div>
            </div>

            {/* Backdrop click to close */}
            <form method="dialog" className="modal-backdrop">
                <button onClick={onClose}>Close</button>
            </form>
        </dialog>
    );
}
