import React from "react";

/**
 * A reusable spinner component with customizable size and color
 *
 * @param {Object} props - Component props
 * @param {string} props.size - Size of the spinner: 'sm', 'md', 'lg' (default: 'md')
 * @param {string} props.color - Color of the spinner: 'primary', 'secondary', 'white', 'gray' (default: 'primary')
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} - Rendered spinner component
 */
export default function Spinner({
    size = "md",
    color = "primary",
    className = "",
}) {
    // Size mappings
    const sizeClasses = {
        xs: "h-4 w-4 border-2",
        sm: "h-5 w-5 border-2",
        md: "h-8 w-8 border-3",
        lg: "h-12 w-12 border-4",
        xl: "h-16 w-16 border-4",
    };

    // Color mappings
    const colorClasses = {
        primary: "border-blue-500 border-r-transparent",
        secondary: "border-gray-500 border-r-transparent",
        white: "border-white border-r-transparent",
        gray: "border-gray-300 border-r-transparent",
        current: "border-current border-r-transparent",
    };

    // Default animation classes
    const animationClasses = "animate-spin rounded-full";

    // Combine all classes
    const spinnerClasses = [
        animationClasses,
        sizeClasses[size] || sizeClasses.md,
        colorClasses[color] || colorClasses.primary,
        className,
    ].join(" ");

    return (
        <div className="inline-flex items-center justify-center">
            <div
                className={spinnerClasses}
                role="status"
                aria-label="loading"
            ></div>
            <span className="sr-only">Loading...</span>
        </div>
    );
}
