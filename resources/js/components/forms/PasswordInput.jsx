import {
    forwardRef,
    useEffect,
    useImperative<PERSON>andle,
    useRef,
    useState,
} from "react";
import InputError from "@/components/forms/InputError";
import InputLabel from "@/components/forms/InputLabel";
import { Eye, EyeOff } from "lucide-react";

export default forwardRef(function PasswordInput(
    { className = "", isFocused = false, ...props },
    ref,
) {
    const [showPassword, setShowPassword] = useState(false);
    const localRef = useRef(null);

    useImperativeHandle(ref, () => ({
        focus: () => localRef.current?.focus(),
    }));

    useEffect(() => {
        if (isFocused) {
            localRef.current?.focus();
        }
    }, [isFocused]);

    const { error, ...inputProps } = props;
    return (
        <div className="form-control mt-2">
            {props.label && (
                <InputLabel
                    htmlFor={props.id}
                    value={props.label}
                    className={`${props.datarequired ? "block after:content-['*'] after:text-red-500 after:ml-1" : ""}`}
                />
            )}
            <label className="input input-bordered flex items-center gap-2">
                <input
                    {...inputProps}
                    type={showPassword ? "text" : "password"}
                    className={`grow ${className}`}
                    ref={localRef}
                />
                <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                >
                    {showPassword ? (
                        <EyeOff className="w-5 h-5" />
                    ) : (
                        <Eye className="w-5 h-5" />
                    )}
                </button>
            </label>
            {error && <InputError message={error} className="mt-2" />}
        </div>
    );
});
