import { useState } from "react";
import SelectInput from "@/components/forms/SelectInput";

export default function FilterSelectCollapse({
    title,
    placeholder,
    options = {},
    selectedOption = null,
    onChange,
    isMulti = false,
}) {
    const [isOpen, setIsOpen] = useState(selectedOption.length);

    const selectOptions = Object.entries(options).map(([value, label]) => ({
        value,
        label,
    }));

    return (
        <div
            className={`collapse collapse-plus ${isOpen ? "collapse-open" : ""}`}
        >
            <div
                className="collapse-title font-semibold flex justify-between items-center cursor-pointer"
                onClick={() => setIsOpen(!isOpen)}
            >
                <span>
                    {title}
                    {Array.isArray(selectedOption) &&
                        selectedOption.length > 0 && (
                            <span className="ml-2 text-xs text-primary">
                                ({selectedOption.length})
                            </span>
                        )}
                </span>
            </div>

            <div className="collapse-content">
                <SelectInput
                    options={selectOptions}
                    value={
                        isMulti
                            ? selectOptions.filter((opt) =>
                                  selectedOption?.includes(opt.value),
                              )
                            : selectOptions.find(
                                  (opt) => opt.value === selectedOption,
                              )
                    }
                    onChange={(selected) => {
                        if (isMulti) {
                            onChange(selected.map((item) => item.value));
                        } else {
                            onChange(selected?.value || null);
                        }
                    }}
                    isMulti={isMulti}
                    placeholder={placeholder}
                    menuPortalTarget={
                        typeof document !== "undefined" ? document.body : null
                    }
                />
            </div>

            <div className="divider m-0"></div>
        </div>
    );
}
