import { useState } from "react";
import FilterCheckbox from "@/components/forms/FilterCheckbox";

export default function FilterCheckboxCollapse({
    title,
    options = [],
    selectedOptions = [],
    onChange,
}) {
    const [isOpen, setIsOpen] = useState(selectedOptions.length);

    return (
        <div
            className={`collapse collapse-plus ${isOpen ? "collapse-open" : ""}`}
        >
            <div
                className="collapse-title font-semibold flex justify-between items-center cursor-pointer"
                onClick={() => setIsOpen(!isOpen)}
            >
                <span>
                    {title}
                    {Array.isArray(selectedOptions) &&
                        selectedOptions.length > 0 && (
                            <span className="ml-2 text-xs text-primary">
                                ({selectedOptions.length})
                            </span>
                        )}
                </span>
            </div>
            <div className="collapse-content text-sm">
                {Object.keys(options).length > 0 &&
                    Object.keys(options).map((key) => (
                        <FilterCheckbox
                            key={key}
                            label={options[key]}
                            value={key}
                            checked={selectedOptions.includes(key)}
                            onChange={onChange}
                        />
                    ))}
            </div>
            <div className="divider m-0"></div>
        </div>
    );
}
