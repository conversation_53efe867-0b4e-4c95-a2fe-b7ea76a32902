import { forwardRef, useRef } from "react";
import Select from "react-select";
import InputError from "@/components/forms/InputError";
import InputLabel from "@/components/forms/InputLabel";
import React from "react";

export default forwardRef(function SelectInput(
    { className = "", options = [], wrapperClass = "", ...props },
    ref,
) {
    const localRef = useRef(null);

    const { error, ...selectProps } = props;

    const customStyles = {
        control: (base, state) => ({
            ...base,
            backgroundColor: "white",
            borderColor: error ? "red" : "#1f293733",
            borderWidth: "1px",
            borderRadius: "8px",
            padding: "5px",
            outline: state.isFocused ? "2px solid #1f293733" : "none",
            outlineOffset: state.isFocused ? "2px" : "",
            boxShadow: "none",
            "&:hover": {
                borderColor: error ? "red" : "#1f293733",
            },
        }),
        menuPortal: (base) => ({ ...base, zIndex: 999 }),
    };

    return (
        <div className={`form-control mt-2 ${(wrapperClass = "")}`}>
            {props.label && (
                <InputLabel
                    htmlFor={props.id}
                    value={props.label}
                    className={`${props.datarequired ? "block after:content-['*'] after:text-red-500 after:ml-1" : ""}`}
                />
            )}
            <Select
                {...selectProps} // Spread remaining props
                options={options}
                className={`w-full ${className}`}
                styles={customStyles}
                placeholder={
                    props.datarequired
                        ? `${props.placeholder} *`
                        : props.placeholder
                }
                classNamePrefix="daisyui-select"
                isSearchable
                ref={localRef}
            />
            {error && <InputError message={error} className="mt-2" />}
        </div>
    );
});
