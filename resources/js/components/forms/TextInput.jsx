import { forwardRef, useEffect, useImperativeHandle, useRef } from "react";
import InputLabel from "@/components/forms/InputLabel";
import InputError from "@/components/forms/InputError";

export default forwardRef(function TextInput(
    { type = "text", className = "", isFocused = false, ...props },
    ref,
) {
    const localRef = useRef(null);

    useImperativeHandle(ref, () => ({
        focus: () => localRef.current?.focus(),
    }));

    useEffect(() => {
        if (isFocused) {
            localRef.current?.focus();
        }
    }, [isFocused]);

    const { error, ...inputProps } = props;

    const baseClass =
        (type === "textarea"
            ? "textarea textarea-bordered "
            : "input input-bordered ") +
        className +
        (props.readOnly ? " bg-gray-200 cursor-not-allowed" : "") +
        (error ? " input-error" : "");

    return (
        <div className="form-control mt-2">
            {props.label && (
                <InputLabel
                    htmlFor={props.id}
                    value={props.label}
                    className={`${props.datarequired ? "block after:content-['*'] after:text-red-500 after:ml-1" : ""}`}
                />
            )}
            {type === "textarea" ? (
                <textarea
                    {...inputProps}
                    className={baseClass}
                    placeholder={
                        props.datarequired
                            ? `${props.placeholder} *`
                            : props.placeholder
                    }
                    ref={localRef}
                />
            ) : (
                <input
                    {...inputProps}
                    type={type}
                    className={baseClass}
                    placeholder={
                        props.datarequired
                            ? `${props.placeholder} *`
                            : props.placeholder
                    }
                    ref={localRef}
                />
            )}
            {error && <InputError message={error} className="mt-2" />}
        </div>
    );
});
