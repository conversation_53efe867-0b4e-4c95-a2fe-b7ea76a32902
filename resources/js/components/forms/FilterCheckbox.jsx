export default function FilterCheckbox({ label, value, checked, onChange }) {
    return (
        <label className="flex items-center gap-2 mb-2 cursor-pointer">
            <input
                type="checkbox"
                className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                checked={checked}
                onChange={() => onChange(value)}
            />
            <span className="text-sm text-gray-600">{label}</span>
        </label>
    );
}
