import { Range, getTrackBackground } from "react-range";
import { useEffect, useRef, useMemo } from "react";
import debounce from "lodash/debounce";

export default function RangeSlider({
    label,
    values,
    min,
    max,
    step = 1,
    onChange,
}) {
    const currentValuesRef = useRef(values);

    const debouncedOnChange = useMemo(() => {
        return debounce((v) => {
            // Ensure values are sorted before passing to parent
            const sortedValues = v.slice().sort((a, b) => a - b);
            onChange(sortedValues);
        }, 50);
    }, [onChange]);

    useEffect(() => {
        currentValuesRef.current = values;
    }, [values]);

    useEffect(() => {
        return () => {
            debouncedOnChange.cancel();
        };
    }, [debouncedOnChange]);

    return (
        <div className="w-full">
            {label && <label className="label-text font-medium">{label}</label>}

            <Range
                values={values.slice().sort((a, b) => a - b)} // Ensure values are sorted
                step={step}
                min={min}
                max={max}
                onChange={(newValues) => {
                    currentValuesRef.current = newValues;
                    debouncedOnChange(newValues);
                }}
                renderTrack={({ props, children }) => {
                    return (
                        <div
                            {...props}
                            style={{
                                ...props.style,
                                height: "6px",
                                width: "100%",
                                background: getTrackBackground({
                                    values: values
                                        .slice()
                                        .sort((a, b) => a - b), // Use sorted values
                                    colors: ["#d1d5db", "#3b82f6", "#d1d5db"],
                                    min,
                                    max,
                                }),
                                borderRadius: "4px",
                            }}
                            className="mt-10"
                        >
                            {children}
                        </div>
                    );
                }}
                renderThumb={({ props, index }) => {
                    return (
                        <div
                            {...props}
                            key={props.key}
                            className="relative flex flex-col items-center justify-center focus:outline-none"
                            style={{ ...props.style }}
                        >
                            {/* Value Box */}
                            <div
                                className={
                                    "absolute -top-9 px-2 py-1 rounded-md shadow text-sm bg-white text-gray-800 whitespace-nowrap z-10 " +
                                    `${props.key == 1 ? " -right-4" : " -left-4"}`
                                }
                            >
                                €{parseFloat(values[index]).toFixed(2)}
                            </div>
                            {/* Thumb */}
                            <div className="h-4 w-4 rounded-full bg-blue-500 shadow-md" />
                        </div>
                    );
                }}
            />

            <div className="flex justify-between text-sm text-gray-600 mt-2">
                <span>€{parseFloat(min).toFixed(2)}</span>
                <span>€{parseFloat(max).toFixed(2)}</span>
            </div>
        </div>
    );
}
