import { useState, useRef } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function FileUploadWithPreview({
    label,
    files, // single File | null  OR  File[]
    setFiles,
    multiple = true,
    maxFiles = 5,
    maxFileSizeMb = 5,
    allowedTypes = ["application/pdf", "image/jpeg", "image/jpg", "image/png"],
}) {
    const { translate } = useSSRTranslations();

    const fileInputRef = useRef(null);
    const [uploadError, setUploadError] = useState(null);
    const [isDragging, setIsDragging] = useState(false);

    const acceptString = allowedTypes.join(",");

    const fileTypeText = Array.from(
        new Set(allowedTypes.map((t) => t.split("/").pop()?.toUpperCase())),
    ).join(", ");

    const isImageFile = (file) => file && file.type.startsWith("image/");

    // Validate files (returns new files or null if invalid)
    const validateFiles = (selectedFiles) => {
        if (!selectedFiles.length) return null;

        if (multiple) {
            const combined = [...(files || []), ...selectedFiles];
            if (combined.length > maxFiles) {
                setUploadError(
                    replacePlaceholders(
                        translate("validation.custom.max_files"),
                        {
                            maxFiles: maxFiles,
                        },
                    ),
                );
                return null;
            }
            for (const file of selectedFiles) {
                if (!allowedTypes.includes(file.type)) {
                    const ext = file.name.split(".").pop().toLowerCase();
                    setUploadError(
                        replacePlaceholders(
                            translate("validation.custom.file_type"),
                            {
                                ext: ext,
                            },
                        ),
                    );
                    return null;
                }
                if (file.size > maxFileSizeMb * 1024 * 1024) {
                    setUploadError(
                        replacePlaceholders(
                            translate("validation.custom.file_size"),
                            {
                                maxFileSize: maxFileSizeMb,
                            },
                        ),
                    );
                    return null;
                }
            }
            return combined;
        } else {
            const file = selectedFiles[0];
            if (!allowedTypes.includes(file.type)) {
                const ext = file.name.split(".").pop().toLowerCase();
                setUploadError(
                    replacePlaceholders(
                        translate("validation.custom.file_type"),
                        {
                            ext: ext,
                        },
                    ),
                );
                return null;
            }
            if (file.size > maxFileSizeMb * 1024 * 1024) {
                setUploadError(
                    replacePlaceholders(
                        translate("validation.custom.file_size"),
                        {
                            maxFileSize: maxFileSizeMb,
                        },
                    ),
                );
                return null;
            }
            return file;
        }
    };

    const replacePlaceholders = (message, replacements) => {
        return Object.entries(replacements).reduce(
            (msg, [key, value]) => msg.replaceAll(`:${key}`, value),
            message,
        );
    };

    const handleFileChange = (e) => {
        const selectedFiles = Array.from(e.target.files);
        const validated = validateFiles(selectedFiles);
        if (validated !== null) {
            setFiles(validated);
            setUploadError(null);
        }
        e.target.value = null; // allow re-selecting same file
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        const droppedFiles = Array.from(e.dataTransfer.files);
        const validated = validateFiles(droppedFiles);
        if (validated !== null) {
            setFiles(validated);
            setUploadError(null);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const removeFile = (index) => {
        if (multiple) {
            setFiles((prev) => prev.filter((_, i) => i !== index));
        } else {
            setFiles(null);
        }
    };

    // always render as array for preview
    const previewFiles = multiple ? files || [] : files ? [files] : [];

    return (
        <div>
            <label className="label">
                <span className="label-text font-medium">{label}</span>
            </label>

            <div
                className={`border-2 border-dashed rounded p-3 cursor-pointer 
                    ${isDragging ? "border-primary bg-primary/10" : "border-gray-300"}
                    flex flex-col items-center gap-1 justify-center text-center`}
                onClick={() => fileInputRef.current?.click()}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
            >
                <div className="text-sm text-gray-500">
                    {translate("common.labels.file_upload")}
                </div>
                <div className="text-xs text-gray-400">
                    {translate("common.labels.supported_file_types")}:{" "}
                    {fileTypeText}
                </div>
                <div className="text-xs text-gray-400">
                    {translate("common.labels.max_file_size")}: {maxFileSizeMb}{" "}
                    MB {multiple && <>{translate("common.labels.per_file")}</>}
                </div>
            </div>

            <input
                ref={fileInputRef}
                type="file"
                accept={acceptString}
                multiple={multiple}
                onChange={handleFileChange}
                className="hidden"
            />

            {uploadError && (
                <div className="text-red-500 text-sm mt-1">{uploadError}</div>
            )}

            {previewFiles.length > 0 && (
                <ul className="mt-4 space-y-2 text-xs text-gray-600 max-h-32 overflow-auto">
                    {previewFiles.map((file, idx) => (
                        <li key={idx} className="flex items-center gap-2">
                            {isImageFile(file) ? (
                                <img
                                    src={URL.createObjectURL(file)}
                                    alt={file.name}
                                    className="w-10 h-10 object-cover rounded border"
                                />
                            ) : (
                                <div className="w-10 h-10 flex items-center justify-center bg-gray-200 text-red-500 rounded">
                                    {file.name.split(".").pop().toUpperCase()}
                                </div>
                            )}
                            <span className="flex-1 truncate">{file.name}</span>
                            <span className="text-gray-400">
                                {file.size < 1024 * 1024
                                    ? `(${(file.size / 1024).toFixed(1)} KB)`
                                    : `(${(file.size / 1024 / 1024).toFixed(1)} MB)`}
                            </span>
                            <button
                                onClick={() => removeFile(idx)}
                                type="button"
                                className="btn btn-xs btn-circle btn-ghost text-red-500 hover:bg-red-100"
                            >
                                ✕
                            </button>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
}
