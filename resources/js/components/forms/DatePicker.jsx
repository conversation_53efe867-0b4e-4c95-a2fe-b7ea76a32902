import React, { useState, useRef, useEffect } from "react";
import dayjs from "dayjs";
import { Calendar, ChevronLeft, ChevronRight } from "lucide-react";
import InputError from "@/components/forms/InputError";

const DatePicker = ({
    value,
    onChange,
    placeholder = "Select date",
    className = "",
    disabled = false,
    minDate = null,
    maxDate = null,
    ...props
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [currentMonth, setCurrentMonth] = useState(dayjs(value || undefined));
    const dropdownRef = useRef(null);
    const inputRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target)
            ) {
                setIsOpen(false);
            }
        };

        // Check if document is available (not during SSR)
        if (typeof document !== "undefined") {
            document.addEventListener("mousedown", handleClickOutside);
            return () =>
                document.removeEventListener("mousedown", handleClickOutside);
        }
    }, []);

    // Generate calendar days
    const generateCalendarDays = () => {
        const startOfMonth = currentMonth.startOf("month");
        const endOfMonth = currentMonth.endOf("month");
        const startOfCalendar = startOfMonth.startOf("week");
        const endOfCalendar = endOfMonth.endOf("week");

        const days = [];
        let day = startOfCalendar;

        while (
            day.isBefore(endOfCalendar) ||
            day.isSame(endOfCalendar, "day")
        ) {
            days.push(day);
            day = day.add(1, "day");
        }

        return days;
    };

    const handleDateSelect = (date) => {
        const formattedDate = date.format("YYYY-MM-DD");
        onChange(formattedDate);
        setIsOpen(false);
    };

    const handlePrevMonth = () => {
        setCurrentMonth(currentMonth.subtract(1, "month"));
    };

    const handleNextMonth = () => {
        setCurrentMonth(currentMonth.add(1, "month"));
    };

    const isDateDisabled = (date) => {
        if (minDate && date.isBefore(dayjs(minDate), "day")) return true;
        if (maxDate && date.isAfter(dayjs(maxDate), "day")) return true;
        return false;
    };

    const formatDisplayValue = (dateValue) => {
        if (!dateValue) return "";
        return dayjs(dateValue).format("DD/MM/YYYY");
    };

    const calendarDays = generateCalendarDays();
    const selectedDate = value ? dayjs(value) : null;

    const { error, ...inputProps } = props;

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            {/* Input Field */}
            <div className="relative">
                <input
                    ref={inputRef}
                    type="text"
                    value={formatDisplayValue(value)}
                    placeholder={placeholder}
                    readOnly
                    disabled={disabled}
                    onClick={() => !disabled && setIsOpen(!isOpen)}
                    className={`
                        input input-bordered w-full pr-10 cursor-pointer
                        ${disabled ? "input-disabled" : ""}
                    `}
                />
                <Calendar
                    size={18}
                    className={`
                        absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none
                        ${disabled ? "text-gray-400" : "text-gray-500"}
                    `}
                />
            </div>

            {error && <InputError message={error} className="mt-2" />}

            {/* Calendar Dropdown */}
            {isOpen && !disabled && (
                <div className="absolute top-full left-0 mt-1 z-50 bg-base-100 border border-base-300 rounded-lg shadow-lg p-4 w-80">
                    {/* Month Navigation */}
                    <div className="flex items-center justify-between mb-4">
                        <button
                            type="button"
                            onClick={handlePrevMonth}
                            className="btn btn-ghost btn-sm btn-square"
                        >
                            <ChevronLeft size={16} />
                        </button>

                        <h3 className="font-semibold text-base">
                            {currentMonth.format("MMMM YYYY")}
                        </h3>

                        <button
                            type="button"
                            onClick={handleNextMonth}
                            className="btn btn-ghost btn-sm btn-square"
                        >
                            <ChevronRight size={16} />
                        </button>
                    </div>

                    {/* Weekday Headers */}
                    <div className="grid grid-cols-7 gap-1 mb-2">
                        {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map(
                            (day) => (
                                <div
                                    key={day}
                                    className="text-center text-xs font-medium text-gray-500 py-2"
                                >
                                    {day}
                                </div>
                            ),
                        )}
                    </div>

                    {/* Calendar Grid */}
                    <div className="grid grid-cols-7 gap-1">
                        {calendarDays.map((day, index) => {
                            const isCurrentMonth = day.isSame(
                                currentMonth,
                                "month",
                            );
                            const isSelected =
                                selectedDate && day.isSame(selectedDate, "day");
                            const isToday = day.isSame(dayjs(), "day");
                            const isDisabled = isDateDisabled(day);

                            return (
                                <button
                                    key={index}
                                    type="button"
                                    onClick={() =>
                                        !isDisabled && handleDateSelect(day)
                                    }
                                    disabled={isDisabled}
                                    className={`
                                        w-8 h-8 text-sm rounded-md transition-colors
                                        ${!isCurrentMonth ? "text-gray-300" : ""}
                                        ${isSelected ? "bg-primary text-primary-content" : ""}
                                        ${isToday && !isSelected ? "bg-base-200 font-semibold" : ""}
                                        ${isDisabled ? "text-gray-300 cursor-not-allowed" : "hover:bg-base-200"}
                                        ${!isSelected && !isDisabled && isCurrentMonth ? "hover:bg-base-200" : ""}
                                    `}
                                >
                                    {day.format("D")}
                                </button>
                            );
                        })}
                    </div>

                    {/* Clear Button */}
                    {value && (
                        <div className="mt-4 pt-3 border-t border-base-300">
                            <button
                                type="button"
                                onClick={() => {
                                    onChange("");
                                    setIsOpen(false);
                                }}
                                className="btn btn-ghost btn-sm w-full"
                            >
                                Clear Date
                            </button>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default DatePicker;
