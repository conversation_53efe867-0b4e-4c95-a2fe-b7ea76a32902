import { forwardRef, useRef } from "react";
import InputLabel from "@/components/forms/InputLabel";
import InputError from "@/components/forms/InputError";

export default forwardRef(function RadioInput(
    { options = [], className = "", checked, ...props },
    ref,
) {
    const localRef = useRef(null);

    const { error, ...radioProps } = props;
    return (
        <div className={`form-control mt-2 ${className}`}>
            {props.label && (
                <InputLabel
                    htmlFor={props.name}
                    value={props.label}
                    className={
                        props.datarequired
                            ? "block after:content-['*'] after:text-red-500 after:ml-1"
                            : ""
                    }
                />
            )}

            {options.map((option, index) => (
                <label
                    key={option.value}
                    className="flex items-center cursor-pointer mb-3"
                >
                    <input
                        type="radio"
                        value={option.value}
                        checked={checked === option.value}
                        className={
                            "radio radio-primary border-gray-300 hover:border-gray-500 checked:border-gray-500 radio-sm " +
                            className
                        }
                        {...radioProps}
                    />
                    <span className="label-text ml-2 text">{option.label}</span>
                </label>
            ))}

            {error && <InputError message={error} className="mt-2" />}
        </div>
    );
});
