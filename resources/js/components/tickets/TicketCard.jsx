import { SquarePen, Trash2 } from "lucide-react";
import { formatDate } from "@/helpers/formatUtils";
import { Link } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

const TicketCard = ({ ticket, onDeleteClick }) => {
    const { translate } = useSSRTranslations();

    return (
        <div className="card bg-base-100 shadow-md border border-base-300">
            <div className="card-body p-5">
                <div className="flex justify-between items-start flex-wrap gap-2 border-b pb-3">
                    <div className="flex flex-col min-[400px]:flex-row min-[400px]:items-center gap-2">
                        <h2 className="text-base sm:text-lg font-semibold">
                            {translate("my_tickets.ticket_text")} #
                            {ticket.ticket_no}
                        </h2>
                        <span
                            className={`badge badge-outline capitalize w-fit badge-${ticket.ticket_type.color}`}
                        >
                            {ticket.ticket_type.label}
                        </span>
                    </div>

                    <div className="flex items-center gap-2">
                        <Link
                            className="rounded-md bg-amber-400/10 text-amber-400 hover:bg-amber-400/20 p-2 transition"
                            href={route(
                                "my-account.tickets.edit",
                                ticket.ticket_no,
                            )}
                        >
                            <SquarePen className="w-4 h-4" />
                        </Link>

                        {ticket.orders_count === 0 &&
                        ticket.reservations_count === 0 ? (
                            <span
                                className="cursor-pointer rounded-md bg-red-400/10 text-red-400 hover:bg-red-400/20 p-2 transition"
                                onClick={() => onDeleteClick(ticket)}
                            >
                                <Trash2 className="w-4 h-4" />
                            </span>
                        ) : (
                            <span
                                className="tooltip tooltip-left rounded-md bg-gray-200 text-gray-500 hover:bg-gray-300 p-2 transition"
                                data-tip={translate(
                                    "my_tickets.delete_tooltip_text",
                                )}
                            >
                                <Trash2 className="w-4 h-4" />
                            </span>
                        )}
                    </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 mt-2">
                    <figure className="relative h-20 bg-gray-200 object-cover rounded border border-base-300 mx-auto md:mx-0">
                        {ticket.event.image !== "" ? (
                            <img
                                src={ticket.event.image}
                                alt={
                                    ticket.event.image_alt?.alt ||
                                    ticket.event.name
                                }
                                className="w-24 h-full object-cover"
                            />
                        ) : (
                            <img
                                src="/img/ticketgol-logo.png"
                                alt={ticket.event.name}
                                className="w-24"
                            />
                        )}
                    </figure>
                    <div className="flex-1 space-y-2 text-sm text-base-content/70 text-center sm:text-left">
                        <p>
                            <span className="font-medium text-base-content">
                                {translate("sell.labels.event")}:
                            </span>{" "}
                            {ticket.event.name}
                        </p>

                        <div className="flex grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("sell.labels.total_quantity")}:
                                </span>{" "}
                                {ticket.quantity}
                            </p>

                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("sell.labels.reserved_quantity")}
                                    :
                                </span>{" "}
                                {ticket.reservations_sum_quantity ?? 0}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate(
                                        "sell.labels.available_quantity",
                                    )}
                                    :
                                </span>{" "}
                                {ticket.quantity -
                                    (ticket.reservations_sum_quantity ?? 0)}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("sell.labels.sold_tickets")}:
                                </span>{" "}
                                {ticket.sold_quantity ?? 0}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("sell.labels.price_text")}:
                                </span>{" "}
                                €{ticket.price}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("sell.labels.event_date")}:
                                </span>{" "}
                                {formatDate(ticket.event.date)}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("sell.labels.sector_id")}:
                                </span>{" "}
                                {ticket.sector.name}
                            </p>
                            <p>
                                <span className="font-medium text-base-content">
                                    {translate("common.labels.created_at")}:
                                </span>{" "}
                                {formatDate(ticket.created_at)}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TicketCard;
