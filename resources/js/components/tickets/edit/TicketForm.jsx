import { Info } from "lucide-react";
import toast from "react-hot-toast";
import { router } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useValidation from "@/hooks/useValidation";
import useEditTicket from "@/hooks/useEditTicket";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import RadioInput from "@/components/forms/RadioInput";
import FilterCheckbox from "@/components/forms/FilterCheckbox";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

const TicketForm = () => {
    const { translate } = useSSRTranslations();

    const { ticket, formData, updateFormData } = useEditTicket();

    const validationSchema = {
        quantity: [
            "required",
            "number",
            { rule: "min", value: ticket.reservations_sum_quantity || 1 },
            {
                rule: "max",
                value: ticket.configurations.max_quantity_per_ticket,
            },
        ],
        price: [
            "required",
            "number",
            { rule: "min", value: 1 },
            { rule: "max", value: ticket.configurations.max_price_limit },
        ],
        face_value_price: [
            "required",
            "number",
            { rule: "min", value: 1 },
            { rule: "max", value: ticket.configurations.max_price_limit },
        ],
        ticket_rows: [{ rule: "maxlength", value: 100 }],
        ticket_seats: [{ rule: "maxlength", value: 200 }],
        ticket_type: ["required"],
        quantity_split_type: ["required"],
        description: ["required", { rule: "maxlength", value: 255 }],
    };
    const { errors, validate, validateField } = useValidation(validationSchema);

    const ticketTypeOptions = prepareOptionsFromEnum(
        translate("enums.ticket_types"),
    );
    const ticketSplitTypeOptions = prepareOptionsFromEnum(
        translate("enums.ticket_quantity_split_types"),
    );

    const updateTooltipMsg = translate(
        "sell.quantity_update_message",
    ).replaceAll("{{quantities}}", ticket.reservations_sum_quantity);

    const handleUpdateClick = async (e) => {
        e.preventDefault();
        const isValid = validate(formData);
        if (!isValid) return;

        if (!formData.terms_agreed) {
            toast.error(
                translate(
                    "sell.conditions_terms",
                    "Please read and agree to our terms and conditions",
                ),
            );
            return;
        }

        try {
            const { data } = await axios.post(
                route("api.tickets.update"),
                formData,
            );
            if (data.success) {
                toast.success(data.message);
                router.visit(route("my-account.tickets"));
            }
        } catch (error) {
            if (error.response?.status === 422 && error.response.data?.errors) {
                toast.error(Object.values(error.response.data?.errors)[0]?.[0]);
            } else {
                toast.error(translate("common.something_wrong"));
            }
        }
    };

    return (
        <form>
            <h2 className="text-lg font-bold border-t pt-3">
                {translate("sell.step_1_title")}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-2 gap-x-4">
                <div className="relative">
                    <TextInput
                        id="quantity"
                        type="number"
                        value={formData.quantity}
                        min={ticket.reservations_sum_quantity || 1}
                        max={ticket.configurations.max_quantity_per_ticket}
                        label={translate("sell.labels.quantity")}
                        placeholder={translate("sell.placeholder.quantity")}
                        datarequired="true"
                        onChange={(e) =>
                            updateFormData("quantity", e.target.value)
                        }
                        error={errors?.quantity}
                    />
                    {ticket.reservations_sum_quantity > 0 && (
                        <div
                            className="tooltip tooltip-left absolute top-0 right-0 mt-5 sm:mr-2"
                            data-tip={updateTooltipMsg}
                        >
                            <span className="text-gray-500 text-sm">
                                <Info className="w-4 h-4" />
                            </span>
                        </div>
                    )}
                </div>
                <TextInput
                    id="price"
                    type="number"
                    value={formData.price}
                    label={translate("sell.labels.price")}
                    min="0"
                    max={ticket.configurations.max_price_limit}
                    step="0.01"
                    placeholder={translate("sell.placeholder.price")}
                    datarequired="true"
                    onChange={(e) => updateFormData("price", e.target.value)}
                    onBlur={(e) => {
                        updateFormData(
                            "price",
                            parseFloat(e.target.value).toFixed(2),
                        );
                        validateField("price", formData.price, formData);
                    }}
                    error={errors?.price}
                />
                <TextInput
                    id="face_value_price"
                    type="number"
                    value={formData.face_value_price}
                    label={translate("sell.labels.face_value_price")}
                    min="0"
                    max={ticket.configurations.max_price_limit}
                    step="0.01"
                    placeholder={translate("sell.placeholder.face_value_price")}
                    datarequired="true"
                    onChange={(e) =>
                        updateFormData("face_value_price", e.target.value)
                    }
                    onBlur={(e) => {
                        updateFormData(
                            "face_value_price",
                            parseFloat(e.target.value).toFixed(2),
                        );
                        validateField(
                            "face_value_price",
                            formData.face_value_price,
                            formData,
                        );
                    }}
                    error={errors?.face_value_price}
                />
                <TextInput
                    id="ticket_rows"
                    value={formData.ticket_rows}
                    label={translate("sell.labels.ticket_rows")}
                    placeholder={translate("sell.placeholder.ticket_rows")}
                    onChange={(e) =>
                        updateFormData("ticket_rows", e.target.value)
                    }
                    onBlur={(e) =>
                        validateField(
                            "ticket_rows",
                            formData.ticket_rows,
                            formData,
                        )
                    }
                    error={errors?.ticket_rows}
                />
                <TextInput
                    id="ticket_seats"
                    value={formData.ticket_seats}
                    label={translate("sell.labels.ticket_seats")}
                    placeholder={translate("sell.placeholder.ticket_seats")}
                    onChange={(e) =>
                        updateFormData("ticket_seats", e.target.value)
                    }
                    onBlur={(e) =>
                        validateField(
                            "ticket_seats",
                            formData.ticket_seats,
                            formData,
                        )
                    }
                    error={errors?.ticket_seats}
                />
            </div>
            <h2 className="text-lg font-bold mt-5 border-t pt-3">
                {translate("sell.step_2_title")}
            </h2>
            <div className="grid grid-cols-1 gap-2">
                <TextInput
                    id="description"
                    type="textarea"
                    value={formData.description}
                    label={translate("sell.labels.description")}
                    placeholder={translate("sell.placeholder.description")}
                    datarequired="true"
                    onChange={(e) =>
                        updateFormData("description", e.target.value)
                    }
                    onBlur={(e) =>
                        validateField(
                            "description",
                            formData.description,
                            formData,
                        )
                    }
                    error={errors?.description}
                />
                <RadioInput
                    label={translate("sell.labels.ticket_type")}
                    checked={formData.ticket_type}
                    onChange={(e) => {
                        updateFormData("ticket_type", e.target.value);
                        validateField("ticket_type", e.target.value, formData);
                    }}
                    datarequired="true"
                    options={ticketTypeOptions}
                    error={errors?.ticket_type}
                />
                <RadioInput
                    label={translate("sell.labels.quantity_split_type")}
                    checked={formData.quantity_split_type}
                    onChange={(e) => {
                        updateFormData("quantity_split_type", e.target.value);
                        validateField(
                            "quantity_split_type",
                            e.target.value,
                            formData,
                        );
                    }}
                    datarequired="true"
                    options={ticketSplitTypeOptions}
                    error={errors?.quantity_split_type}
                />
            </div>
            <h2 className="text-lg font-bold mt-5 border-t pt-3">
                {translate("sell.step_3_title")}
            </h2>
            <div className="grid grid-cols-1">
                <div className="form-control mt-2">
                    <label className="label text-sm font-medium pl-0">
                        {translate("sell.labels.restrictions")}
                    </label>
                    {Object.keys(ticket.configurations.restrictions).map(
                        (key) => (
                            <FilterCheckbox
                                key={key}
                                label={ticket.configurations.restrictions[key]}
                                value={key}
                                checked={formData.restrictions.includes(
                                    Number(key),
                                )}
                                onChange={(value) => {
                                    updateFormData("restrictions", value);
                                }}
                            />
                        ),
                    )}
                </div>
            </div>
            <label className="flex items-center text-sm text-gray-700 cursor-pointer border-t pt-5">
                <input
                    type="checkbox"
                    className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    checked={formData.terms_agreed}
                    onChange={(e) =>
                        updateFormData("terms_agreed", e.target.checked)
                    }
                />
                <span className="ml-2">
                    {translate(
                        "sell.read_agree_text",
                        "I have read and agree to the",
                    )}{" "}
                    <a href="#" className="link link-primary">
                        {translate(
                            "sell.terms_conditions_text",
                            "terms & conditions",
                        )}
                    </a>
                </span>
            </label>
            <div className="flex justify-end mt-4">
                <button className="btn btn-primary" onClick={handleUpdateClick}>
                    {translate("sell.update_btn", "Update")}
                </button>
            </div>
        </form>
    );
};

export default TicketForm;
