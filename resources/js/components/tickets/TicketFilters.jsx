import { Search, Filter } from "lucide-react";
import { useEffect, useRef } from "react";
import { useDebounce } from "@uidotdev/usehooks";
import useMyTickets from "@/hooks/useMyTickets";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import DatePicker from "@/components/forms/DatePicker";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

const TicketFilters = () => {
    const { translate } = useSSRTranslations();

    const { filters, updateFilter, clearFilters, getMyTickets } =
        useMyTickets();

    const debouncedFilters = useDebounce(filters, 500);
    const isFirstRender = useRef(true);
    const today = new Date();

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        getMyTickets();
    }, [debouncedFilters]);

    const ticketTypeOptions = [
        { label: translate("my_tickets.all_ticket_types"), value: "" },
        ...prepareOptionsFromEnum(translate("enums.ticket_types")),
    ];

    return (
        <div className="border-b pb-5">
            <div className="relative w-full mb-3">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                <TextInput
                    value={filters.search}
                    onChange={(e) => updateFilter("search", e.target.value)}
                    placeholder={translate("my_tickets.search_placeholder")}
                    className="pl-10"
                />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <DatePicker
                    value={filters.date_from}
                    className="form-control mt-2"
                    onChange={(value) => updateFilter("date_from", value)}
                    placeholder={translate("my_tickets.date_from_placeholder")}
                    maxDate={filters.date_to || today}
                />

                <DatePicker
                    className="form-control mt-2"
                    value={filters.date_to}
                    onChange={(value) => updateFilter("date_to", value)}
                    placeholder={translate("my_tickets.date_to_placeholder")}
                    minDate={filters.date_from || undefined}
                    maxDate={today}
                />

                <SelectInput
                    options={ticketTypeOptions}
                    value={ticketTypeOptions.find(
                        (opt) => opt.value === filters.ticket_type,
                    )}
                    onChange={(option) =>
                        updateFilter("ticket_type", option?.value || "")
                    }
                    placeholder={translate(
                        "my_tickets.ticket_type_placeholder",
                    )}
                />

                <SelectInput
                    options={translate("my_tickets.tickets_sort_options")}
                    value={
                        filters.sort
                            ? translate("my_tickets.tickets_sort_options").find(
                                  (option) => option.value === filters.sort,
                              )
                            : null
                    }
                    onChange={(option) =>
                        updateFilter("sort", option?.value || "")
                    }
                    placeholder={translate("my_tickets.sort_by_placeholder")}
                />
            </div>

            <div className="flex gap-6 justify-end mt-5">
                <button
                    onClick={clearFilters}
                    className="btn btn-outline btn-sm"
                >
                    {translate("common.clear", "Clear")}
                </button>
            </div>
        </div>
    );
};

export default TicketFilters;
