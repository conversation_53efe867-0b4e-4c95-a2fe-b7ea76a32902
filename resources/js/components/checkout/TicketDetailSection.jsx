import { Calendar, MapPin, Blocks, Tickets } from "lucide-react";
import { Link } from "@inertiajs/react";
import EventDateTime from "@/components/eventdetails/EventDateTime";
import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function TicketDetailSection() {
    const { translate } = useSSRTranslations();
    const { reservation } = useTicketCheckout();

    return (
        <>
            <div className="px-5 mb-5">
                <h2 className="mt-5 text-xl font-bold leading-tight">
                    <Link
                        href={route(
                            "detail.show",
                            reservation.ticket.event.slug,
                        )}
                        className="hover:underline"
                    >
                        {reservation.ticket.event.name}
                    </Link>
                </h2>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700">
                    <Calendar className="w-4 h-4" />
                    <EventDateTime event={reservation.ticket.event} />
                </div>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700 hover:underline">
                    <MapPin className="w-4 h-4" />
                    <Link
                        href={route(
                            "detail.show",
                            reservation.ticket.event.stadium.slug,
                        )}
                    >
                        <span>
                            {reservation.ticket.event.stadium.name},{" "}
                            {reservation.ticket.event.stadium.address_line_1},{" "}
                            {reservation.ticket.event.stadium.address_line_2},{" "}
                            {reservation.ticket.event.stadium.country.name},{" "}
                            {reservation.ticket.event.stadium.postcode}
                        </span>
                    </Link>
                </div>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700">
                    <Blocks className="w-4 h-4" />
                    <p>{reservation.ticket.sector.name}</p>
                </div>
                <div className="mt-3 flex items-center gap-2 font-bold text-sm text-gray-700">
                    <Tickets className="w-4 h-4" />
                    <p>
                        {reservation.quantity}{" "}
                        {translate("ticket.tickets_text")}
                    </p>
                </div>
                <div className="mt-3 flex items-center gap-2 text-sm text-gray-700">
                    <p
                        className={`badge badge-outline capitalize badge-${reservation.ticket.ticket_type.color}`}
                    >
                        {reservation.ticket.ticket_type.label}
                    </p>
                </div>
            </div>
        </>
    );
}
