import { useState } from "react";
import { router, usePage } from "@inertiajs/react";
import toast from "react-hot-toast";
import RestrictionsSection from "@/components/checkout/RestrictionsSection";
import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useStripePayment from "@/hooks/useStripePayment";

export default function ReviewConfirmStep() {
    const { translate } = useSSRTranslations();
    const { reservationId } = usePage().props;

    const {
        reservation,
        isAgreed,
        updateIsAgreed,
        prevStep,
        showTimesUpPopup,
    } = useTicketCheckout();

    const {
        loading,
        CardElement,
        confirmPayment,
        cardCompleted,
        setCardCompleted,
    } = useStripePayment({
        reservationId,
    });

    const handleConfirmClick = async (e) => {
        e.preventDefault();

        if (showTimesUpPopup) {
            toast.error(
                translate(
                    "ticket.rerservation_session_expired",
                    "Your ticket purchase session has expired.",
                ),
            );
            return;
        }

        if (!isAgreed.terms) {
            toast.error(
                translate(
                    "ticket.conditions_terms",
                    "Please read and agree to our terms and conditions",
                ),
            );
            return;
        }

        if (
            (Object.keys(reservation.ticket.restrictions).length > 0 ||
                Object.keys(reservation.ticket.event.restrictions).length >
                    0) &&
            !isAgreed.restrictions
        ) {
            toast.error(
                translate(
                    "ticket.restrictions_terms",
                    "Please read and agree to our Event & Ticket terms",
                ),
            );
            return;
        }

        const result = await confirmPayment();

        if (result.success) {
            return router.visit(
                route("checkout.success", {
                    orderId: result.encryptedOrderId,
                    payment: "success",
                }),
            );
        }
    };

    return (
        <>
            <div className="relative">
                <form className="space-y-4" autoComplete="off">
                    {loading && (
                        <div className="absolute inset-0 z-10 bg-white bg-opacity-70 flex items-center justify-center">
                            <span className="loading loading-bars loading-xl"></span>
                        </div>
                    )}
                    <h2 className="text-xl font-bold mb-3">
                        {translate(
                            "ticket.review_step_text",
                            "Review & Confirm",
                        )}
                    </h2>
                    <div className="text-sm text-gray-700">
                        {translate("ticket.total_tickets", "Total Tickets")}
                        <span className="float-right">
                            {reservation.quantity} x €{reservation.price}
                        </span>
                    </div>
                    <div className="divider m-0"></div>
                    <div className="bg-base-200 rounded-lg p-4 text-sm text-gray-800">
                        <div className="flex justify-between mb-1">
                            <span>
                                {translate("ticket.subtotal_text", "Sub Total")}
                            </span>
                            <span>€{reservation.subTotal}</span>
                        </div>
                        <div className="flex justify-between mb-1">
                            <span>
                                {translate(
                                    "ticket.service_charge_text",
                                    "Service Charge",
                                )}
                            </span>
                            <span>€{reservation.serviceCharge}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                            <span>{translate("ticket.tax_text", "Tax")}</span>
                            <span>€{reservation.taxRate}</span>
                        </div>
                        <div className="divider m-0" />
                        <div className="flex justify-between font-semibold text-base mt-2">
                            <span>
                                {translate(
                                    "ticket.grand_total_text",
                                    "Grand Total",
                                )}
                            </span>
                            <span>€{reservation.grandTotal}</span>
                        </div>
                    </div>

                    <div className="divider m-0" />
                    <h2 className="text-lg font-bold mb-2">Payment Details</h2>
                    <div className="w-full border border-gray-300 rounded-lg px-4 py-3 bg-white focus-within:ring-2 focus-within:ring-primary focus-within:border-primary">
                        <CardElement
                            options={{
                                style: {
                                    base: {
                                        fontSize: "16px",
                                        color: "#000",
                                        fontFamily: "'Inter', sans-serif",
                                        "::placeholder": {
                                            color: "#a3a3a3",
                                        },
                                    },
                                    invalid: {
                                        color: "#f43f5e",
                                    },
                                },
                                hidePostalCode: true,
                            }}
                            onChange={(event) => {
                                setCardCompleted(event.complete);
                            }}
                        />
                    </div>

                    <div className="divider m-0" />
                    {(Object.keys(reservation.ticket.restrictions).length > 0 ||
                        Object.keys(reservation.ticket.event.restrictions)
                            .length > 0) && (
                        <RestrictionsSection
                            reservation={reservation}
                            isAgreed={isAgreed}
                            updateIsAgreed={updateIsAgreed}
                        />
                    )}
                    <div className="flex items-start text-sm text-gray-700">
                        <label>
                            <input
                                type="checkbox"
                                className="checkbox checkbox-sm"
                                checked={isAgreed.terms}
                                onChange={(e) =>
                                    updateIsAgreed("terms", e.target.checked)
                                }
                            />
                        </label>
                        <span className="ml-2">
                            {translate(
                                "ticket.read_agree_text",
                                "I have read and agree to the",
                            )}{" "}
                            <a href="#" className="link link-primary">
                                {translate(
                                    "ticket.terms_conditions_text",
                                    "terms & conditions",
                                )}
                            </a>
                        </span>
                    </div>
                    <div className="flex justify-between mt-4">
                        <button className="btn btn-outline" onClick={prevStep}>
                            {translate("ticket.back_btn", "Back")}
                        </button>
                        <button
                            className="btn btn-primary"
                            onClick={handleConfirmClick}
                            disabled={loading || !cardCompleted}
                        >
                            {translate(
                                "ticket.confirm_pay_btn",
                                "Confirm & Pay",
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </>
    );
}
