import { useState } from "react";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import DatePicker from "@/components/forms/DatePicker";
import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useValidation from "@/hooks/useValidation";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";
import dayjs from "dayjs";

export default function AttendeesStep() {
    const { translate } = useSSRTranslations();

    const {
        reservation,
        attendees,
        updateAttendees,
        nextStep,
        prevStep,
        paymentState,
    } = useTicketCheckout();

    const today = dayjs().format("YYYY-MM-DD");

    const validationSchema = {
        attendees: [
            "array",
            { rule: "minArrayLength", value: attendees.length },
        ],
        "attendees.*.name": ["required"],
        "attendees.*.email": ["required", "email"],
        "attendees.*.gender": ["required"],
        "attendees.*.dob": ["required", { rule: "maxDate", value: today }],
    };

    const { errors, validate, validateField } = useValidation(validationSchema);

    const genderOptions = prepareOptionsFromEnum(translate("enums.genders"));

    const handleNext = (e) => {
        e.preventDefault();
        const isValid = validate({ attendees });
        if (isValid) {
            nextStep();
        }
    };

    return (
        <>
            <h2 className="text-xl font-bold mb-3">
                {translate(
                    "ticket.attendees_step_text",
                    "Attendees Information",
                )}
            </h2>
            <div className="text-sm text-warning mb-4">
                <strong>{translate("ticket.note_label", "Note")} :</strong>{" "}
                {translate(
                    "ticket.attendees_locked_info",
                    "Once you click Confirm & Pay in the final step, you won't be able to edit attendee details.",
                )}
            </div>
            <div className="space-y-4">
                {attendees.map((attendee, index) => (
                    <div key={index} className="border rounded-lg p-4 shadow">
                        <h5 className="font-semibold mb-3">
                            {translate("ticket.attendee_text", "Attendee")}{" "}
                            {index + 1}
                        </h5>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <TextInput
                                id={`${index}_name`}
                                value={attendee.name}
                                placeholder={translate(
                                    "common.placeholder.name",
                                )}
                                disabled={!!paymentState.orderId}
                                datarequired="true"
                                onChange={(e) => {
                                    updateAttendees(
                                        index,
                                        "name",
                                        e.target.value,
                                    );
                                    validateField(
                                        `attendees.${index}.name`,
                                        e.target.value,
                                        { attendees },
                                    );
                                }}
                                onBlur={(e) =>
                                    validateField(
                                        `attendees.${index}.name`,
                                        e.target.value,
                                        { attendees },
                                    )
                                }
                                error={errors[`attendees.${index}.name`]}
                            />
                            <TextInput
                                id={`${index}_email`}
                                type="email"
                                value={attendee.email}
                                datarequired="true"
                                placeholder={translate(
                                    "common.placeholder.email",
                                )}
                                disabled={!!paymentState.orderId}
                                onChange={(e) => {
                                    updateAttendees(
                                        index,
                                        "email",
                                        e.target.value,
                                    );
                                    validateField(
                                        `attendees.${index}.email`,
                                        e.target.value,
                                        { attendees },
                                    );
                                }}
                                onBlur={(e) =>
                                    validateField(
                                        `attendees.${index}.email`,
                                        e.target.value,
                                        { attendees },
                                    )
                                }
                                error={errors[`attendees.${index}.email`]}
                            />
                            <SelectInput
                                id={`${index}_gender`}
                                placeholder={translate(
                                    "common.placeholder.gender",
                                )}
                                isDisabled={!!paymentState.orderId}
                                datarequired="true"
                                options={genderOptions}
                                value={genderOptions.find(
                                    (option) =>
                                        option.value === attendee.gender,
                                )}
                                onChange={(selectedOption) => {
                                    updateAttendees(
                                        index,
                                        "gender",
                                        selectedOption.value,
                                    );
                                    validateField(
                                        `attendees.${index}.gender`,
                                        selectedOption.value,
                                        { attendees },
                                    );
                                }}
                                onBlur={() =>
                                    validateField(
                                        `attendees.${index}.gender`,
                                        attendee.gender,
                                        { attendees },
                                    )
                                }
                                error={errors[`attendees.${index}.gender`]}
                            />
                            <DatePicker
                                id={`${index}_dob`}
                                value={attendee.dob}
                                placeholder={translate(
                                    "common.placeholder.dob",
                                )}
                                disabled={!!paymentState.orderId}
                                className="form-control mt-2"
                                onChange={(value) => {
                                    updateAttendees(index, "dob", value);
                                    validateField(
                                        `attendees.${index}.dob`,
                                        value,
                                        { attendees },
                                    );
                                }}
                                onBlur={() =>
                                    validateField(
                                        `attendees.${index}.dob`,
                                        attendee.dob,
                                        { attendees },
                                    )
                                }
                                maxDate={today}
                                error={errors[`attendees.${index}.dob`]}
                            />
                        </div>
                    </div>
                ))}
            </div>
            <div className="flex justify-between mt-4">
                <button className="btn btn-outline" onClick={prevStep}>
                    {translate("ticket.back_btn", "Back")}
                </button>
                <button className="btn btn-primary" onClick={handleNext}>
                    {translate("ticket.continue_btn", "Continue")}
                </button>
            </div>
        </>
    );
}
