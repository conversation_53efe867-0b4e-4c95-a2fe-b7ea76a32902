import { useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function RestrictionsSection({
    reservation,
    isAgreed,
    updateIsAgreed,
}) {
    const { translate } = useSSRTranslations();
    const [isOpen, setIsOpen] = useState(false);

    return (
        <>
            <div className="mt-4">
                <div
                    className={`collapse collapse-plus border border-base-300 rounded-box 
                            ${isOpen ? "collapse-open" : ""}`}
                    onClick={() => setIsOpen((prev) => !prev)}
                >
                    <div className="collapse-title font-semibold flex justify-between items-center cursor-pointer">
                        <span>
                            {translate(
                                "ticket.event_ticket_term_text",
                                "Event & Ticket Terms",
                            )}
                        </span>
                    </div>
                    <div className="collapse-content text-sm text-gray-700">
                        {Object.keys(reservation.ticket.event.restrictions)
                            .length > 0 && (
                            <div className="mt-1">
                                <p className="font-bold">
                                    {translate(
                                        "ticket.event_term_text",
                                        "Event Terms",
                                    )}
                                </p>
                                <ul className="list-disc ml-5">
                                    {Object.entries(
                                        reservation.ticket.event.restrictions,
                                    ).map(([id, name]) => (
                                        <li key={id}>{name}</li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {Object.keys(reservation.ticket.restrictions).length >
                            0 && (
                            <div className="mt-2">
                                <p className="font-bold">
                                    {translate(
                                        "ticket.ticket_term_text",
                                        "Ticket Terms",
                                    )}
                                </p>
                                <ul className="list-disc ml-5">
                                    {Object.entries(
                                        reservation.ticket.restrictions,
                                    ).map(([id, name]) => (
                                        <li key={id}>{name}</li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <div className="flex items-start text-sm text-gray-700">
                <label>
                    <input
                        type="checkbox"
                        className="checkbox checkbox-sm"
                        checked={isAgreed.restrictions}
                        onChange={(e) =>
                            updateIsAgreed("restrictions", e.target.checked)
                        }
                    />
                </label>
                <span className="ml-2">
                    {translate(
                        "ticket.read_agree_text",
                        "I have read and agree to the",
                    )}{" "}
                    {translate(
                        "ticket.event_ticket_term_text",
                        "Event & Ticket Terms",
                    )}
                </span>
            </div>
        </>
    );
}
