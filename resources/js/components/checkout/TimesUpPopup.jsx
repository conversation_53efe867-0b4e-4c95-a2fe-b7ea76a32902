import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function TimesUpPopup({ open, onClose }) {
    const { translate } = useSSRTranslations();

    return (
        <div className={`modal ${open ? "modal-open" : ""}`}>
            <div className="modal-box max-w-sm">
                <h3 className="text-lg font-bold mb-4 text-center">
                    {translate("ticket.times_up_text", "Time's Up!")}
                </h3>
                <div className="divider"></div>
                <p className="mb-4 text-center">
                    {translate(
                        "ticket.times_up_desc",
                        "Click the button below to go back to the ticket selection page for this event.",
                    )}
                </p>
                <div className="modal-action flex justify-center">
                    <button
                        onClick={onClose}
                        className="btn btn-primary btn-sm"
                    >
                        {translate("ticket.event_back_btn", "Go Back to Event")}
                    </button>
                </div>
            </div>
        </div>
    );
}
