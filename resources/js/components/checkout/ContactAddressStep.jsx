import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function ContactAddressStep() {
    const { translate } = useSSRTranslations();

    const { reservation, nextStep, prevStep } = useTicketCheckout();
    return (
        <>
            <h2 className="text-xl font-bold mb-3">
                {translate(
                    "ticket.contact_address_text",
                    "Contact & Address Information",
                )}
            </h2>
            <div className="text-sm text-gray-700 mb-2">
                {translate("common.labels.email", "Email")} :{" "}
                <strong>{reservation.user.email}</strong>
            </div>
            <div className="text-sm text-gray-700 mb-2">
                {translate("common.labels.phone", "Phone")} :{" "}
                <strong>{reservation.user.user_detail.phone}</strong>
            </div>
            <div className="text-sm text-gray-700 mb-2">
                {translate("common.labels.address", "Address")} :{" "}
                <strong>
                    {reservation.user.user_detail.address},{" "}
                    {reservation.user.user_detail.city},{" "}
                    {reservation.user.user_detail.country?.name}
                    {reservation.user.user_detail.country && ", "}
                    {reservation.user.user_detail.zip}
                </strong>
            </div>
            <div className="flex justify-between mt-4">
                <button className="btn btn-outline" onClick={prevStep}>
                    {translate("ticket.back_btn", "Back")}
                </button>
                <button className="btn btn-primary" onClick={nextStep}>
                    {translate("ticket.continue_btn", "Continue")}
                </button>
            </div>
        </>
    );
}
