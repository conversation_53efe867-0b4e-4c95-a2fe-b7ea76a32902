import QuantityConfirmStep from "@/components/checkout/QuantityConfirmStep";
import ContactAddressStep from "@/components/checkout/ContactAddressStep";
import AttendeesStep from "@/components/checkout/AttendeesStep";
import ReviewConfirmStep from "@/components/checkout/ReviewConfirmStep";
import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function CheckoutStepsSection() {
    const { step } = useTicketCheckout();
    const { translate } = useSSRTranslations();

    return (
        <>
            <ul className="steps mb-5 w-full">
                <li className={`step ${step >= 1 ? "step-primary" : ""}`}>
                    {translate("ticket.step_1_text", "Confirm Quantity")}
                </li>
                <li className={`step ${step >= 2 ? "step-primary" : ""}`}>
                    {translate("ticket.step_2_text", "Contact & Address")}
                </li>
                <li className={`step ${step >= 3 ? "step-primary" : ""}`}>
                    {translate("ticket.step_3_text", "Attendees")}
                </li>
                <li className={`step ${step >= 4 ? "step-primary" : ""}`}>
                    {translate("ticket.step_4_text", "Review")}
                </li>
            </ul>
            <div className="divider mt-0 mb-2"></div>
            {step === 1 && <QuantityConfirmStep />}

            {step === 2 && <ContactAddressStep />}

            {step === 3 && <AttendeesStep />}

            {step === 4 && <ReviewConfirmStep />}
        </>
    );
}
