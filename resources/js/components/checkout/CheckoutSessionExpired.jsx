import { Frown } from "lucide-react";
import { <PERSON> } from "@inertiajs/react";
import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function CheckoutSessionExpired() {
    const { translate } = useSSRTranslations();
    const { reservation } = useTicketCheckout();
    return (
        <div className="flex items-center justify-center py-12">
            <div className="text-center py-16 px-6 bg-white shadow-xl rounded-2xl max-w-lg">
                <div className="flex justify-center mb-6">
                    <Frown className="w-16 h-16 text-primary animate-bounce" />
                </div>
                <h4 className="text-2xl font-extrabold text-gray-800 mb-4">
                    {translate("ticket.session_expired", "Session expired")}
                </h4>
                <p className="text-xl text-gray-600 mb-6">
                    {translate(
                        "ticket.session_expired_desc",
                        "Your ticket purchase session has timed out. Kindly use the link below to initiate a new order.",
                    )}
                </p>
                {reservation ? (
                    <Link
                        key={`${reservation.ticket.event.id}`}
                        href={route(
                            "detail.show",
                            reservation.ticket.event.slug,
                        )}
                        className="inline-block bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200"
                    >
                        {translate("ticket.event_back_btn", "Go Back to Event")}
                    </Link>
                ) : (
                    <Link
                        key="events"
                        href={route("events")}
                        className="inline-block bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200"
                    >
                        {translate("ticket.event_back_btn", "Go Back to Event")}
                    </Link>
                )}
            </div>
        </div>
    );
}
