import useTicketCheckout from "@/hooks/useTicketCheckout";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function QuantityConfirmStep() {
    const { reservation, nextStep } = useTicketCheckout();
    const { translate } = useSSRTranslations();

    return (
        <>
            <div>
                <h4 className="font-bold">
                    {translate(
                        "ticket.confirm_text",
                        "Please confirm the tickets you would like to purchase",
                    )}
                </h4>
                <p className="mt-2 font-medium">
                    {reservation.ticket.sector.name}
                </p>
                <p className="mt-2">
                    {translate("ticket.total_tickets", "Total Tickets")}:{" "}
                    {reservation.quantity}
                </p>
                <button
                    className="btn btn-primary mt-4 float-right"
                    onClick={nextStep}
                >
                    {translate("ticket.continue_btn", "Continue")}
                </button>
            </div>
        </>
    );
}
