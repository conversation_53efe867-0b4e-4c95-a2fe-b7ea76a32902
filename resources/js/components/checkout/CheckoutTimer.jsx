import { useEffect, useRef, useState } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import useSSRTranslations from "@/hooks/useSSRTranslations";

dayjs.extend(utc);

export default function CheckoutTimer({ expiryDateTime, onExpire }) {
    const { translate } = useSSRTranslations();

    const [timeLeft, setTimeLeft] = useState(getRemainingTime());
    const intervalRef = useRef(null);

    function getRemainingTime() {
        const now = dayjs(); // Local time
        const expiry = dayjs.utc(expiryDateTime).local(); // Convert UTC to local
        const diffMs = expiry.diff(now);

        const expired = diffMs <= 0;
        const totalSeconds = Math.max(Math.floor(diffMs / 1000), 0);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;

        return { minutes, seconds, expired };
    }

    useEffect(() => {
        intervalRef.current = setInterval(() => {
            const newTime = getRemainingTime();
            if (newTime.expired) {
                clearInterval(intervalRef.current);
                onExpire(true);
            }
            setTimeLeft(newTime);
        }, 1000);

        return () => clearInterval(intervalRef.current);
    }, [expiryDateTime]);

    const padded = (num) => String(num).padStart(2, "0");

    return (
        <div className="flex items-center gap-2 text-sm font-semibold text-error">
            <span>
                {translate(
                    "common.complete_booking_in",
                    "Complete your booking in",
                )}
            </span>
            <span className="font-mono text-lg tracking-wider">
                {padded(timeLeft.minutes)}:{padded(timeLeft.seconds)}
            </span>
            <span>{translate("common.mins", "mins")}</span>
        </div>
    );
}
