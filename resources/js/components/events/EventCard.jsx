import { Calendar, MapPin, ShieldCheck } from "lucide-react";
import { router } from "@inertiajs/react";
import dayjs from "dayjs";

export default function EventCard({
    event,
    translationFrom,
    translationBuyTickets,
}) {
    const goToEvent = () => {
        router.visit(route("detail.show", event.slug));
    };

    const goToStadium = (e) => {
        e.stopPropagation();
        router.visit(route("detail.show", event.stadium.slug));
    };

    const goToLeague = (e) => {
        e.stopPropagation();
        router.visit(route("detail.show", event.league.slug));
    };
    return (
        <div
            className="card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow"
            onClick={goToEvent}
        >
            <figure className="relative h-48 bg-gray-200">
                {event.image !== "" ? (
                    <img
                        src={event.image}
                        alt={event.image_alt || event.name}
                        className="w-full h-full object-cover"
                    />
                ) : (
                    <img src="/img/ticketgol-logo.png" alt={event.name} />
                )}
                <div className="absolute top-4 right-4 badge badge-warning">
                    {translationFrom} €{event.min_price}
                </div>
            </figure>
            <div className="card-body p-5">
                <div
                    className="tooltip tooltip-neutral w-fit text-left"
                    data-tip={event.name}
                    tabIndex={0}
                >
                    <h2 className="card-title line-clamp-1 max-w-xs">
                        {event.name}
                    </h2>
                </div>
                <div className="flex items-center text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span className="text-sm">
                        {dayjs(event.date).format("DD/MM/YYYY")}
                    </span>
                </div>
                <div className="flex items-center text-gray-600 hover:underline">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span className="text-sm truncate" onClick={goToStadium}>
                        {event.stadium_name}
                    </span>
                </div>
                <div className="flex items-center text-gray-600 hover:underline">
                    <ShieldCheck className="w-4 h-4 mr-2" />
                    <span className="text-sm truncate" onClick={goToLeague}>
                        {event.league_name}
                    </span>
                </div>
                <div className="card-actions justify-end mt-2">
                    <button className="btn btn-primary btn-sm">
                        {translationBuyTickets}
                    </button>
                </div>
            </div>
        </div>
    );
}
