import { Calendar, MapPin, ShieldCheck } from "lucide-react";
import { Link, router } from "@inertiajs/react";
import dayjs from "dayjs";

export default function SellTicketEventCard({ event, buttonTitle }) {
    return (
        <div className="card bg-base-100 shadow-md border border-base-300">
            <div className="card-body p-5">
                <div className="flex justify-between items-center gap-2 border-b pb-2">
                    <div
                        className="tooltip tooltip-bottom tooltip-neutral w-full text-left"
                        data-tip={event.name}
                        tabIndex={0}
                    >
                        <h2 className="card-title line-clamp-1 max-w-full">
                            {event.name}
                        </h2>
                    </div>

                    <Link
                        className="btn btn-primary btn-outline btn-sm shrink-0 whitespace-nowrap"
                        href={route("ticket.sell", event.slug)}
                    >
                        {buttonTitle}
                    </Link>
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                    <figure className="relative h-20 bg-gray-200 object-cover rounded border border-base-300">
                        {event.image !== "" ? (
                            <img
                                src={event.image}
                                alt={event.image_alt || event.name}
                                className="w-24 h-full object-cover"
                            />
                        ) : (
                            <img
                                src="/img/ticketgol-logo.png"
                                alt={event.name}
                                className="w-24"
                            />
                        )}
                    </figure>

                    <div className="flex flex-col justify-center gap-1 text-gray-600 sm:max-w-[calc(100%-6rem)]">
                        <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-2" />
                            <span className="text-sm">
                                {dayjs(event.date).format("DD/MM/YYYY")}
                            </span>
                        </div>

                        <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-2" />
                            <span className="text-sm">
                                {event.stadium_name}
                            </span>
                        </div>

                        <div className="flex items-center">
                            <ShieldCheck className="w-4 h-4 mr-2" />
                            <span className="text-sm">{event.league_name}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
