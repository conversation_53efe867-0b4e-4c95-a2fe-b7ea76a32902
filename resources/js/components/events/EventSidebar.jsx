import { useEffect, useRef } from "react";

import { XCircle } from "lucide-react";
import { useDebounce } from "@uidotdev/usehooks";
import FilterCheckboxCollapse from "@/components/forms/FilterCheckboxCollapse";
import FilterSelectCollapse from "@/components/forms/FilterSelectCollapse";
import useEvents from "@/hooks/useEvents";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { selectFiltersConfig } from "@/constants/events";

export default function EventSidebar() {
    const { translate } = useSSRTranslations();

    const {
        filters,
        clearFilters,
        updateFilter,
        filterOptions,
        updateCategories,
        refreshEvents,
        isFilterCleared,
        updatesFilterCleared,
    } = useEvents();
    const debouncedFilters = useDebounce(filters, 800);
    const isFirstRender = useRef(true);

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }
        if (isFilterCleared) {
            updatesFilterCleared(false);
            return;
        }

        refreshEvents();
    }, [debouncedFilters]);

    return (
        <aside className="md:w-1/4 w-full bg-base-100 px-6 rounded-box shadow-md max-h-screen overflow-y-auto md:sticky md:top-3">
            <h2 className="font-semibold mt-8 mb-4">
                {translate("events.filters_title")}
            </h2>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <input
                    type="text"
                    placeholder={translate("events.search_placeholder")}
                    className="input input-bordered w-full"
                    value={filters?.search || ""}
                    onChange={(e) => updateFilter("search", e.target.value)}
                />

                <button
                    onClick={clearFilters}
                    className="text-gray-500 hover:text-gray-800 transition-colors"
                    title={translate("events.reset_filters")}
                >
                    <XCircle size={24} />
                </button>
            </div>

            <div className="divider m-0"></div>

            <FilterCheckboxCollapse
                title={translate("events.categories_title")}
                options={filterOptions.categories}
                selectedOptions={filters.categories}
                onChange={(value) => {
                    updateCategories(value);
                }}
            />

            {selectFiltersConfig.map(({ key, titleKey, placeholderKey }) => (
                <FilterSelectCollapse
                    key={key}
                    title={translate(`events.${titleKey}`)}
                    placeholder={translate(`events.${placeholderKey}`)}
                    options={filterOptions[key]}
                    selectedOption={filters[key]}
                    onChange={(value) => {
                        updateFilter(key, value);
                    }}
                    isMulti
                />
            ))}
        </aside>
    );
}
