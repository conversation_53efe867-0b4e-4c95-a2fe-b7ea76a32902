import { useEffect, useRef, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import EventCard from "@/components/events/EventCard";
import useSSRTranslations from "@/hooks/useSSRTranslations";

const EventCarousel = ({ events }) => {
    const { translate } = useSSRTranslations();

    const containerRef = useRef(null);
    const trackRef = useRef(null);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [visibleItems, setVisibleItems] = useState(4);
    const [slideWidth, setSlideWidth] = useState(0);

    const updateLayout = () => {
        // Check if window is available (not during SSR)
        if (typeof window === "undefined") {
            setVisibleItems(4); // Default for SSR
            return;
        }

        const width = window.innerWidth;
        if (width >= 1280) setVisibleItems(4);
        else if (width >= 1024) setVisibleItems(3);
        else if (width >= 640) setVisibleItems(2);
        else setVisibleItems(1);
    };

    const calculateSlideWidth = () => {
        if (containerRef.current) {
            const containerWidth = containerRef.current.offsetWidth;
            setSlideWidth(containerWidth / visibleItems);
        }
    };

    useEffect(() => {
        updateLayout();

        // Only add event listeners if window is available
        if (typeof window !== "undefined") {
            window.addEventListener("resize", updateLayout);
            return () => window.removeEventListener("resize", updateLayout);
        }
    }, []);

    useEffect(() => {
        calculateSlideWidth();
    }, [visibleItems, events.length]);

    useEffect(() => {
        if (trackRef.current) {
            trackRef.current.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
        }
    }, [currentIndex, slideWidth]);

    const maxIndex = Math.max(events.length - visibleItems, 0);

    const next = () => {
        setCurrentIndex((prev) => Math.min(prev + 1, maxIndex));
    };

    const prev = () => {
        setCurrentIndex((prev) => Math.max(prev - 1, 0));
    };

    return (
        <div className="relative w-full overflow-hidden" ref={containerRef}>
            <div
                ref={trackRef}
                className="flex transition-transform duration-500 ease-in-out"
                style={{ width: `${events.length * slideWidth}px` }}
            >
                {events.map((event, idx) => (
                    <div
                        key={event.id || idx}
                        style={{ width: `${slideWidth}px` }}
                        className="px-3 py-10 flex-shrink-0 hover:scale-[1.02] cursor-pointer transition-transform duration-150"
                    >
                        <EventCard
                            event={event}
                            translationFrom={translate("common.from")}
                            translationBuyTickets={translate(
                                "common.buy_tickets",
                            )}
                        />
                    </div>
                ))}
            </div>

            {events.length > visibleItems && (
                <>
                    {currentIndex > 0 && (
                        <div className="absolute inset-y-0 left-2 flex items-center z-50">
                            <button onClick={prev} className="btn btn-circle">
                                <ChevronLeft className="w-6 h-6" />
                            </button>
                        </div>
                    )}
                    {currentIndex < maxIndex && (
                        <div className="absolute inset-y-0 right-2 flex items-center z-50">
                            <button onClick={next} className="btn btn-circle">
                                <ChevronRight className="w-6 h-6" />
                            </button>
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default EventCarousel;
