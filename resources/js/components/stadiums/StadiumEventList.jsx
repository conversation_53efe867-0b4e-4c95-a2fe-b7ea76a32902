import { useEffect } from "react";
import useInfiniteScroll from "react-infinite-scroll-hook";

import EventCard from "@/components/events/EventCard";
import SelectInput from "@/components/forms/SelectInput";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useStadiumDetail from "@/hooks/useStadiumDetail";
import useEvents from "@/hooks/useEvents";

export default function StadiumEventList() {
    const { translate } = useSSRTranslations();
    const { stadium } = useStadiumDetail();

    const {
        events,
        filters,
        updateFilter,
        eventLoading,
        loadMoreEvents,
        clearNextPageUrl,
        clearFilters,
        nextPageUrl,
        fetchEventsInitially,
    } = useEvents();

    useEffect(() => {
        clearFilters();
        clearNextPageUrl();
        updateFilter("stadiums", [stadium.id]);
    }, [stadium.id]);

    useEffect(() => {
        const timer = setTimeout(() => {
            fetchEventsInitially();
        }, 50);

        return () => clearTimeout(timer);
    }, [filters]);

    const [observerRef] = useInfiniteScroll({
        loading: eventLoading,
        hasNextPage: !!nextPageUrl,
        onLoadMore: loadMoreEvents,
        rootMargin: "100px",
    });

    return (
        <>
            <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-2 mb-2 mt-8">
                <h1 className="text-base lg:text-2xl md:text-lg font-bold">
                    {translate("stadiums.events_in_text")} {stadium.name}
                </h1>
                <SelectInput
                    wrapperClass="w-full md:w-1/4"
                    options={translate("stadiums.event_sort_options")}
                    value={
                        filters.sort
                            ? translate("stadiums.event_sort_options").find(
                                  (option) => option.value === filters.sort,
                              )
                            : null
                    }
                    onChange={(selected) =>
                        updateFilter("sort", selected.value)
                    }
                    placeholder={translate("stadiums.sort_by_placeholder")}
                />
            </div>
            {!eventLoading && events.length === 0 ? (
                <div className="flex flex-col items-center justify-center px-5 h-96 bg-base-100 mt-5 rounded-xl shadow-sm">
                    <h2 className="text-2xl font-semibold text-base-content">
                        {translate("stadiums.no_events")}
                    </h2>
                </div>
            ) : (
                <>
                    <div className="container mx-auto py-5 grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                        {events.map((event, index) => (
                            <div
                                key={event.id}
                                className="hover:scale-[1.02] cursor-pointer transition-transform duration-150"
                            >
                                <EventCard
                                    event={event}
                                    translationFrom={translate("common.from")}
                                    translationBuyTickets={translate(
                                        "common.buy_tickets",
                                    )}
                                />
                            </div>
                        ))}
                    </div>
                    {nextPageUrl && (
                        <div ref={observerRef} className="h-10"></div>
                    )}

                    {eventLoading && (
                        <p className="flex items-center justify-center h-32">
                            <span className="loading loading-bars loading-xl"></span>
                        </p>
                    )}
                </>
            )}
        </>
    );
}
