import { useEffect, useRef } from "react";

import { XCircle } from "lucide-react";
import { useDebounce } from "@uidotdev/usehooks";
import FilterSelectCollapse from "@/components/forms/FilterSelectCollapse";
import useStadiums from "@/hooks/useStadiums";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function StadiumSidebar() {
    const { translate } = useSSRTranslations();

    const {
        filters,
        clearFilters,
        updateFilter,
        filterOptions,
        refreshStadiums,
    } = useStadiums();
    const debouncedFilters = useDebounce(filters, 800);
    const isFirstRender = useRef(true);

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        refreshStadiums();
    }, [debouncedFilters]);

    return (
        <aside className="md:w-1/4 w-full bg-base-100 px-6 rounded-box shadow-md max-h-screen overflow-y-auto md:sticky md:top-3">
            <h2 className="font-semibold mt-8 mb-4">
                {translate("stadiums.filters_title", "Filters")}
            </h2>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <input
                    type="text"
                    placeholder={translate(
                        "stadiums.search_placeholder",
                        "Search stadiums...",
                    )}
                    className="input input-bordered w-full"
                    value={filters?.search || ""}
                    onChange={(e) => updateFilter("search", e.target.value)}
                />

                <button
                    onClick={clearFilters}
                    className="text-gray-500 hover:text-gray-800 transition-colors"
                    title={translate("stadiums.reset_filters", "Reset Filters")}
                >
                    <XCircle size={24} />
                </button>
            </div>

            <div className="divider m-0"></div>
            <FilterSelectCollapse
                key="countries"
                title={translate("stadiums.countries_title", "Countries")}
                placeholder={translate(
                    "stadiums.countries_placeholder",
                    "Select countries",
                )}
                options={filterOptions["countries"]}
                selectedOption={filters["countries"]}
                onChange={(value) => {
                    updateFilter("countries", value);
                }}
                isMulti
            />
        </aside>
    );
}
