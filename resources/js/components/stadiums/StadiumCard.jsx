import { MapPin } from "lucide-react";

export default function StadiumCard({ stadium }) {
    return (
        <div className="card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure className="relative h-48 bg-gray-200">
                {stadium.image !== "" ? (
                    <img
                        src={stadium.image}
                        alt={stadium.image_alt || stadium.name}
                        className="w-full h-full object-cover"
                    />
                ) : (
                    <img src="/img/ticketgol-logo.png" alt={stadium.name} />
                )}
            </figure>
            <div className="card-body">
                <div
                    className="tooltip tooltip-neutral w-fit text-left"
                    data-tip={stadium.name}
                    tabIndex={0}
                >
                    <h2 className="card-title line-clamp-1 max-w-xs">
                        {stadium.name}
                    </h2>
                </div>
                <div className="flex items-center text-gray-600">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span className="text-sm truncate">
                        {stadium.address_line_1}, {stadium.address_line_2},{" "}
                        {stadium.postcode}
                    </span>
                </div>
            </div>
        </div>
    );
}
