// resources/js/components/support/SupportRequestModal.jsx
import { useEffect } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { X } from "lucide-react";
import SelectInput from "@/components/forms/SelectInput";
import useSupportRequests from "@/hooks/useSupportRequests";
import useAxiosForm from "@/hooks/useAxiosForm";
import toast from "react-hot-toast";
import FileUploadWithPreview from "@/components/forms/FileUploadWithPreview";
import Spinner from "@/components/ui/Spinner";

export default function SupportRequestModal({ isOpen, onClose, onSaved }) {
    const { translate } = useSSRTranslations();
    const { options } = useSupportRequests();

    const {
        data: formData,
        setFormData,
        post,
        errors,
        processing,
        reset,
    } = useAxiosForm({
        subject: "",
        message: "",
        request_type: "order_related",
        priority: "medium",
        files: [],
    });

    const requestTypeOptions = options?.requestTypes || [];
    const priorityOptions = options?.priorities || [];

    useEffect(() => {
        // Check if document is available (not during SSR)
        if (typeof document !== "undefined") {
            const modalElement = document.getElementById(
                "support_request_modal",
            );
            if (isOpen && modalElement) {
                modalElement.showModal();
            } else if (modalElement) {
                modalElement.close();
            }
        }
        reset();
    }, [isOpen]);

    if (!isOpen) return null;

    const handleSubmit = async (e) => {
        e.preventDefault();

        post(route("api.support-requests.store"), {
            headers: {
                "Content-Type": "multipart/form-data",
            },
            onSuccess: (response) => {
                toast.success(
                    response.message || "Support request created successfully",
                );
                onClose();
                onSaved();
            },
            onError: (errors) => {
                if (errors.message) {
                    toast.error(errors.message);
                } else {
                    toast.error("Support request creation failed");
                }
            },
        });
    };

    return (
        <dialog id="support_request_modal" className="modal">
            <div className="modal-box w-11/12 max-w-5xl bg-white">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="font-semibold text-lg">
                        {translate(
                            "support.create_new_request",
                            "Create New Support Request",
                        )}
                    </h3>
                    <button
                        onClick={onClose}
                        className="btn btn-sm btn-circle btn-ghost"
                    >
                        <X className="w-5 h-5" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <SelectInput
                            required
                            label={translate(
                                "support.request_type_label",
                                "Request Type",
                            )}
                            options={requestTypeOptions || []}
                            value={
                                requestTypeOptions?.find(
                                    (opt) =>
                                        opt.value === formData.request_type,
                                ) || null
                            }
                            onChange={(selected) => {
                                setFormData("request_type", selected?.value);
                            }}
                            error={errors.request_type}
                            placeholder={translate(
                                "support.request_type_label",
                                "Select request type",
                            )}
                        />

                        <SelectInput
                            label={translate(
                                "support.priority_label",
                                "Priority",
                            )}
                            options={priorityOptions || []}
                            value={
                                priorityOptions?.find(
                                    (opt) => opt.value === formData.priority,
                                ) || null
                            }
                            onChange={(selected) => {
                                setFormData("priority", selected?.value);
                            }}
                            error={errors.priority}
                            placeholder={translate(
                                "support.priority_label",
                                "Select priority",
                            )}
                        />
                    </div>

                    <div className="form-control">
                        <label className="label">
                            <span className="label-text">
                                {translate("support.subject_label", "Subject")}
                            </span>
                        </label>
                        <input
                            type="text"
                            name="subject"
                            value={formData.subject}
                            onChange={(e) => {
                                setFormData("subject", e.target.value);
                            }}
                            className={`input input-bordered ${errors.subject ? "input-error" : ""}`}
                            placeholder={translate(
                                "support.subject_placeholder",
                                "Brief description of your issue",
                            )}
                        />
                        {errors.subject && (
                            <label className="label">
                                <span className="label-text-alt text-error">
                                    {errors.subject}
                                </span>
                            </label>
                        )}
                    </div>

                    <div className="form-control">
                        <label className="label">
                            <span className="label-text">
                                {translate("support.message", "Message")}
                            </span>
                        </label>
                        <textarea
                            name="message"
                            value={formData.message}
                            onChange={(e) => {
                                setFormData("message", e.target.value);
                            }}
                            rows="5"
                            className={`textarea textarea-bordered ${errors.message ? "textarea-error" : ""}`}
                            placeholder={translate(
                                "support.message_placeholder",
                                "Please provide details about your issue",
                            )}
                        ></textarea>
                        {errors.message && (
                            <label className="label">
                                <span className="label-text-alt text-error">
                                    {errors.message}
                                </span>
                            </label>
                        )}
                    </div>

                    <div className="form-control">
                        <FileUploadWithPreview
                            label={translate(
                                "support.upload_files",
                                "Upload files",
                            )}
                            files={formData.files}
                            setFiles={(files) => setFormData("files", files)}
                            maxFiles={1}
                        />
                    </div>

                    <div className="modal-action mt-6">
                        <button
                            type="button"
                            onClick={onClose}
                            className="btn btn-outline px-5 mr-2"
                        >
                            {translate("common.cancel", "Cancel")}
                        </button>
                        <button
                            type="submit"
                            disabled={processing}
                            className="btn btn-primary px-7"
                        >
                            {processing ? (
                                <>
                                    <span className="mr-2">
                                        <Spinner size="xs" color="white" />
                                    </span>
                                    {translate(
                                        "common.submitting",
                                        "Submitting...",
                                    )}
                                </>
                            ) : (
                                translate("common.submit", "Submit")
                            )}
                        </button>
                    </div>
                </form>
            </div>

            {/* Modal backdrop with click handler */}
            <form method="dialog" className="modal-backdrop" onClick={onClose}>
                <button>close</button>
            </form>
        </dialog>
    );
}
