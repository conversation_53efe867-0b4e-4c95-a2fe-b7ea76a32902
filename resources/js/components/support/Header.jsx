// resources/js/components/support/Header.jsx
import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { Plus } from "lucide-react";
import PropTypes from "prop-types";

function Header({ openCreateModal }) {
    const { translate } = useSSRTranslations();

    return (
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-md mb-8 overflow-hidden">
            <div className="px-8 py-8 text-white">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                    <div>
                        <h1 className="text-3xl font-bold mb-2">
                            {translate("support.title", "My Support Requests")}
                        </h1>
                        <p className="text-blue-100 mb-4 md:mb-0">
                            {translate(
                                "support.page_description",
                                "Track and manage your support tickets",
                            )}
                        </p>
                    </div>
                    <div className="md:ml-4">
                        <button
                            onClick={openCreateModal}
                            className="bg-white text-blue-600 px-5 py-2.5 rounded-lg shadow hover:bg-blue-50 transition-all font-medium flex items-center"
                        >
                            <Plus className="h-5 w-5 mr-2" />
                            {translate(
                                "support.create_new_request_btn",
                                "Create New Request",
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

Header.propTypes = {
    openCreateModal: PropTypes.func.isRequired,
};

export default Header;
