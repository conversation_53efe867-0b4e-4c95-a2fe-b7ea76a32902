import React from "react";
import useSupportRequests from "@/hooks/useSupportRequests";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { Filter } from "lucide-react";
import SelectInput from "@/components/forms/SelectInput";

export default function SupportRequestFilters() {
    const {
        filters,
        updateFilter,
        clearFilters,
        options,
        fetchSupportTickets,
    } = useSupportRequests();
    const { translate } = useSSRTranslations();

    const getSelectedOptions = (filterValue, optionsArray) => {
        if (!filterValue) return [];

        return filterValue.map((value) => {
            const option = optionsArray.find((opt) => opt.value === value);
            return option ? option : { value, label: value };
        });
    };

    const handleFilterChange = (key, selected) => {
        const value = selected ? selected.map((item) => item.value) : [];
        updateFilter(key, value);
    };

    const applyFilters = () => {
        fetchSupportTickets();
    };

    const handleClearFilters = () => {
        clearFilters();
        fetchSupportTickets(true);
    };

    return (
        <div className="bg-white rounded-xl shadow-sm p-4 mb-6 border border-gray-100">
            <div className="mb-4">
                <h3 className="font-medium text-gray-700 mb-2">
                    {translate(
                        "support.filters_title",
                        "Filter Support Requests",
                    )}
                </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <SelectInput
                        label={translate("support.type_label", "Request Type")}
                        placeholder={translate(
                            "support.all_types",
                            "All Types",
                        )}
                        value={getSelectedOptions(
                            filters.requestType,
                            options.requestTypes,
                        )}
                        onChange={(selected) =>
                            handleFilterChange("requestType", selected)
                        }
                        options={options.requestTypes}
                        isMulti
                        isClearable
                    />
                </div>

                <div>
                    <SelectInput
                        label={translate("support.status_label", "Status")}
                        placeholder={translate(
                            "support.all_statuses",
                            "All Statuses",
                        )}
                        value={getSelectedOptions(
                            filters.status,
                            options.statuses,
                        )}
                        onChange={(selected) =>
                            handleFilterChange("status", selected)
                        }
                        options={options.statuses}
                        isMulti
                        isClearable
                    />
                </div>

                <div>
                    <SelectInput
                        label={translate("support.priority_label", "Priority")}
                        placeholder={translate(
                            "support.all_priorities",
                            "All Priorities",
                        )}
                        value={getSelectedOptions(
                            filters.priority,
                            options.priorities,
                        )}
                        onChange={(selected) =>
                            handleFilterChange("priority", selected)
                        }
                        options={options.priorities}
                        isMulti
                        isClearable
                    />
                </div>
            </div>

            <div className="flex justify-end items-center space-x-3 mt-4">
                <button
                    onClick={handleClearFilters}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-blue-500/20"
                >
                    {translate("support.clear_filters", "Clear Filters")}
                </button>
                <button
                    onClick={applyFilters}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-700 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-500/20"
                >
                    <Filter className="h-4 w-4 mr-2" />
                    {translate("support.apply_filters", "Apply Filters")}
                </button>
            </div>
        </div>
    );
}
