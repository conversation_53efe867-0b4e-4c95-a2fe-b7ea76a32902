// resources/js/components/support/SupportRequestCard.jsx
/* eslint-disable react/prop-types */
import React from "react";
import { Link } from "@inertiajs/react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useSupportRequests from "@/hooks/useSupportRequests";
import { convertToLocalTimezone } from "@/helpers/dateTimeUtils";
import {
    getSupportStatusBadgeClass,
    getSupportPriorityBadgeClass,
} from "@/helpers/badgeClassUtils";
import {
    getSupportPriorityIcon,
    getSupportStatusIcon,
} from "@/helpers/lucideIconUtils";
import { Tag, MessageSquare, ArrowRight, Calendar } from "lucide-react";

export default function SupportRequestCard({ supportRequest }) {
    const { translate } = useSSRTranslations();
    const { options } = useSupportRequests();

    const getOptionLabel = (key, value) => {
        if (!options || !options[key] || !value) return value;

        const option = options[key].find((opt) => opt.value === value);
        return option ? option.label : value;
    };

    const createdAt = convertToLocalTimezone(supportRequest.created_at);
    const updatedAt = convertToLocalTimezone(supportRequest.updated_at);

    return (
        <Link
            href={route("my-account.support.show", supportRequest.id)}
            className="block bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 overflow-hidden"
        >
            <div className="flex flex-col h-full">
                {/* Top status bar */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-3 flex justify-between items-center border-b border-gray-100">
                    <div className="flex items-center space-x-2">
                        <span className="text-gray-500 font-medium text-sm">
                            #{supportRequest.sr_no}
                        </span>
                        <span className="text-slate-400">•</span>

                        <span className="text-gray-500 text-sm">
                            {createdAt.toFormattedDateTime(
                                "DD/MM/YYYY | h:mm A",
                            )}{" "}
                            ({createdAt.getShortTimezoneName()})
                        </span>
                    </div>
                    <div className="flex space-x-2">
                        <span
                            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSupportPriorityBadgeClass(supportRequest.priority)}`}
                        >
                            {getSupportPriorityIcon(supportRequest.priority)}
                            {getOptionLabel(
                                "priorities",
                                supportRequest.priority,
                            )}
                        </span>
                    </div>
                </div>

                {/* Main content */}
                <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                        <h3 className="text-xl font-semibold text-gray-800">
                            {supportRequest.subject}
                        </h3>
                        <span
                            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getSupportStatusBadgeClass(supportRequest.status)}`}
                        >
                            {getSupportStatusIcon(supportRequest.status)}
                            {getOptionLabel("statuses", supportRequest.status)}
                        </span>
                    </div>

                    <div className="mb-4">
                        <p className="text-gray-600 line-clamp-2 min-h-[3em]">
                            {supportRequest.latest_message?.content ||
                                translate(
                                    "support.no_messages",
                                    "No messages yet",
                                )}
                        </p>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-4">
                        <div className="flex items-center text-xs bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md border border-blue-100">
                            <Tag className="w-3.5 h-3.5 mr-1.5" />
                            <span>
                                {getOptionLabel(
                                    "requestTypes",
                                    supportRequest.request_type,
                                )}
                            </span>
                        </div>
                        <div className="flex items-center text-xs bg-indigo-50 text-indigo-700 px-3 py-1.5 rounded-md border border-indigo-100">
                            <MessageSquare className="w-3.5 h-3.5 mr-1.5" />
                            <span>
                                {translate("support.message_count", "Messages")}
                                : {supportRequest.message_count || 0}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="mt-auto border-t border-gray-100 bg-gray-50 px-6 py-3 flex justify-between items-center">
                    <div className="flex items-center gap-3 text-xs text-gray-500">
                        <div className="flex items-center">
                            <Calendar className="w-3.5 h-3.5 mr-1.5 text-gray-400" />
                            <span>
                                Updated:{" "}
                                {updatedAt.toFormattedDateTime(
                                    "DD/MM/YYYY | h:mm A",
                                )}{" "}
                                ({updatedAt.getShortTimezoneName()})
                            </span>
                        </div>
                    </div>
                    <div className="flex items-center group">
                        <span className="text-sm text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                            {translate("support.view_details", "View Details")}
                            <ArrowRight className="w-4 h-4 inline ml-1 transform group-hover:translate-x-1 transition-transform" />
                        </span>
                    </div>
                </div>
            </div>
        </Link>
    );
}
