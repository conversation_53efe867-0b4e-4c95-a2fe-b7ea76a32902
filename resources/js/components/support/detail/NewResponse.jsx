import React, { useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useSupportRequestDetail from "@/hooks/useSupportRequestDetail";
import { Send, Paperclip, MessageSquare, X } from "lucide-react";
import Spinner from "@/components/ui/Spinner";
import FileUploadWithPreview from "@/components/forms/FileUploadWithPreview";
import DaisyModal from "@/components/ui/DaisyModal";

function NewResponse({ requestTicketId }) {
    const {
        newMessage,
        setNewMessage,
        sendMessage,
        isMessageSending: sending,
    } = useSupportRequestDetail(requestTicketId);
    const { translate } = useSSRTranslations();

    const [showAttachmentModal, setShowAttachmentModal] = useState(false);
    const [newFiles, setNewFiles] = useState([]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (newMessage.trim()) {
            await sendMessage(newFiles);
            setNewFiles([]);
        }
    };

    return (
        <div className="border-t p-4 bg-gray-50 rounded-b-lg shadow-inner transition-all duration-300 hover:bg-gray-50/80">
            <div className="flex items-center mb-3 pb-2 border-b border-gray-200">
                <MessageSquare className="w-5 h-5 text-primary mr-2" />
                <h3 className="text-md font-medium text-gray-700">
                    {translate("support.new_response", "New Response")}
                </h3>
            </div>
            <div className="animate-fadeIn">
                {" "}
                {/* Changed from form to div */}
                <div className="w-full">
                    {/* Message Column with Pin Icon */}
                    <div className="relative border rounded-lg shadow-sm overflow-hidden">
                        <textarea
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            placeholder={translate(
                                "support.type_message",
                                "Type your message here...",
                            )}
                            className={`w-full border-none p-4 pr-12 resize-none transition-all duration-200 placeholder:text-gray-400 placeholder:text-sm h-32 focus:outline-none`}
                            disabled={sending}
                        ></textarea>
                        <div className="flex items-center justify-between bg-white px-4 py-2">
                            <div className="flex items-center">
                                <button
                                    type="button"
                                    onClick={() => setShowAttachmentModal(true)}
                                    className="text-gray-500 hover:text-primary mr-3 transition-colors duration-200 relative"
                                    aria-label={translate(
                                        "support.add_attachment",
                                        "Add attachment",
                                    )}
                                >
                                    <Paperclip className="w-5 h-5" />
                                    {newFiles.length > 0 && (
                                        <span className="absolute -top-2 -right-2 bg-primary text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                                            {newFiles.length}
                                        </span>
                                    )}
                                </button>
                                <span
                                    className={`text-xs ${newMessage.length > 500 ? "text-red-500 font-medium" : "text-gray-400"}`}
                                >
                                    {newMessage.length}/1000
                                </span>
                            </div>
                            <button
                                type="button"
                                onClick={handleSubmit}
                                className={`bg-primary text-white p-2 rounded-full flex items-center justify-center shadow-sm ${!newMessage.trim() ? "opacity-50 cursor-not-allowed" : "hover:bg-primary-dark hover:shadow-md transform hover:scale-105"} transition-all duration-200`}
                                disabled={sending || !newMessage.trim()}
                                aria-label={translate(
                                    "support.send_message_btn",
                                    "Send message",
                                )}
                            >
                                {sending ? (
                                    <Spinner size="xs" color="white" />
                                ) : (
                                    <Send className="w-5 h-5" />
                                )}
                            </button>
                        </div>
                    </div>

                    <DaisyModal
                        isOpen={showAttachmentModal}
                        onClose={() => setShowAttachmentModal(false)}
                        title={translate(
                            "support.add_attachments",
                            "Add Attachments",
                        )}
                        size="lg"
                    >
                        <div className="p-2">
                            <FileUploadWithPreview
                                label={translate(
                                    "support.attachment_note",
                                    "Attachments can be added when creating a new support request",
                                )}
                                files={newFiles}
                                setFiles={setNewFiles}
                                maxFiles={10}
                            />

                            {newFiles.length > 0 && (
                                <div className="mt-4 text-sm text-gray-500 flex items-center">
                                    <div className="badge badge-primary badge-lg gap-1">
                                        {newFiles.length}{" "}
                                        {newFiles.length === 1
                                            ? translate(
                                                  "support.file_attached_single",
                                                  "file attached",
                                              )
                                            : translate(
                                                  "support.files_attached_multiple",
                                                  "files attached",
                                              )}
                                    </div>
                                </div>
                            )}

                            <div className="modal-action mt-6">
                                <button
                                    type="button"
                                    onClick={() =>
                                        setShowAttachmentModal(false)
                                    }
                                    className="btn btn-primary"
                                >
                                    {translate("support.done", "Done")}
                                </button>
                            </div>
                        </div>
                    </DaisyModal>
                </div>
            </div>{" "}
            {/* Changed closing tag from form to div */}
        </div>
    );
}

export default NewResponse;
