import React, { useEffect, useRef } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import useSupportRequestDetail from "@/hooks/useSupportRequestDetail";
import { User, Clock, File } from "lucide-react";
import { convertToLocalTimezone } from "@/helpers/dateTimeUtils";

// Custom scrollbar styles for webkit browsers
const scrollbarStyles = `
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
        background-color: #d1d5db;
        border-radius: 20px;
    }
`;

function ChatWindow() {
    // Add scrollbar styles to document head
    useEffect(() => {
        // Check if document is available (not during SSR)
        if (typeof document !== "undefined") {
            const styleElement = document.createElement("style");
            styleElement.textContent = scrollbarStyles;
            document.head.appendChild(styleElement);

            return () => {
                document.head.removeChild(styleElement);
            };
        }
    }, []);

    const { translate } = useSSRTranslations();
    const { supportMessages, supportRequest, loading, canScrollToBottom } =
        useSupportRequestDetail();
    const messagesEndRef = useRef(null);

    // Scroll to bottom when messages change
    useEffect(() => {
        if (canScrollToBottom) {
            scrollToBottom();
        }
    }, [supportMessages]);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    return (
        <div
            className="h-[60vh] max-h-[600px] overflow-y-auto p-4 rounded-lg bg-white shadow-sm custom-scrollbar"
            style={{
                scrollbarWidth: "thin",
                scrollbarColor: "#d1d5db transparent",
            }}
        >
            {loading ? (
                <div className="h-full flex items-center justify-center">
                    <div className="animate-pulse flex flex-col items-center">
                        <div className="h-2.5 bg-gray-200 rounded-full w-48 mb-4"></div>
                        <div className="h-2 bg-gray-200 rounded-full w-32 mb-2.5"></div>
                        <div className="h-2 bg-gray-200 rounded-full w-40"></div>
                    </div>
                </div>
            ) : supportMessages.length === 0 ? (
                <div className="h-full flex items-center justify-center text-gray-500">
                    <p>
                        {translate(
                            "support.no_messages_start",
                            "No messages yet. Start the conversation!",
                        )}
                    </p>
                </div>
            ) : (
                <div className="space-y-6">
                    {supportMessages.map((message) => {
                        const isAdmin =
                            message.user_id !== supportRequest.user_id;

                        return (
                            <div
                                key={message.id}
                                className={`flex ${isAdmin ? "justify-start" : "justify-end"} mb-4`}
                            >
                                {isAdmin && (
                                    <div className="flex-shrink-0 mr-2">
                                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                            <User className="w-4 h-4 text-gray-600" />
                                        </div>
                                    </div>
                                )}
                                <div
                                    className={`max-w-[80%] rounded-lg p-4 shadow-sm ${
                                        isAdmin
                                            ? "bg-gray-100 text-gray-800 rounded-tl-none"
                                            : "bg-primary text-white rounded-tr-none"
                                    }`}
                                >
                                    <div className="mb-2 text-sm flex justify-between items-center">
                                        <span className="font-medium flex items-center">
                                            {isAdmin
                                                ? translate(
                                                      "support.support_team",
                                                      "Support Team",
                                                  )
                                                : translate(
                                                      "support.you",
                                                      "You",
                                                  )}
                                        </span>
                                        <span className="ml-2 opacity-75 flex items-center text-xs">
                                            <Clock className="w-3 h-3 mr-1" />
                                            {message.updated_at &&
                                                convertToLocalTimezone(
                                                    message.updated_at,
                                                )?.toFormattedDateTime()}
                                        </span>
                                    </div>
                                    <p className="whitespace-pre-wrap">
                                        {message.content || message.message}
                                    </p>
                                    {message.media &&
                                        message.media.length > 0 && (
                                            <div
                                                className={`mt-2 pt-2 border-t ${isAdmin ? "border-gray-200" : "border-white"}`}
                                            >
                                                <p className="text-xs font-medium mb-1">
                                                    {translate(
                                                        "support.attachments",
                                                        "Attachments",
                                                    )}
                                                    :
                                                </p>
                                                <div className="flex flex-wrap gap-2">
                                                    {message.media.map(
                                                        (file) => (
                                                            <a
                                                                key={file.id}
                                                                href={file.url}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                className={`text-xs ${isAdmin ? "text-blue-600" : "text-gray-100"}
                                            hover:underline flex items-center p-1 px-2 rounded-full ${
                                                isAdmin
                                                    ? "bg-blue-50"
                                                    : "bg-blue-600/20"
                                            }`}
                                                            >
                                                                <File className="w-3 h-3 mr-1" />
                                                                {file.name}
                                                            </a>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                </div>
                                {!isAdmin && (
                                    <div className="flex-shrink-0 ml-2">
                                        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                                            <User className="w-4 h-4 text-white" />
                                        </div>
                                    </div>
                                )}
                            </div>
                        );
                    })}
                    <div ref={messagesEndRef} />
                </div>
            )}
        </div>
    );
}

export default ChatWindow;
