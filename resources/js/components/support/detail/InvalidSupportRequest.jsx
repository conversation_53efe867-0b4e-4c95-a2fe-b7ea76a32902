import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { ChevronLeft, Search } from "lucide-react";
import { Link } from "@inertiajs/react";

function InvalidSupportRequest() {
    const { translate } = useSSRTranslations();

    return (
        <div className="flex items-center justify-center py-12">
            <div className="text-center py-16 px-6 bg-white shadow-xl rounded-2xl max-w-lg">
                <div className="flex justify-center mb-6">
                    <Search className="w-16 h-16 text-red-500 animate-pulse" />
                </div>
                <h1 className="text-2xl font-bold text-gray-800 mb-4">
                    {translate(
                        "support.request_not_found",
                        "Support request not found",
                    )}
                </h1>
                <p className="text-gray-600 mb-6">
                    {translate(
                        "support.request_not_found_desc",
                        "The support request you're looking for could not be found or may have been deleted.",
                    )}
                </p>
                <Link
                    href={route("my-account.support")}
                    className="inline-block bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200 flex items-center justify-center gap-2 mx-auto w-fit"
                >
                    <ChevronLeft className="w-4 h-4" />
                    {translate(
                        "support.back_to_list",
                        "Back to support requests",
                    )}
                </Link>
            </div>
        </div>
    );
}

export default InvalidSupportRequest;
