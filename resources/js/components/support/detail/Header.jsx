import React from "react";
import { Link } from "@inertiajs/react";
import { ArrowLeft } from "lucide-react";
import {
    getSupportStatusBadgeClass,
    getSupportPriorityBadgeClass,
} from "@/helpers/badgeClassUtils";
import useSSRTranslations from "@/hooks/useSSRTranslations";

function Header({ supportRequest }) {
    const { translate } = useSSRTranslations();

    return (
        <div className="flex flex-col md:flex-row justify-between items-center mb-6">
            <div>
                <Link
                    href={route("my-account.support")}
                    className="flex items-center text-primary hover:underline mb-5"
                >
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    {translate(
                        "support.back_to_list",
                        "Back to support requests",
                    )}
                </Link>

                <h1 className="text-2xl font-bold mb-5">
                    {translate("support.subject_label", "Subject")}:{" "}
                    {supportRequest.subject}
                </h1>
                <div className="flex flex-wrap gap-2 mt-2">
                    <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${getSupportStatusBadgeClass(
                            supportRequest.status?.value,
                        )}`}
                    >
                        {supportRequest.status?.label}
                    </span>
                    <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${getSupportPriorityBadgeClass(
                            supportRequest.priority?.value,
                        )}`}
                    >
                        {supportRequest.priority?.label}
                    </span>
                    <span className="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {supportRequest.request_type?.label}
                    </span>
                    <span className="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        ID: {supportRequest.sr_no}
                    </span>
                </div>
            </div>
        </div>
    );
}

export default Header;
