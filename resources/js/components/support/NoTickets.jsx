import React from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import { Plus, Mail } from "lucide-react";
import PropTypes from "prop-types";

function NoTickets({ openCreateModal }) {
    const { translate } = useSSRTranslations();

    return (
        <div className="bg-white rounded-xl shadow-sm p-8 text-center border border-gray-100">
            <div className="mb-6">
                <Mail className="h-16 w-16 text-blue-200 mx-auto" />
            </div>
            <h3 className="text-xl font-medium text-gray-800 mb-3">
                {translate(
                    "support.no_requests_found",
                    "No Support Requests Found",
                )}
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
                {translate(
                    "support.no_requests_description",
                    "You don't have any support requests yet. Create a new request to get started.",
                )}
            </p>
            <button
                onClick={openCreateModal}
                className="bg-blue-600 text-white px-5 py-2.5 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center mx-auto"
            >
                <Plus className="h-5 w-5 mr-2" />
                {translate(
                    "support.create_first_request",
                    "Create New Request",
                )}
            </button>
        </div>
    );
}

NoTickets.propTypes = {
    openCreateModal: PropTypes.func.isRequired,
};

export default NoTickets;
