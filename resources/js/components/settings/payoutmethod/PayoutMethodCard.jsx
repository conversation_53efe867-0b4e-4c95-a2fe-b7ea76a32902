import { useState, useEffect } from "react";
import { router, usePage } from "@inertiajs/react";
import toast from "react-hot-toast";
import { Trash2, Landmark, IdCard } from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import usePayoutMethod from "@/hooks/usePayoutMethod";

export default function PayoutMethodCard({
    payoutMethod,
    markDefaultClick,
    onDeleteClick,
}) {
    const { translate } = useSSRTranslations();

    return (
        <div
            className="card border shadow-sm bg-base-100"
            key={payoutMethod.id}
        >
            <div className="card-body p-4">
                <h2 className="card-title flext justify-between text-base">
                    {payoutMethod.payout_method_type.label}
                    {payoutMethod.is_default === 0 ? (
                        <span
                            className="cursor-pointer rounded-md bg-red-400/10 text-red-400 hover:bg-red-400/20 p-2 transition"
                            onClick={() => onDeleteClick(payoutMethod)}
                        >
                            <Trash2 className="w-4 h-4" />
                        </span>
                    ) : (
                        <span
                            className="tooltip tooltip-left rounded-md bg-gray-200 text-gray-500 hover:bg-gray-300 p-2 transition"
                            data-tip={translate(
                                "my_settings.delete_payout_method_tooltip_text",
                            )}
                        >
                            <Trash2 className="w-4 h-4" />
                        </span>
                    )}
                </h2>

                <p className="flex items-center text-sm text-gray-600">
                    <Landmark className="w-5 h-5 mr-2" />
                    {payoutMethod.bank_name}
                </p>
                <p className="flex items-center text-sm text-gray-600">
                    <IdCard className="w-5 h-5 mr-2" />
                    {payoutMethod.account_number}
                </p>
                {payoutMethod.is_default === 1 ? (
                    <div className="badge badge-success text-white text-xs mt-1">
                        {translate("my_settings.default_text", "Default")}
                    </div>
                ) : (
                    <div
                        className="cursor-pointer badge badge-success badge-outline text-white text-xs mt-1"
                        onClick={() => markDefaultClick(payoutMethod.id)}
                    >
                        {translate(
                            "my_settings.mark_as_default_text",
                            "Mark as Default",
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
