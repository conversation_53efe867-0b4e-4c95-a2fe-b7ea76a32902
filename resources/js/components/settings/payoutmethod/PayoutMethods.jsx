import React, { useState, useEffect } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import usePayoutMethod from "@/hooks/usePayoutMethod";
import PayoutMethodCard from "./PayoutMethodCard";
import AddPayoutMethodModal from "./AddPayoutMethodModal";
import DeletePayoutMethodModal from "./DeletePayoutMethodModal";
import toast from "react-hot-toast";

export default function PayoutMethods() {
    const { translate } = useSSRTranslations();

    const [showAddPayoutModal, setShowAddPayoutModal] = useState(false);

    const [selectedPayoutMethod, setSelectedPayoutMethod] = useState(null);

    const {
        userPayoutMethods,
        loading,
        getUserPayoutMethods,
        configurations,
        configurationsLoading,
        getConfigurationsData,
    } = usePayoutMethod();

    useEffect(() => {
        getConfigurationsData();
        getUserPayoutMethods();
    }, []);

    const handleMarkAsDefaultClick = async (id) => {
        try {
            const { data } = await axios.post(
                route("api.payout-methods.mark-default"),
                { id },
            );
            if (data.success) {
                getUserPayoutMethods();
                toast.success(data.message);
            }
        } catch (error) {
            toast.error(translate("common.something_wrong"));
        }
    };

    return (
        <>
            <div className="flex justify-between items-center border-b pb-3 mb-6">
                <h1 className="text-md sm:text-xl font-bold">
                    {translate("my_settings.payout_method_tab_title")}
                </h1>
                {configurations.canAddPayoutMethod && (
                    <div
                        className="btn btn-sm btn-primary"
                        onClick={() => setShowAddPayoutModal(true)}
                    >
                        {translate("my_settings.add_payout_method_btn")}
                    </div>
                )}
            </div>
            {loading ? (
                <p className="flex items-center justify-center h-64">
                    <span className="loading loading-bars loading-xl"></span>
                </p>
            ) : userPayoutMethods.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-48">
                    <h2 className="text-2xl font-semibold text-base-content">
                        {translate("my_settings.no_payout_method_found")}
                    </h2>
                </div>
            ) : (
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    {userPayoutMethods.map((payoutMethod) => (
                        <PayoutMethodCard
                            key={payoutMethod.id}
                            payoutMethod={payoutMethod}
                            markDefaultClick={handleMarkAsDefaultClick}
                            onDeleteClick={setSelectedPayoutMethod}
                        />
                    ))}
                </div>
            )}

            <AddPayoutMethodModal
                open={showAddPayoutModal}
                onClose={() => setShowAddPayoutModal(false)}
                configurations={configurations}
            />

            <DeletePayoutMethodModal
                open={!!selectedPayoutMethod}
                onClose={() => setSelectedPayoutMethod(null)}
                payoutMethod={selectedPayoutMethod}
            />
        </>
    );
}
