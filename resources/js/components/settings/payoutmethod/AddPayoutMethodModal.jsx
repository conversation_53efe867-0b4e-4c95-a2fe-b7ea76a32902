import { useEffect, useMemo } from "react";
import toast from "react-hot-toast";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import usePayoutMethod from "@/hooks/usePayoutMethod";
import useValidation from "@/hooks/useValidation";
import bankFieldsByCountry from "@/helpers/bankFieldsByCountry.json";
import { HandCoins } from "lucide-react";
import TextInput from "@/components/forms/TextInput";
import SelectInput from "@/components/forms/SelectInput";
import prepareOptionsFromEnum from "@/helpers/prepareOptionsFromEnum";

const buildValidationSchema = (formData, dynamicFields = []) => {
    const schema = {
        payout_method_type: ["required"],
        country_code: ["required"],
    };

    if (formData.payout_method_type === "bank") {
        schema.bank_name = ["required"];
        schema.account_type = ["required"];

        dynamicFields.forEach((field) => {
            const name = field.name;
            schema[name] = [name];

            if (field.required) {
                schema[name].push("required");
            }
        });
    }
    return schema;
};

export default function AddPayoutMethodModal({
    open,
    onClose,
    configurations,
}) {
    const { translate } = useSSRTranslations();
    const {
        configurationsLoading,
        getUserPayoutMethods,
        formData,
        updateFormData,
        clearFormData,
    } = usePayoutMethod();

    const fields = bankFieldsByCountry[formData.country_code] || [];

    const dynamicValidationSchema = useMemo(
        () => buildValidationSchema(formData, fields),
        [formData, fields],
    );

    const { errors, validate, validateField } = useValidation(
        dynamicValidationSchema,
    );

    const payoutMethodTypesOptions = prepareOptionsFromEnum(
        configurations.payout_method_types,
    );

    const countryOptions = prepareOptionsFromEnum(configurations.countries);

    const accountTypeOptions = prepareOptionsFromEnum(
        configurations.bank_account_types,
    );

    const handleCloseClick = () => {
        clearFormData();
        onClose();
    };

    const handleSaveClick = async (e) => {
        e.preventDefault();
        const isValid = validate(formData);
        if (!isValid) return;

        try {
            const { data } = await axios.post(
                route("api.payout-methods.store"),
                formData,
            );
            if (data.success) {
                toast.success(data.message);
                onClose();
                clearFormData();
                getUserPayoutMethods();
            }
        } catch (error) {
            if (error.response?.status === 422 && error.response.data?.errors) {
                toast.error(Object.values(error.response.data?.errors)[0]?.[0]);
            } else {
                toast.error(translate("common.something_wrong"));
            }
        }
    };

    return (
        <div className={`modal ${open ? "modal-open" : ""}`} role="dialog">
            {!configurationsLoading && (
                <div className="modal-box max-w-2xl">
                    <button
                        className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                        onClick={handleCloseClick}
                    >
                        ✕
                    </button>

                    <div className="flex justify-center mb-5">
                        <div className="bg-success/20 text-success rounded-full p-2">
                            <HandCoins className="w-6 h-6" />
                        </div>
                    </div>
                    <h3 className="text-lg font-bold text-center mb-3">
                        {translate("my_settings.add_payout_method_popup_title")}
                    </h3>
                    <div className="text-gray-500 text-sm mt-2 mb-2">
                        <strong>
                            {translate("my_settings.note_text", "Note:")}
                        </strong>{" "}
                        {translate("my_settings.add_payout_method_note")}
                    </div>
                    <form className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
                            <SelectInput
                                options={payoutMethodTypesOptions}
                                label={translate(
                                    "my_settings.payout_form.labels.payout_method",
                                )}
                                value={
                                    formData.payout_method_type
                                        ? payoutMethodTypesOptions.find(
                                              (opt) =>
                                                  opt.value ===
                                                  formData.payout_method_type,
                                          )
                                        : null
                                }
                                onChange={(option) =>
                                    updateFormData(
                                        "payout_method_type",
                                        option?.value || "",
                                    )
                                }
                                datarequired={true}
                                placeholder={translate(
                                    "my_settings.payout_form.placeholder.payout_method",
                                )}
                                error={errors?.payout_method_type}
                                menuPortalTarget={
                                    typeof document !== "undefined"
                                        ? document.body
                                        : null
                                }
                            />
                            <SelectInput
                                options={countryOptions}
                                label={translate(
                                    "my_settings.payout_form.labels.country",
                                )}
                                value={
                                    formData.country_code
                                        ? countryOptions.find(
                                              (opt) =>
                                                  opt.value ===
                                                  formData.country_code,
                                          )
                                        : null
                                }
                                onChange={(option) =>
                                    updateFormData(
                                        "country_code",
                                        option?.value || "",
                                    )
                                }
                                datarequired={true}
                                placeholder={translate(
                                    "my_settings.payout_form.placeholder.country",
                                )}
                                error={errors?.country_code}
                                menuPortalTarget={
                                    typeof document !== "undefined"
                                        ? document.body
                                        : null
                                }
                            />
                            {formData.country_code !== "" &&
                                formData.payout_method_type === "bank" && (
                                    <>
                                        <TextInput
                                            id="bank_name"
                                            value={formData.bank_name}
                                            label={translate(
                                                "my_settings.payout_form.labels.bank_name",
                                            )}
                                            placeholder={translate(
                                                "my_settings.payout_form.placeholder.bank_name",
                                            )}
                                            datarequired="true"
                                            onChange={(e) => {
                                                updateFormData(
                                                    "bank_name",
                                                    e.target.value,
                                                );
                                                validateField(
                                                    "bank_name",
                                                    e.target.value,
                                                    formData,
                                                );
                                            }}
                                            onBlur={(e) =>
                                                validateField(
                                                    "bank_name",
                                                    e.target.value,
                                                    formData,
                                                )
                                            }
                                            error={errors?.bank_name}
                                        />
                                        <TextInput
                                            id="branch_name"
                                            value={formData.branch_name}
                                            label={translate(
                                                "my_settings.payout_form.labels.branch_name",
                                            )}
                                            placeholder={translate(
                                                "my_settings.payout_form.placeholder.branch_name",
                                            )}
                                            onChange={(e) =>
                                                updateFormData(
                                                    "branch_name",
                                                    e.target.value,
                                                )
                                            }
                                        />
                                        <SelectInput
                                            options={accountTypeOptions}
                                            label={translate(
                                                "my_settings.payout_form.labels.account_type",
                                            )}
                                            value={accountTypeOptions.find(
                                                (opt) =>
                                                    opt.value ===
                                                    formData.account_type,
                                            )}
                                            onChange={(option) => {
                                                updateFormData(
                                                    "account_type",
                                                    option?.value || "",
                                                );
                                                validateField(
                                                    "account_type",
                                                    option?.value || "",
                                                    formData,
                                                );
                                            }}
                                            datarequired={true}
                                            placeholder={translate(
                                                "my_settings.payout_form.placeholder.account_type",
                                            )}
                                            onBlur={(e) =>
                                                validateField(
                                                    "account_type",
                                                    formData.account_type,
                                                    formData,
                                                )
                                            }
                                            error={errors?.account_type}
                                            menuPortalTarget={
                                                typeof document !== "undefined"
                                                    ? document.body
                                                    : null
                                            }
                                        />

                                        {fields.map((field) => (
                                            <TextInput
                                                key={field.name}
                                                id={field.name}
                                                value={
                                                    formData[field.name] || ""
                                                }
                                                label={translate(
                                                    "my_settings.payout_form.labels." +
                                                        field.name,
                                                )}
                                                placeholder={translate(
                                                    "my_settings.payout_form.placeholder." +
                                                        field.name,
                                                )}
                                                onChange={(e) => {
                                                    updateFormData(
                                                        field.name,
                                                        e.target.value,
                                                    );
                                                    validateField(
                                                        field.name,
                                                        e.target.value,
                                                        formData,
                                                    );
                                                }}
                                                datarequired={
                                                    field.required
                                                        ? "true"
                                                        : undefined
                                                }
                                                onBlur={() =>
                                                    validateField(
                                                        field.name,
                                                        formData[field.name],
                                                        formData,
                                                    )
                                                }
                                                error={errors?.[field.name]}
                                            />
                                        ))}
                                    </>
                                )}
                        </div>
                        <div className="modal-action gap-2">
                            <button
                                className="btn btn-primary btn-sm"
                                onClick={handleSaveClick}
                            >
                                {translate("common.save_btn")}
                            </button>
                        </div>
                    </form>
                </div>
            )}
        </div>
    );
}
