import { useState, useEffect } from "react";
import { router, usePage } from "@inertiajs/react";
import toast from "react-hot-toast";
import { Trash2 } from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function DeletePayoutMethodModal({
    open,
    onClose,
    payoutMethod,
}) {
    const { translate } = useSSRTranslations();

    const isVisible = open && payoutMethod;
    const [disabled, setDisabled] = useState(false);

    const handleDeleteClick = async () => {
        setDisabled(true);
        try {
            const { data } = await axios.delete(
                route("api.payout-methods.delete", { id: payoutMethod.id }),
            );
            if (data.success) {
                setDisabled(false);
                onClose();
                toast.success(data.message);
                window.location.reload();
            }
        } catch (error) {
            setDisabled(false);
            onClose();
            if (error.response?.status === 400) {
                toast.error(error.response.data?.message);
            } else {
                toast.error(translate("common.something_wrong"));
            }
        }
    };

    return (
        <>
            <div className={`modal ${isVisible ? "modal-open" : ""}`}>
                <div className="modal-box max-w-sm">
                    {payoutMethod && (
                        <>
                            <button
                                className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
                                onClick={onClose}
                            >
                                ✕
                            </button>
                            <div className="flex justify-center mb-2">
                                <div className="bg-error/20 text-error rounded-full p-2">
                                    <Trash2 className="w-6 h-6" />
                                </div>
                            </div>
                            <h3 className="text-lg font-bold text-center border-b-2 pb-3">
                                {translate(
                                    "my_settings.delete_payout_method_text",
                                )}
                            </h3>
                            <p className="py-4 text-center">
                                {translate(
                                    "my_settings.delete_payout_method_confirm_text",
                                )}
                            </p>
                            <div className="modal-action">
                                <button
                                    className="btn btn-sm btn-error text-white"
                                    onClick={handleDeleteClick}
                                    disabled={disabled}
                                >
                                    {translate("common.delete_btn")}
                                </button>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </>
    );
}
