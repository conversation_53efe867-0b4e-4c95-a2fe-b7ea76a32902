import DangerButton from "@/components/buttons/DangerButton";
import PasswordInput from "@/components/forms/PasswordInput";
import Modal from "@/components/utils/Modal";
import SecondaryButton from "@/components/buttons/SecondaryButton";
import { useForm } from "@inertiajs/react";
import { useRef, useState } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import toast from "react-hot-toast";

export default function DeleteUserForm() {
    const { translate } = useSSRTranslations();

    const [confirmingUserDeletion, setConfirmingUserDeletion] = useState(false);
    const passwordInput = useRef();

    const {
        data,
        setData,
        delete: destroy,
        processing,
        reset,
        errors,
        clearErrors,
    } = useForm({
        password: "",
    });

    const confirmUserDeletion = () => {
        setConfirmingUserDeletion(true);
    };

    const deleteUser = (e) => {
        e.preventDefault();

        destroy(route("profile.destroy"), {
            preserveScroll: true,
            onSuccess: () => {
                closeModal();
                toast.success(translate("common.deleted_text"));
            },
            onError: () => {
                passwordInput.current.focus();
                toast.error(errors.password);
            },
            onFinish: () => reset(),
        });
    };

    const closeModal = () => {
        setConfirmingUserDeletion(false);

        clearErrors();
        reset();
    };

    return (
        <section className="space-y-6">
            <header>
                <h2 className="text-md sm:text-xl font-bold border-b pb-3 mb-6">
                    {translate("profile.delete_account")}
                </h2>

                <p className="text-sm text-gray-600">
                    {translate("profile.delete_account_description")}
                </p>
            </header>

            <DangerButton onClick={confirmUserDeletion}>
                {translate("profile.delete_account")}
            </DangerButton>

            <Modal show={confirmingUserDeletion} onClose={closeModal}>
                <form onSubmit={deleteUser} className="p-6">
                    <h2 className="text-lg font-medium text-gray-900">
                        {translate("profile.delete_confirm_message")}
                    </h2>

                    <p className="mt-1 text-sm text-gray-600">
                        {translate("profile.delete_confirm_description")}
                    </p>
                    <div className="mt-6">
                        <PasswordInput
                            id="password"
                            ref={passwordInput}
                            value={data.password}
                            onChange={(e) =>
                                setData("password", e.target.value)
                            }
                            isFocused
                            placeholder={translate(
                                "common.placeholder.password",
                            )}
                            autoComplete="current-password"
                            error={errors.password}
                        />
                    </div>

                    <div className="mt-6 flex justify-end">
                        <SecondaryButton onClick={closeModal}>
                            {translate("common.cancel_btn")}
                        </SecondaryButton>

                        <DangerButton className="ms-3" disabled={processing}>
                            {translate("profile.delete_account")}
                        </DangerButton>
                    </div>
                </form>
            </Modal>
        </section>
    );
}
