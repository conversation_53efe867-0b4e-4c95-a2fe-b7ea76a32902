import PrimaryButton from "@/components/buttons/PrimaryButton";
import PasswordInput from "@/components/forms/PasswordInput";
import { Transition } from "@headlessui/react";
import { useForm } from "@inertiajs/react";
import { useRef } from "react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import toast from "react-hot-toast";

export default function UpdatePasswordForm() {
    const { translate } = useSSRTranslations();

    const passwordInput = useRef();
    const currentPasswordInput = useRef();

    const {
        data,
        setData,
        errors,
        put,
        reset,
        processing,
        recentlySuccessful,
    } = useForm({
        current_password: "",
        password: "",
        password_confirmation: "",
    });

    const updatePassword = (e) => {
        e.preventDefault();

        put(route("password.update"), {
            preserveScroll: true,
            onSuccess: () => {
                reset();
                toast.success(translate("common.saved_text"));
            },
            onError: (errors) => {
                if (errors.password) {
                    reset("password", "password_confirmation");
                    passwordInput.current.focus();
                    toast.error(errors.password);
                }

                if (errors.current_password) {
                    reset("current_password");
                    currentPasswordInput.current.focus();
                    toast.error(errors.current_password);
                }
            },
        });
    };

    return (
        <section>
            <header>
                <h2 className="text-md sm:text-xl font-bold border-b pb-3 mb-6">
                    {translate("profile.update_password")}
                </h2>
            </header>

            <form onSubmit={updatePassword} className="mt-6 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 sm:grid-cols-2 gap-4">
                    <PasswordInput
                        id="current_password"
                        name="current_password" // Add name attribute
                        ref={currentPasswordInput}
                        value={data.current_password}
                        onChange={(e) =>
                            setData("current_password", e.target.value)
                        }
                        placeholder={translate(
                            "common.placeholder.current_password",
                        )}
                        autoComplete="current-password"
                        error={errors.current_password}
                    />

                    <PasswordInput
                        id="password"
                        name="password" // Add name attribute
                        ref={passwordInput}
                        value={data.password}
                        onChange={(e) => setData("password", e.target.value)}
                        placeholder={translate(
                            "common.placeholder.new_password",
                        )}
                        autoComplete="new-password"
                        error={errors.password}
                    />

                    <PasswordInput
                        id="password_confirmation"
                        name="password_confirmation" // Add name attribute
                        value={data.password_confirmation}
                        onChange={(e) =>
                            setData("password_confirmation", e.target.value)
                        }
                        placeholder={translate(
                            "common.placeholder.password_confirmation",
                        )}
                        autoComplete="new-password"
                        error={errors.password_confirmation}
                    />
                </div>

                <div className="flex items-center justify-end gap-4">
                    <PrimaryButton disabled={processing} className="btn-sm">
                        {translate("common.save_btn")}
                    </PrimaryButton>

                    <Transition
                        show={recentlySuccessful}
                        enter="transition ease-in-out"
                        enterFrom="opacity-0"
                        leave="transition ease-in-out"
                        leaveTo="opacity-0"
                    >
                        <p className="text-sm text-gray-600">
                            {translate("common.saved_text")}
                        </p>
                    </Transition>
                </div>
            </form>
        </section>
    );
}
