import { Link, usePage } from "@inertiajs/react";
import { User } from "lucide-react";

import NavLink from "@/components/navbar/NavLink";
import LanguageDropdown from "@/components/navbar/LanguageDropdown";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function Navbar() {
    const { auth } = usePage().props;
    const { translate } = useSSRTranslations();

    return (
        <div className="navbar bg-gray-800 text-neutral-content shadow-md">
            <div className="navbar-start">
                <div className="dropdown">
                    <div
                        tabIndex={0}
                        role="button"
                        className="btn btn-ghost lg:hidden"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M4 6h16M4 12h8m-8 6h16"
                            />
                        </svg>
                    </div>
                    <ul
                        tabIndex={0}
                        className="menu menu-sm dropdown-content mt-3 z-[20] p-2 shadow bg-base-100 rounded-box w-52"
                    >
                        <li>
                            <NavLink
                                href="/"
                                isExact={true}
                                className="text-gray-700"
                            >
                                {translate("common.menu.home")}
                            </NavLink>
                        </li>
                        <li>
                            <NavLink
                                href={route("events")}
                                className="text-gray-700"
                            >
                                {translate("common.menu.events")}
                            </NavLink>
                        </li>
                        <li>
                            <NavLink
                                href={route("stadiums")}
                                className="text-gray-700"
                            >
                                {translate("common.menu.stadiums")}
                            </NavLink>
                        </li>
                        <li>
                            <NavLink
                                href={route("clubs")}
                                className="text-gray-700"
                            >
                                {translate("common.menu.clubs")}
                            </NavLink>
                        </li>
                        <li>
                            <NavLink
                                href={route("leagues")}
                                className="text-gray-700"
                            >
                                {translate("common.menu.leagues")}
                            </NavLink>
                        </li>
                        <li>
                            <NavLink href="/about" className="text-gray-700">
                                {translate("common.menu.about")}
                            </NavLink>
                        </li>
                    </ul>
                </div>
                <Link href={route("home")}>
                    <img
                        src="/img/ticketgol-logo.png"
                        className="sm:ml-2 h-5 sm:h-6 lg:h-8"
                    />
                </Link>
            </div>
            <div className="navbar-center hidden lg:flex">
                <ul className="menu menu-horizontal px-1">
                    <li>
                        <NavLink href={route("home")} isExact={true}>
                            {translate("common.menu.home")}
                        </NavLink>
                    </li>
                    <li>
                        <NavLink href={route("events")}>
                            {translate("common.menu.events")}
                        </NavLink>
                    </li>
                    <li>
                        <NavLink href={route("stadiums")}>
                            {translate("common.menu.stadiums")}
                        </NavLink>
                    </li>
                    <li>
                        <NavLink href={route("clubs")}>
                            {translate("common.menu.clubs")}
                        </NavLink>
                    </li>
                    <li>
                        <NavLink href={route("leagues")}>
                            {translate("common.menu.leagues")}
                        </NavLink>
                    </li>
                    <li>
                        <NavLink href="/about">
                            {translate("common.menu.about")}
                        </NavLink>
                    </li>
                </ul>
            </div>
            <div className="navbar-end">
                <div className="lg:mr-4 mr-2">
                    <LanguageDropdown translate={translate} />
                </div>
                {auth?.user ? (
                    <div className="dropdown dropdown-end">
                        <div
                            tabIndex="0"
                            role="button"
                            className="btn btn-sm bg-amber-400 border-amber-400 text-white hover:bg-amber-500 hover:border-amber-500 m-1"
                        >
                            <User className="w-5 h-5 inline-block" />
                            <span className="hidden sm:flex">
                                {auth.user.name}
                            </span>
                        </div>
                        <ul
                            tabIndex="0"
                            className="dropdown-content menu bg-base-100 rounded-box z-20 w-52 p-2 shadow-sm"
                        >
                            <li>
                                <Link
                                    href={route("dashboard")}
                                    className="text-gray-700 px-3"
                                >
                                    {translate("common.menu.my_account")}
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={route("logout")}
                                    method="post"
                                    as="button"
                                    className="text-gray-700 px-3"
                                >
                                    {translate("common.menu.logout")}
                                </Link>
                            </li>
                        </ul>
                    </div>
                ) : (
                    <Link
                        href={route("login")}
                        className="btn btn-sm bg-amber-400 border-amber-400 text-white hover:bg-amber-500 hover:border-amber-500"
                    >
                        <User className="w-4 h-4 inline-block" />
                        <span>{translate("common.login_btn")}</span>
                    </Link>
                )}
            </div>
        </div>
    );
}
