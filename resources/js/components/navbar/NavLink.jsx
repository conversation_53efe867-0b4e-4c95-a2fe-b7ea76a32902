import { Link, usePage } from "@inertiajs/react";

export default function NavLink({
    isExact = false,
    className = "",
    children,
    ...props
}) {
    const { url } = usePage();
    const currentPath = url;
    const isActive = isExact
        ? currentPath === "/"
        : props.href.includes(currentPath) && currentPath !== "/";
    return (
        <Link
            {...props}
            className={
                "text-base focus:text-neutral-content hover:underline hover:underline-offset-8 " +
                (isActive
                    ? " underline underline-offset-8 font-extrabold "
                    : "") +
                className
            }
        >
            {children}
        </Link>
    );
}
