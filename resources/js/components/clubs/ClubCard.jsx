export default function ClubCard({ club }) {
    return (
        <div className="card w-full bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
            <figure className="relative h-48 bg-gray-200">
                {club.image !== "" ? (
                    <img
                        src={club.image}
                        alt={club.image_alt || club.name}
                        className="w-full h-full object-cover"
                    />
                ) : (
                    <img src="/img/ticketgol-logo.png" alt={club.name} />
                )}
            </figure>
            <div className="card-body">
                <div
                    className="tooltip tooltip-neutral w-fit text-left"
                    data-tip={club.name}
                    tabIndex={0}
                >
                    <h2 className="card-title line-clamp-1 max-w-xs">
                        {club.name}
                    </h2>
                </div>
            </div>
        </div>
    );
}
