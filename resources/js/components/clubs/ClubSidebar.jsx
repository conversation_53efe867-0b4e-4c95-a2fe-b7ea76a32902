import { useEffect, useRef } from "react";

import { XCircle } from "lucide-react";
import { useDebounce } from "@uidotdev/usehooks";
import FilterSelectCollapse from "@/components/forms/FilterSelectCollapse";
import useClubs from "@/hooks/useClubs";
import useSSRTranslations from "@/hooks/useSSRTranslations";

export default function ClubSidebar() {
    const { translate } = useSSRTranslations();

    const { filters, clearFilters, updateFilter, filterOptions, refreshClubs } =
        useClubs();
    const debouncedFilters = useDebounce(filters, 800);
    const isFirstRender = useRef(true);

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }

        refreshClubs();
    }, [debouncedFilters]);

    return (
        <aside className="md:w-1/4 w-full bg-base-100 px-6 rounded-box shadow-md max-h-screen overflow-y-auto md:sticky md:top-3">
            <h2 className="font-semibold mt-8 mb-4">
                {translate("clubs.filters_title", "Filters")}
            </h2>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <input
                    type="text"
                    placeholder={translate(
                        "clubs.search_placeholder",
                        "Search clubs...",
                    )}
                    className="input input-bordered w-full"
                    value={filters?.search || ""}
                    onChange={(e) => updateFilter("search", e.target.value)}
                />

                <button
                    onClick={clearFilters}
                    className="text-gray-500 hover:text-gray-800 transition-colors"
                    title={translate("clubs.reset_filters", "Reset Filters")}
                >
                    <XCircle size={24} />
                </button>
            </div>

            <div className="divider m-0"></div>
            <FilterSelectCollapse
                key="countries"
                title={translate("clubs.countries_title", "Countries")}
                placeholder={translate(
                    "clubs.countries_placeholder",
                    "Select countries",
                )}
                options={filterOptions["countries"]}
                selectedOption={filters["countries"]}
                onChange={(value) => {
                    updateFilter("countries", value);
                }}
                isMulti
            />

            <FilterSelectCollapse
                key="stadiums"
                title={translate("clubs.stadiums_title", "Stadiums")}
                placeholder={translate(
                    "clubs.stadiums_placeholder",
                    "Select stadiums",
                )}
                options={filterOptions["stadiums"]}
                selectedOption={filters["stadiums"]}
                onChange={(value) => {
                    updateFilter("stadiums", value);
                }}
                isMulti
            />
        </aside>
    );
}
