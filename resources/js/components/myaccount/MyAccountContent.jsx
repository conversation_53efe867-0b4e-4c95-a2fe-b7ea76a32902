import React from "react";
import { router } from "@inertiajs/react";

export default function MyAccountContent({ activeMenu, children }) {
    // If no specific content is provided, redirect to the appropriate page based on activeMenu
    if (!children) {
        switch (activeMenu) {
            case "profile":
                router.visit(route("profile.edit"));
                return null;
            case "orders":
                router.visit(route("my-account.orders"));
                return null;
            // Add other cases as needed
            default:
                return (
                    <div className="bg-white p-6 rounded-lg shadow-md">
                        <p className="text-gray-500">
                            Select an option from the sidebar
                        </p>
                    </div>
                );
        }
    }

    // If content is provided, render it
    return (
        <div className="md:w-3/4 w-full">
            <div className="bg-white p-4 sm:p-5 lg:p-6 rounded-lg shadow-md">
                {children}
            </div>
        </div>
    );
}
