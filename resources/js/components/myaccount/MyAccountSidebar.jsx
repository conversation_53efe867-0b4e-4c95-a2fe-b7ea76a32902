import { Link, usePage } from "@inertiajs/react";
import {
    User,
    Gauge,
    ShoppingBag,
    Tickets,
    MessageSquare,
    Banknote,
    ShoppingBasket,
    Settings,
} from "lucide-react";
import useSSRTranslations from "@/hooks/useSSRTranslations";
import React from "react";

export default function MyAccountSidebar({ activeMenu }) {
    const { auth } = usePage().props;
    const { translate } = useSSRTranslations();

    const accountMenuItems = [
        {
            icon: <Gauge className="w-5 h-5" />,
            title: "Dashboard",
            route: route("dashboard"),
            key: "dashboard",
        },
        {
            icon: <User className="w-5 h-5" />,
            title: "My Profile",
            route: route("profile.edit"),
            key: "profile",
        },
        {
            icon: <Tickets className="w-5 h-5" />,
            title: "My Tickets",
            route: route("my-account.tickets"),
            key: "tickets",
        },
        {
            icon: <ShoppingBag className="w-5 h-5" />,
            title: "My Orders",
            route: route("my-account.orders"),
            key: "orders",
        },
        {
            icon: <ShoppingBasket className="w-5 h-5" />,
            title: "My Sales",
            route: route("my-account.sales"),
            key: "sales",
        },
        {
            icon: <Banknote className="w-5 h-5" />,
            title: "My Withdrawals",
            route: route("my-account.withdrawals"),
            key: "withdrawals",
        },
        {
            icon: <MessageSquare className="w-5 h-5" />,
            title: "My Support Requests",
            route: route("my-account.support"),
            key: "support",
        },
        {
            icon: <Settings className="w-5 h-5" />,
            title: "Settings",
            route: route("my-account.settings"),
            key: "settings",
        },
    ];

    return (
        <aside className="overflow-y-auto px-6 w-full max-h-screen bg-white rounded-lg shadow-md md:w-1/4 md:sticky md:top-3">
            <div className="py-6">
                <h2 className="mb-6 text-xl font-semibold">
                    {translate("common.menu.my_account")}
                </h2>

                <div className="flex flex-col space-y-2">
                    {accountMenuItems.map((item) => (
                        <Link
                            key={item.key}
                            href={item.route}
                            className={`flex items-center p-3 rounded-lg transition-colors ${activeMenu === item.key ? "bg-primary text-white" : "hover:bg-gray-50"}`}
                        >
                            <div
                                className={`mr-3 ${activeMenu === item.key ? "text-white" : "text-primary"}`}
                            >
                                {item.icon}
                            </div>
                            <span className="font-medium">{item.title}</span>
                        </Link>
                    ))}
                </div>
            </div>
        </aside>
    );
}
