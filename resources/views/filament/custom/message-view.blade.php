<div class="space-y-4">
    <div class="flex items-center space-x-4 gap-4">
        @if ($user->avatar)
            <img src="{{ $user->avatar }}" class="w-10 h-10 rounded-full" alt="{{ $user->name }}">
        @else
            <div class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-white">
                {{ collect(explode(' ', $user->name))->map(fn($segment) => mb_substr($segment, 0, 1))->join('') }}
            </div>
        @endif
        <div>
            <div class="font-medium">{{ $user->name }}</div>
            <div class="text-sm text-gray-500">
                {{ $created_at->format('M d, Y H:i:s') }}
            </div>
        </div>
    </div>

    <div class="prose max-w-none dark:prose-invert">
        <div class="whitespace-pre-wrap">
            {{ $message }}
        </div>
    </div>

    @if ($media->count() > 0)
        <h3 class="mt-6">{{ __('support.attachments_label') }}</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mt-3">
            @foreach ($media as $mediaItem)
                <div class="relative group">
                    @if (Str::startsWith($mediaItem->mime_type, 'image/'))
                        <img src="{{ $mediaItem->getUrl() }}" alt="Attachment"
                            class="rounded-lg w-full h-32 object-cover" />
                        <a href="{{ $mediaItem->getUrl() }}" target="_blank"
                            class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <span class="text-white text-sm">{{ __('support.view_full_size') }}</span>
                        </a>
                    @else
                        <a href="{{ $mediaItem->getUrl() }}" target="_blank"
                            class="px-3 py-2 m-4 bg-blue-500 rounded-lg border border-blue-400">
                            <span class="text-sm">{{ $mediaItem->file_name }}</span>
                        </a>
                    @endif
                </div>
            @endforeach
        </div>
    @endif
</div>
