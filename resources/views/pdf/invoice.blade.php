<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ __('order.invoice_title') }} #: TGINV{{ str_pad($order->id, 3, '0', STR_PAD_LEFT) }}</title>
    <style>
        body { font-family: sans-serif; font-size: 14px; margin: 0; padding: 0; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 5px; }
        th, td { padding: 5px; text-align: left; vertical-align: top; }
        .total-row td { font-weight: bold; }
        h2 { margin: 0; padding: 0; } 
        p { margin: 5px 0; padding: 0; }
    </style>
</head>
<body>

    @php 
        $refundTransaction = $order->transactions->firstWhere('transaction_type', 'refund');

        $refundAmount = $refundTransaction ? $refundTransaction->total_amount : 0;
    @endphp
    
    <!-- Header -->
    <table style="border-bottom: 1px solid #ddd; ">
        <tr>
            <td style="padding-bottom: 20px; text-align: center;">
                <h2>{{ __('order.invoice_title') }}</h2>
            </td>
        </tr>
    </table>

    <table style="border-bottom: 1px solid #ddd;">
        <tr>
            <td>
                <strong>{{ __('order.invoice_title') }} #:</strong> TGINV{{ str_pad($order->id, 3, '0', STR_PAD_LEFT) }}
            </td>
        </tr>
        <tr>
            <td>
                <strong>{{ __('order.order_number') }} #:</strong> {{ $order->order_no }}
            </td>
        </tr>
        <tr>
        	<td style="padding-bottom: 20px;">
                <strong>{{ __('order.labels.date') }}:</strong> {{ $order->purchase_date }}
            </td>
        </tr>
    </table>

    <!-- Bill To / From -->
    <table style="border-bottom: 1px solid #ddd;">
        <tr>
        	<td width="50%" style="padding-bottom: 20px;">
                <p><strong>{{ __('order.labels.bill_to') }}:</strong></p>
                <p>{{ $order->buyer->name }}</p>
                <p>{{ $order->buyer->email }}</p>
                <p>{{ $order->buyer->userDetail->phone }}</p>
                <p>{{ $order->buyer->userDetail->address }}, {{ $order->buyer->userDetail->city }} {{ $order->buyer->userDetail->country ? ', '.$order->buyer->userDetail->country->translation->name : '' }} - {{ $order->buyer->userDetail->zip }}</p>
            </td>
            <td width="50%" style="padding-bottom: 20px;">
                <p><strong>{{ __('order.labels.bill_from') }}:</strong></p>
                <p>{{ config('app.name') }}</p>
                <p>{{ $generalSettings['sales_email'] }}</p>
                <p>{{ $generalSettings['contact_phone'] }}</p>
                <p>{{ $generalSettings['contact_address'] }}</p>
            </td>
        </tr>
    </table>

    <table style="margin-top: 50px;">
        <thead>
            <tr>
                <th width="55%" style="border-bottom: 1px solid #ddd;">{{ __('order.labels.description') }}</th>
                <th width="15%" style="border-bottom: 1px solid #ddd;">{{ __('order.labels.qty') }}</th>
                <th width="15%" style="border-bottom: 1px solid #ddd;">{{ __('order.labels.unit_price') }}</th>
                <th width="15%" style="text-align: right;border-bottom: 1px solid #ddd;">{{ __('order.total') }}</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td style="border-bottom: 1px solid #ddd;">{{ __('order.labels.event') }} : {{ $order->order_meta_data->event->name->{$language} }}</td>
                <td style="border-bottom: 1px solid #ddd;">{{ $order->quantity }}</td>
                <td style="border-bottom: 1px solid #ddd;">€{{ $order->price }}</td>
                <td style="text-align: right;border-bottom: 1px solid #ddd;">€{{ $order->total_price }}</td>
            </tr>
            <tr>
                <td style="border-bottom: 1px solid #ddd;">{{ __('order.labels.service_charge') }}</td>
                <td style="border-bottom: 1px solid #ddd;">1</td>
                <td style="border-bottom: 1px solid #ddd;">€{{ $order->service_charge_amount }}</td>
                <td style="text-align: right;border-bottom: 1px solid #ddd;">€{{ $order->service_charge_amount }}</td>
            </tr>
            <tr>
                <td style="border-bottom: 1px solid #ddd;">{{ __('order.labels.tax') }}</td>
                <td style="border-bottom: 1px solid #ddd;">1</td>
                <td style="border-bottom: 1px solid #ddd;">€{{ $order->tax_amount }}</td>
                <td style="text-align: right;border-bottom: 1px solid #ddd;">€{{ $order->tax_amount }}</td>
            </tr>
            @if($refundAmount > 0)
            <tr>
                <td colspan="3" style="border-bottom: 1px solid #ddd;">{{ __('order.labels.refund') }}</td>
                <td style="text-align: right;border-bottom: 1px solid #ddd;">-€{{ $refundAmount }}</td>
            </tr>
            @endif
            <tr class="total-row">
                <td colspan="3" style="border-bottom: 1px solid #ddd;">{{ __('order.total') }}</td>
                <td style="text-align: right;border-bottom: 1px solid #ddd;">€{{ number_format($order->grand_total - $refundAmount, 2) }}</td>
            </tr>
        </tbody>
    </table>

</body>
</html>
