name: Deploy <PERSON>vel Application to Production Server

on:
  push:
    branches:
      - main

jobs:
  deploy:
    if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3

      - name: Install Composer Dependencies
        run: composer install --optimize-autoloader --no-dev --no-progress --no-interaction --prefer-dist

      - name: Install Node JS
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Installing Dependencies
        run: npm install && npm run build

      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh/
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key

      - name: Run Remote/Artisan Commands
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/ticketgol
            git pull origin main
            git reset --hard origin/main
            composer install --optimize-autoloader --no-dev --no-progress --no-interaction --prefer-dist
            npm install && npm run build
            php artisan migrate --force
            php artisan optimize
