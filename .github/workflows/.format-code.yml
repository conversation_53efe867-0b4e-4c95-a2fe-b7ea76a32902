name: Format Code

on:
  pull_request:

jobs:
  format:
    runs-on: ubuntu-latest
    permissions:
      contents: write  # Required to commit changes back to the repository

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags
          ref: ${{ github.head_ref }} # Checkout the PR branch

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'

      - name: Install Composer Dependencies
        run: composer install --optimize-autoloader --no-dev --no-progress --no-interaction --prefer-dist

      - name: Install Node JS
        uses: actions/setup-node@v3
        with:
          node-version: '22'

      - name: Install NPM Dependencies
        run: npm install --quiet

      - name: <PERSON> (PHP Linting)
        run: vendor/bin/pint

      - name: Run Prettier (JS/CSS/JSX Formatting)
        run: npx prettier --write "resources/**/*.{js,css,jsx}"

      - name: Commit Formatted Changes
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: "style: Auto-format code with <PERSON><PERSON> and <PERSON><PERSON><PERSON> [skip ci]"
          commit_user_name: "github-actions[bot]"
          commit_user_email: "41898282+github-actions[bot]@users.noreply.github.com"
          commit_author: "GitHub Actions Bot <<EMAIL>>"
          file_pattern: "."
          skip_fetch: true

