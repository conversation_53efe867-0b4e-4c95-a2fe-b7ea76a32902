{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "amidesfahani/filament-tinyeditor": "^2.1", "awcodes/filament-badgeable-column": "^2.3", "barryvdh/laravel-dompdf": "^3.1", "bezhansalleh/filament-language-switch": "^3.1", "bezhansalleh/filament-shield": "^3.3", "dotswan/filament-laravel-pulse": "^1.1", "fakerphp/faker": "^1.24", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "inertiajs/inertia-laravel": "^2.0", "laravel/framework": "^11.31", "laravel/pint": "^1.21", "laravel/pulse": "^1.4", "laravel/sanctum": "^4.0", "laravel/telescope": "^5.2", "laravel/tinker": "^2.9", "opcodesio/log-viewer": "^3.15", "predis/predis": "2.0", "pxlrbt/filament-environment-indicator": "^2.1", "resend/resend-laravel": "^0.16.1", "sentry/sentry-laravel": "^4.13", "shuvroroy/filament-spatie-laravel-backup": "^2.2", "shuvroroy/filament-spatie-laravel-health": "^2.3", "spatie/laravel-activitylog": "^4.9", "spatie/laravel-backup": "^9.3", "spatie/laravel-health": "^1.31", "stripe/stripe-php": "^17.1", "tapp/filament-timezone-field": "^3.0", "tightenco/ziggy": "^2.0", "z3d0x/filament-logger": "^0.7.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "barryvdh/laravel-ide-helper": "^3.2", "beyondcode/laravel-query-detector": "^2.0", "laravel/breeze": "^2.2", "laravel/pail": "^1.1", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.5", "pestphp/pest-plugin-laravel": "^3.0", "spatie/laravel-web-tinker": "^1.9"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": ["barryvdh/laravel-ide-helper"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}