{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build --ssr", "dev": "vite", "dev:ssr": "npm run build:ssr && php artisan inertia:start-ssr", "lint": "eslint ./resources/js --fix", "format": "npx prettier --write \"resources/**/*.{js,css,jsx}\""}, "devDependencies": {"@eslint/js": "^9.29.0", "@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "concurrently": "^9.0.1", "daisyui": "^4.12.23", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "vite": "^6.2.5"}, "dependencies": {"@reduxjs/toolkit": "^2.6.1", "@sentry/react": "^9.19.0", "@sentry/vite-plugin": "^3.4.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@uidotdev/usehooks": "^2.4.1", "dayjs": "^1.11.13", "eslint-plugin-unused-imports": "^4.1.4", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "prettier": "^3.5.1", "react-hot-toast": "^2.5.2", "react-infinite-scroll-hook": "^5.0.2", "react-range": "^1.10.0", "react-redux": "^9.2.0", "react-select": "^5.10.1", "redux-persist": "^6.0.0", "sweetalert2": "^11.6.13", "sweetalert2-react-content": "^5.1.0"}}