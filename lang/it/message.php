
<?php

/*
|--------------------------------------------------------------------------
| Flash messages: For success, error, info etc
|--------------------------------------------------------------------------
 */
return [
    'SUCCESS' => 'Success',
    'EVENTS_NOT_FOUND' => 'No Events Found',
    'STADIUMS_NOT_FOUND' => 'No Stadiums Found',
    'CLUBS_NOT_FOUND' => 'No Clubs Found',
    'LEAGUES_NOT_FOUND' => 'No Leagues Found',
    'TICKETS_NOT_FOUND' => 'No Tickets Found',
    'ORDERS_NOT_FOUND' => 'No Orders Found',
    'ERROR' => 'Error! Something went wrong',
    'NO_RECORD_FOUND' => 'No record found',
    'SOMETHING_WENT_WRONG' => 'Something went wrong, Contact the support team',
    'UNAUTHORIZED' => 'You do not have the required authorization.',
    'EVENT_NOT_EXISTS' => 'Event not exists',
    'CLUB_NOT_EXISTS' => 'Club not exists',
    'LEAGUE_NOT_EXISTS' => 'League not exists',
    'STADIUM_NOT_EXISTS' => 'Stadium not exists',
    'TRANSACTION_IN_PROGRESS' => 'You already have a transaction in progress. Please complete it before proceeding.',
    'OWN_TICKET_PURCHASE_RESTRICT' => 'You cannot purchase tickets from your own listing.',
    'TICKET_PURCHASE_QUANTITY_EXCEED' => 'The requested quantity exceeds the available quantity. Only :remainingQuantity tickets available.',
    'TICKET_CREATED' => 'Ticket created successfully',
    'TICKET_UPDATED' => 'Ticket updated successfully',
    'TICKETS_CREATION_LIMIT_EXCEED' => 'Your ticket creation limit has been exceeded! <NAME_EMAIL>',
    'TICKETS_CREATION_LIMIT_EXCEED_WITH_REMAIN' => 'Ticket limit exceeded! You can only add :remaining more tickets.',
    'TICKETS_QUANTITY_UPDATE_ERROR' => 'There are currently :reserved tickets reserved. You cannot reduce the quantity below this.',
    'TICKETS_UPDATE_USER_ERROR' => 'You cannot edit tickets created by someone else.',
    'TICKET_DELETED' => 'Ticket deleted successfully',
    'TICKET_ORDERS_EXISTS' => 'This Ticket cannot be deleted while it has some orders or active reservations',
    'TICKET_NOT_FOUND' => 'Ticket you are trying to delete not exists',
    'TICKET_RESERVATION_CREATED' => 'Ticket reservation created',
    'TICKET_RESERVATION_CHECK_DONE' => 'Ticket reservation check completed',
    'TICKET_RESERVATION_CREATE_FAILED' => 'Unable to reserve your tickets. Please try again.',
    'TICKET_RESERVATION_GET_ERROR' => 'Failed to get ticket reservation',
    'TICKET_RESERVATION_CANCELLED' => 'Ticket reservation cancelled',
    'ORDER_CREATED' => 'Order created successfully',
    'ORDER_CREATE_FAILED' => 'Failed to create order',
    'ORDER_NOT_FOUND' => 'Order not found',
    'ORDER_STATUS_VERIFICATION_FAILD' => 'Failed to verify order status',
    'ORDER_STATUS_UPDATE_PERMISSION_RESTRICT' => 'You cannot edit order status that belongs to someone else.',
    'ORDER_STATUS_CANCEL_PERMISSION_RESTRICT' => 'You cannot cancel order that is reassigned to you',
    'ORDER_STATUS_CHANGE_NOT_ALLOWED' => 'You cannot change the order status from :currentStatus to :newStatus. Please choose a valid status.',
    'ORDER_STATUS_UPDATE_FAILD' => 'Failed to update order status',
    'ORDER_TICKETS_UPLOAD_FAILD' => 'Failed to upload order tickets',
    'PAYOUT_METHOD_CREATED' => 'Payout method created successfully',
    'PAYOUT_METHOD_CREATE_FAILED' => 'Failed to create payout method',
    'PAYOUT_METHOD_MARKED_AS_DEFAULT' => 'Payout method marked as default successfully',
    'PAYOUT_METHOD_DELETED' => 'Payout method deleted successfully',
    'PAYOUT_METHOD_NOT_FOUND' => 'Payout method not found',
    'WITHDRAWAL_REQUESTS_NOT_FOUND' => 'Withdrawal requests not found',
    'WITHDRAW_REQUEST_CREATED' => 'Withdrawal request created successfully',
    'WITHDRAWAL_REQUEST_DELETED' => 'Withdrawal requests deleted successfully',
    'WITHDRAWAL_REQUEST_NOT_FOUND' => 'Withdrawal request not exists',
    'WITHDRAWAL_REQUEST_PENDING' => 'One of your withdrawal requests is still being processed. You can request another once it is paid.',
    'WITHDRAWAL_INSUFFICIENT_AMOUNT' => 'You do not have enough funds to make a withdrawal.',
    'WALLET_TRANSACTIONS_NOT_FOUND' => 'Wallet transactions not found',
    'WALLET_TRANSACTION_NOT_FOUND' => 'Wallet transaction not found',
];
