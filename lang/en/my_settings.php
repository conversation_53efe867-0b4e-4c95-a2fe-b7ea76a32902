<?php

return [
    'head_title' => 'Settings',
    'page_title' => 'Settings',
    'payout_method_tab_title' => 'Payout Methods',
    'update_password_tab_title' => 'Update Password',
    'delete_account_tab_title' => 'Delete Account',
    'add_payout_method_btn' => 'Add Payout Method',
    'add_payout_method_popup_title' => 'Add Payout Method',
    'no_payout_method_found' => 'No payout method has been added yet.',
    'payout_form' => [
        'labels' => [
            'payout_method' => 'Payout Method',
            'country' => 'Country',
            'bank_name' => 'Bank Name',
            'branch_name' => 'Branch Name',
            'account_type' => 'Account Type',
            'account_holder_name' => 'Account Holder Name',
            'account_number' => 'Account Number',
            'iban' => 'IBAN',
            'swift_code' => 'Swift Code',
            'ifsc_code' => 'IFSC Code',
            'routing_number' => 'Routing Number',
            'sort_code' => 'Sort Code',
            'bsb' => 'BSB Code',
            'bank_code' => 'Bank Code',
            'branch_code' => 'Branch Code',
            'institution_number' => 'Institution Number',
            'transit_number' => 'Transit Number',
        ],
        'placeholder' => [
            'payout_method' => 'Select Payout Method',
            'country' => 'Select Country',
            'bank_name' => 'Enter Bank Name',
            'branch_name' => 'Enter Branch Name',
            'account_type' => 'Select Account Type',
            'account_holder_name' => 'Enter Account Holder Name',
            'account_number' => 'Enter Account Number',
            'iban' => 'Enter IBAN',
            'swift_code' => 'Enter Swift Code',
            'ifsc_code' => 'Enter IFSC Code',
            'routing_number' => 'Enter Routing Number',
            'sort_code' => 'Enter Sort Code',
            'bsb' => 'Enter BSB Code',
            'bank_code' => 'Enter Enter Bank Code',
            'branch_code' => 'Enter Branch Code',
            'institution_number' => 'Enter Institution Number',
            'transit_number' => 'Enter Transit Number',
        ],
    ],
    'default_text' => 'Default',
    'mark_as_default_text' => 'Mark as Default',
    'note_text' => 'Note:',
    'add_payout_method_note' => 'Make sure your payout details are correct and accurate. We don\'t have a way to validate payout details and incorrect data can delay your payout.',
    'delete_payout_method_text' => 'Delete Payout Method',
    'delete_payout_method_confirm_text' => 'Are you sure you want to delete this payout method?',
    'delete_payout_method_tooltip_text' => 'You can\'t delete default payout method',
];
