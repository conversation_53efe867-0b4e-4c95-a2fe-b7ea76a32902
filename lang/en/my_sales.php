<?php

return [
    'head_title' => 'My Sales',
    'page_title' => 'My Sales',
    'detail_head_title' => 'My Sales Order',
    'detail_page_title' => 'Sales Order',
    'back_btn' => 'Back to My Sales',
    'labels' => [
        'order' => 'Order',
        'ticket_no' => 'Ticket No.',
        'event' => 'Event',
        'venue' => 'Venue',
        'tickets' => 'Tickets',
        'total_tickets' => 'Total Tickets',
        'total_price' => 'Total Price',
        'buyer_name' => 'Buyer Name',
        'event_date' => 'Event Date',
        'purchase_date' => 'Purchse Date',
        'order_status' => 'Order Status',
        'payment_status' => 'Payment Status',
        'reason' => 'Reason',
        'reason_placeholer' => 'Enter Reason',
        'status_change_reason' => 'Status Change Reason',
        'upload_tickets' => 'Upload Tickets',
        'additional_doc' => 'Additional Document (optional)',
        'additional_document' => 'Additional Document',
        'your_earning' => 'Your Earning',
        'your_total_earning' => 'Your Total Earning',
        'order_cancel_penalty' => 'Order Cancel Penalty',
    ],
    'no_sales' => 'Sorry, no sales orders match your filters.',
    'no_sales_details' => 'Please update your filter criteria and try again.',
    'sales_order_not_found' => 'Sales order not found, or you do not have permission to view this sales order.',
    'search_placeholder' => 'Search Orders...',
    'status_placeholder' => 'Select Status',
    'date_from_placeholder' => 'Select From Date',
    'date_to_placeholder' => 'Select To Date',
    'sort_by_placeholder' => 'Sort By',
    'update_status_btn' => 'Update Order Status',
    'update_status_text' => 'Update Order Status',
    'view_tickets_btn' => 'View Tickets',
    'upload_btn' => 'Upload',
    'order_summary_title' => 'Order Summary',
    'reupload_tickets_btn' => 'Re-Upload Tickets',
    'update_status_confirm_text' => 'Are you sure you want to mark this order as',
    'mark_as_text' => 'Mark as',
    'reason_validation_msg' => 'Please enter a Reason',
    'tickets_validation_msg' => 'Please upload tickets',
    'reupload_title_text' => 'Re-Upload Tickets & Additional Document',
    'note_text' => 'Note:',
    'cancellation_note' => 'Canceling this order will deduct a processing fee from your payout and in case replacement tickets are found it will also cover any difference in the cost.',
    'under_review_note' => 'Marking this order as Under Review will create a support ticket with our team. We may reach out for more details to help resolve it.',
    'on_dispute_note' => 'Marking this order as On Dispute will create a support ticket with our team. We may reach out for more details to help resolve it.',
    'shipped_note' => 'To mark this order as Shipped, please upload the ticket files and any additional documents required for delivery. Once submitted, your order status will be updated to Shipped and the buyer will be notified. Make sure the uploaded tickets are complete and correct to avoid disputes.',
    'reupload_note_text' => 'Make sure the uploaded tickets are complete and accurate to avoid disputes. Once the buyer downloads the tickets, you will no longer be able to re-upload or replace them.',
    'sales_sort_options' => [
        ['value' => 'created_at_asc', 'label' => 'Date: Oldest First'],
        ['value' => 'created_at_desc', 'label' => 'Date: Newest First'],
        ['value' => 'total_price_asc', 'label' => 'Price: Low to High'],
        ['value' => 'total_price_desc', 'label' => 'Price: High to Low'],
        ['value' => 'quantity_asc', 'label' => 'Tickets: Low to High'],
        ['value' => 'quantity_desc', 'label' => 'Tickets: High to Low'],
        ['value' => 'ticket_id_asc', 'label' => 'Ticket No.: Oldest First'],
        ['value' => 'ticket_id_desc', 'label' => 'Ticket No.: Newest First'],
    ],
];
