<?php

return [
    'head_title' => 'My Withdrawals',
    'page_title' => 'My Withdrawals',
    'withdarwal_tab_title' => 'Withdrawal Requests',
    'wallet_transactions_tab_title' => 'Wallet Transactions',
    'withdrawal_add_btn' => 'Add Withdrawal Request',
    'balance_text' => 'Your Balance',
    'labels' => [
        'all_transaction_types' => 'All Transaction Types',
        'all_entry_types' => 'All Entry Types',
        'all_status' => 'All Status',
        'transaction' => 'Transaction',
        'order' => 'Order',
        'transaction_type' => 'Type',
        'total' => 'Total',
        'status' => 'Status',
        'withdrawn' => 'Withdrawn',
        'remaining' => 'Remaining',
        'withdrawal' => 'Withdrawal',
        'status' => 'Status',
        'amount' => 'Amount',
        'bank_name' => 'Bank Name',
        'account_number' => 'Account Number',
        'payment_reference' => 'Payment Reference',
        'transaction_note' => 'Transaction Note',
        'note' => 'Note',
        'approved_at' => 'Approved At',
        'paid_at' => 'Paid At',
        'created_at' => 'Created At',
        'payout_method' => 'Payout Method',
    ],
    'placeholder' => [
        'withdrawal_search' => 'Search Withdrawal Requests...',
        'amount' => 'Enter Amount',
        'search' => 'Search Wallet Transactions...',
        'from_date' => 'Select From Date',
        'to_date' => 'Select To Date',
        'transaction_type' => 'Select Transaction Type',
        'entry_type' => 'Select Entry Type',
        'status' => 'Select Status',
        'sort_by' => 'Sort By',
        'payout_method' => 'Select Payout Method',
    ],
    'transaction_detail_page_title' => 'Wallet Transaction Detail',
    'no_withdrawals' => 'Sorry, no withdrawal requests match your filters.',
    'no_transactions' => 'Sorry, no wallet transactions match your filters.',
    'no_transactions_details' => 'Please update your filter criteria and try again.',
    'transaction_not_found' => 'Wallet transaction not found, or you do not have permission to view this wallet transaction.',
    'add_withdrawal_note' => 'All withdrawal requests are reviewed before processing. You will be notified once your request is approved. Please make sure your bank account details are correct. We are not responsible for failed transfers due to incorrect information.',
    'back_to_transactions_btn' => 'Back to Transactions',
    'transaction_summary_title' => 'Transaction Summary',
    'order_summary_title' => 'Order Summary',
    'view_sales_order_btn' => 'View Sales Order',
    'delete_withdrawal_text' => 'Delete Withdrawal Request!',
    'delete_withdrawal_confirm_text' => 'Are you sure you want to delete this withdrawal request?',
    'email_approved_message' => 'We have successfully processed your request. The funds should reach your account shortly based on your selected payout method.',
    'email_rejected_message' => 'Unfortunately, your request has been rejected. Please check note for more details. You can resubmit your request from your account.',
    'email_paid_message' => 'The funds have been transferred to your selected payout method. If you have any questions or need further assistance, feel free to reach out to our support team.',
    'wallet_sort_options' => [
        ['value' => 'created_at_asc', 'label' => 'Date: Oldest First'],
        ['value' => 'created_at_desc', 'label' => 'Date: Newest First'],
        ['value' => 'total_amount_asc', 'label' => 'Total: Low to High'],
        ['value' => 'total_amount_desc', 'label' => 'Total: High to Low'],
    ],
    'withdrawal_sort_options' => [
        ['value' => 'created_at_asc', 'label' => 'Date: Oldest First'],
        ['value' => 'created_at_desc', 'label' => 'Date: Newest First'],
        ['value' => 'amount_asc', 'label' => 'Amount: Low to High'],
        ['value' => 'amount_desc', 'label' => 'Amount: High to Low'],
    ],
];
