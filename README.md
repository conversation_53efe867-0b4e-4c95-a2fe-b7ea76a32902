<p align="center">
    <a href="https://laravel.com" target="_blank">
        <img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo">
    </a>
</p>

# TicketGol Project

## Table of Contents
- [Prerequisites](#prerequisites)
- [Local Development Setup](#local-development-setup)
- [Environment Configuration](#environment-configuration)
- [Database Setup](#database-setup)
- [Git Hooks](#git-hooks)
- [Running the Application](#running-the-application)
- [Development Workflow](#development-workflow)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have the following installed:

- [<PERSON><PERSON> Herd](https://herd.laravel.com/)
- [Composer](https://getcomposer.org/)
- [Node.js](https://nodejs.org/) (LTS version recommended)
- [Git](https://git-scm.com/)

## Local Development Setup

### 1. <PERSON>lone the Repository

```bash
# Clone the project
git clone https://github.com/your-organization/ticketgol.git

# Navigate to the project directory
cd ticketgol
```

### 2. Configure Hosts File

Add the following entries to your hosts file:
- macOS/Linux: `/etc/hosts`
- Windows: `C:\Windows\System32\drivers\etc\hosts`

```
127.0.0.1   ticketgol.test
127.0.0.1   admin.ticketgol.test
```

### 3. Laravel Herd Configuration

1. Open Laravel Herd
2. Add the project directory
3. Ensure the domain is set to `ticketgol.test`

## Environment Configuration

### 1. Create Environment File

```bash
# Copy environment example file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 2. Configure .env File

Update the following in your `.env` file:
- Database credentials
- App URL
- Other environment-specific settings

## Database Setup

### 1. Create Database

Using Laravel Herd or your preferred database management tool:
- Create a new MySQL database named `ticketgol`

### 2. Run Migrations and Seeders

```bash
# Run database migrations
php artisan migrate

# Generate Permissions and Policies
php artisan shield:generate --all --ignore-existing-policies

# Seed the database (optional)
php artisan db:seed

# Assign Super Admin Role to User
php artisan shield:super-admin
```

## Git Hooks

### 1. Install Dependencies

```bash
# Install Composer dependencies
composer install

# Install Node.js dependencies
npm install
```

### 2. Configure Git Hooks

```bash
# Set git hooks path
git config core.hooksPath .githooks

# Make pre-commit hook executable
chmod +x .githooks/pre-commit
```

### 3. Example Pre-commit Hook (`.githooks/pre-commit`)

```bash
#!/bin/sh

# Run code style checks
php ./vendor/bin/phpcs
npm run lint

# Run tests
php artisan test
npm run test
```

## Running the Application

### 1. Start Development Servers

```bash
# Start Laravel development server
php artisan serve

# Compile and watch frontend assets
npm run dev
```

### 2. Access URLs

- Main Site: [http://ticketgol.test](http://ticketgol.test)
- Admin Panel: [http://admin.ticketgol.test](http://admin.ticketgol.test)

## Development Workflow

### Frontend Development

```bash
# Install frontend dependencies
npm install

# Compile assets for development
npm run dev

# Compile assets for production
npm run build
```

### Backend Development

```bash
# Generate new migration
php artisan make:migration create_your_table

# Create new model
php artisan make:model YourModel

# Run tests
php artisan test
```

## Troubleshooting

### Common Issues

- **Composer Dependencies**: 
  ```bash
  composer install
  composer update
  ```

- **Clear Configuration Cache**:
  ```bash
  php artisan config:clear
  php artisan cache:clear
  ```

- **Regenerate Composer Autoload**:
  ```bash
  composer dump-autoload
  ```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Code Style

- Follow PSR-12 PHP coding standards
- Use ESLint for JavaScript
- Run `npm run lint` before committing

## License

[Specify your project's license]

## Contact

[Provide contact information or support channels]

Last reviewed 12-12-24
