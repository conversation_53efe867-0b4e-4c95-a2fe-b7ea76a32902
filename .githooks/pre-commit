#!/bin/sh

# Get staged PHP files
php_files=$(git diff --cached --name-only --diff-filter=ACMR -- '*.php')
if [ -n "$php_files" ]; then
    ./vendor/bin/pint $php_files -q
    echo "The following PHP files are formatted by Pint:"
    echo "$php_files"
    echo ""
fi

# Get staged JS/CSS/JSX files
js_files=$(git diff --cached --name-only --diff-filter=ACMR -- '*.js' '*.css' '*.jsx')
if [ -n "$js_files" ]; then
    npx prettier --write $js_files
    echo "The following JS, CSS, JSX files are formatted by Prettier:"
    echo "$js_files"
    echo ""
fi

# Add back the formatted files
if [ -n "$php_files" ]; then
    git add $php_files
fi

if [ -n "$js_files" ]; then
    git add $js_files
fi

echo "Formatted files have been added back to staging"
