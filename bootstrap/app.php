<?php

use App\Http\Middleware\Api\SetLocaleFromHeader;
use App\Http\Middleware\DetectSSRMiddleware;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\InjectLocaleDataMiddleware;
use App\Http\Middleware\VerifySlugMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->statefulApi();

        $middleware->web(append: [
            InjectLocaleDataMiddleware::class,
            DetectSSRMiddleware::class,
            HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->encryptCookies(except: [
            'selected_locale',
        ]);

        $middleware->alias([
            'detect-ssr' => DetectSSRMiddleware::class,
            'set-locale' => SetLocaleFromHeader::class,
            'verify-slug' => VerifySlugMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        Integration::handles($exceptions);
    })
    ->create();
