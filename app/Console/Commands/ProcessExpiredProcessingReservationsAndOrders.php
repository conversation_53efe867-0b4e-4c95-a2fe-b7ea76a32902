<?php

namespace App\Console\Commands;

use App\Enums\OrderStatus;
use App\Jobs\ReleaseTixStockTicketJob;
use App\Services\OrderService;
use App\Services\TicketReservationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessExpiredProcessingReservationsAndOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ticketgol:process-processing-reservations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will change the status of expired processing reservations to "Expired" and also for orders and order transactions it will set status to "Expired"';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $ticketReservationService = app(TicketReservationService::class);
            $orderService = app(OrderService::class);

            $expiredReservations = $ticketReservationService->handleProcessingReservations();

            Log::channel('stripe')->info('Cron job executed', ['time' => now()]);

            // Print these records
            foreach ($expiredReservations as $reservation) {
                if ($reservation->order && $reservation->order->status === OrderStatus::PENDING) {
                    $orderService->expireOrderAndTransaction($reservation->order);
                }

                if ($reservation->tixstock_hold_id) {
                    ReleaseTixStockTicketJob::dispatch($reservation->id);
                }

                $this->info('Expired reservation: '.json_encode($reservation));
            }

            $this->info('Processing reservations and orders expired successfully!');

            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();

            $logData['message'] = $e->getMessage();
            $logData['line'] = $e->getLine();
            $logData['file'] = $e->getFile();

            Log::channel('stripe')->info('Processing reservation and orders not marked as expired due to error', $logData);

            return Command::FAILURE;
        }
    }
}
