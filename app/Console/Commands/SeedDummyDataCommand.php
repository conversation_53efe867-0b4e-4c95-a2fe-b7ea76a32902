<?php

namespace App\Console\Commands;

use App\Models\Club;
use App\Models\CmsPage;
use App\Models\EmailTemplate;
use App\Models\Event;
use App\Models\League;
use App\Models\Order;
use App\Models\Restriction;
use App\Models\Season;
use App\Models\Stadium;
use App\Models\SupportRequest;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SeedDummyDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ticketgol:seed-dummy-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will seed the dummy data for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (! $this->confirm('⚠️  This will seed dummy data. Do you wish to continue?', true)) {
            $this->info('Operation cancelled.');

            return Command::SUCCESS;
        }

        $shouldRefresh = $this->confirm('🔄 Would you like to reset the database and start fresh? (This will delete all existing data)', false);

        $this->info('Starting seeding process...');

        if ($shouldRefresh) {
            $this->warn('🗑️  Refreshing database...');
            try {
                $this->call('migrate:fresh');
                $this->info('✅ Database refreshed successfully!');

                $this->warn('🌱 Running base seeders...');
                $this->call('db:seed');
                $this->info('✅ Base data seeded successfully!');
            } catch (\Exception $e) {
                $this->error('❌ Failed to refresh database: '.$e->getMessage());

                return Command::FAILURE;
            }
        }

        $this->warn('🚀 Seeding dummy data...');

        DB::beginTransaction();
        try {
            $this->seedDummyData();
            DB::commit();
            $this->newLine();
            $this->info('✅ All dummy data has been seeded successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Failed to seed data: '.$e->getMessage());

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    private function seedDummyData()
    {
        $steps = [
            ['Users', fn () => User::factory()->count(10)->create()],

            ['Seasons', fn () => Season::factory()->count(10)->create()],

            ['Leagues', fn () => League::factory()->count(10)->create()],

            ['Stadiums', fn () => Stadium::factory()->count(10)->create()],

            ['Clubs', fn () => Club::factory()->count(10)->create()],

            ['Restrictions', fn () => Restriction::factory()->count(10)->create()],

            ['Events', fn () => Event::factory()->count(10)->create()],

            ['Tickets', fn () => Ticket::factory()->count(10)->create()],

            ['Orders', fn () => Order::factory()->count(10)->create()],

            ['CMS Pages', fn () => CmsPage::factory()->count(10)->create()],

            ['Email Templates', fn () => EmailTemplate::factory()->count(10)->create()],

            ['Support Requests', fn () => SupportRequest::factory()->count(10)->create()],
        ];

        $bar = $this->output->createProgressBar(count($steps));
        $bar->start();

        foreach ($steps as [$name, $action]) {
            $this->newLine();
            $this->info("📦 Seeding {$name}...");
            $action();
            $bar->advance();
            $this->info("✓ {$name} created successfully");
        }

        $bar->finish();
    }
}
