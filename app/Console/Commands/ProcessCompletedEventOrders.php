<?php

namespace App\Console\Commands;

use App\Services\OrderStatusService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessCompletedEventOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ticketgol:process-completed-event-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will change the status of shipped order to completed after 1 week of event completion';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $orderStatusService = app(OrderStatusService::class);

            $orders = $orderStatusService->completePastShippedOrders();

            Log::info('Orders marked as COMPLETED by cron.', $orders->pluck('order_no')->toArray());

            $this->info('Marked event orders to completed successfully!');
            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $logData['message'] = $e->getMessage();
            $logData['line'] = $e->getLine();
            $logData['file'] = $e->getFile();

            Log::info('Events orders not marked as completed due to error', $logData);

            return Command::FAILURE;
        }
    }
}
