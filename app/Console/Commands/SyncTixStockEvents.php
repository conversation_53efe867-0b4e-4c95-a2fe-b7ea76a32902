<?php

namespace App\Console\Commands;

use App\Services\TixStock\SyncEventsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncTixStockEvents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ticketgol:sync-tixstock-events';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will sync the tixstock events to our events DB';

    protected $syncEventsService;

    public function __construct(SyncEventsService $syncEventsService)
    {
        parent::__construct();
        $this->syncEventsService = $syncEventsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {

            $events = $this->syncEventsService->handle();

            $this->info('TixStock Events synced successfully!');
            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $logData['message'] = $e->getMessage();
            $logData['line'] = $e->getLine();
            $logData['file'] = $e->getFile();

            Log::info('Failed to sync TixStock events data due to error', $logData);
            $this->error('Failed to sync TixStock events data due to error');

            return Command::FAILURE;
        }
    }
}
