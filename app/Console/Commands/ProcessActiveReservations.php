<?php

namespace App\Console\Commands;

use App\Jobs\ReleaseTixStockTicketJob;
use App\Services\TicketReservationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessActiveReservations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ticketgol:process-active-reservations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will change the status of expired active reservations to "Expired"';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {
            $ticketReservationService = app(TicketReservationService::class);

            $expiredReservations = $ticketReservationService->handleActiveReservations();
            Log::channel('stripe')->info('Cron job executed', ['time' => now()]);

            foreach ($expiredReservations as $record) {
                $this->info('Expired reservation: '.json_encode($record));

                if ($record->tixstock_hold_id) {
                    ReleaseTixStockTicketJob::dispatch($record->id);
                }
            }

            $this->info('Marked reservations to Expired successfully!');
            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $logData['message'] = $e->getMessage();
            $logData['line'] = $e->getLine();
            $logData['file'] = $e->getFile();

            Log::channel('stripe')->info('Active reservation not marked as expired due to error', $logData);

            return Command::FAILURE;
        }
    }
}
