<?php

namespace App\Console\Commands;

use App\Services\TixStock\SyncTicketsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateTixStockTicketStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ticketgol:update-tixstock-ticket-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will upddate the tixstock ticket status to our tickets DB';

    protected $syncTicketsService;

    public function __construct(SyncTicketsService $syncTicketsService)
    {
        parent::__construct();
        $this->syncTicketsService = $syncTicketsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {

            $tickets = $this->syncTicketsService->updateTicketStatus();

            $this->info('TixStock tickets status updated successfully!');
            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $logData['message'] = $e->getMessage();
            $logData['line'] = $e->getLine();
            $logData['file'] = $e->getFile();

            Log::info('Failed to update TixStock tickets status due to error', $logData);

            $this->error('Failed to update TixStock tickets status due to error');

            return Command::FAILURE;
        }
    }
}
