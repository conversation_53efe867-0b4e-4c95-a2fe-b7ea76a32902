<?php

namespace App\Console\Commands;

use App\Services\TixStock\SyncTicketsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncTixStockTickets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ticketgol:sync-tixstock-tickets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will sync the tixstock tickets to our tickets DB';

    protected $syncTicketsService;

    public function __construct(SyncTicketsService $syncTicketsService)
    {
        parent::__construct();
        $this->syncTicketsService = $syncTicketsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();
        try {

            $tickets = $this->syncTicketsService->handle();

            $this->info('TixStock tickets synced successfully!');
            DB::commit();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $logData['message'] = $e->getMessage();
            $logData['line'] = $e->getLine();
            $logData['file'] = $e->getFile();

            Log::info('Failed to sync TixStock tickets data due to error', $logData);

            $this->error('Failed to sync TixStock tickets data due to error');

            return Command::FAILURE;
        }
    }
}
