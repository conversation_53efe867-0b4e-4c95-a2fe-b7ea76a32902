<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CmsPageTranslation extends Model
{
    /** @use HasFactory<\Database\Factories\CmsPageFactory> */
    use CustomActivityLog, HasFactory;

    public $timestamps = false;

    protected $fillable = ['cms_page_id', 'locale', 'title', 'content', 'meta_title', 'meta_description'];
}
