<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StadiumSector extends Model
{
    /** @use HasFactory<\Database\Factories\StadiumSectorFactory> */
    use CustomActivityLog, HasFactory, SoftDeletes;

    public function translations()
    {
        return $this->hasMany(StadiumSectorTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(StadiumSectorTranslation::class, 'stadium_sector_id', 'id')
            ->where('locale', app()->getLocale());
    }

    public function parent()
    {
        return $this->belongsTo(StadiumSector::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(StadiumSector::class, 'parent_id');
    }

    public function stadium()
    {
        return $this->belongsTo(Stadium::class, 'stadium_id', 'id');
    }

    public function events()
    {
        return $this->belongsToMany(StadiumSector::class, 'event_stadium_sectors');
    }
}
