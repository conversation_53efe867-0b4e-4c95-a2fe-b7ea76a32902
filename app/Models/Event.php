<?php

namespace App\Models;

use App\Enums\EventCategoryType;
use App\Traits\CustomActivityLog;
use App\Traits\HasSlugs;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Event extends Model implements HasMedia
{
    /** @use HasFactory<\Database\Factories\EventFactory> */
    use CustomActivityLog, HasFactory, HasSlugs, InteractsWithMedia, SoftDeletes;

    public const FIRST_IMAGE = 1;

    public const DEFAUL_IMAGE_URL = 'https://images.unsplash.com/photo-1489944440615-453fc2b6a9a9?auto=format&fit=crop&q=80';

    protected $casts = [
        'category' => EventCategoryType::class,
        'date' => 'date',
        'time' => 'datetime',
    ];

    protected $guard = [];

    public function translations()
    {
        return $this->hasMany(EventTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(EventTranslation::class, 'event_id', 'id')
            ->where('locale', app()->getLocale());
    }

    public function homeClub()
    {
        return $this->belongsTo(Club::class, 'home_club_id', 'id');
    }

    public function guestClub()
    {
        return $this->belongsTo(Club::class, 'guest_club_id', 'id');
    }

    public function stadium()
    {
        return $this->belongsTo(Stadium::class, 'stadium_id', 'id');
    }

    public function league()
    {
        return $this->belongsTo(League::class, 'league_id', 'id');
    }

    public function stadiumSectors()
    {
        return $this->belongsToMany(StadiumSector::class, 'event_stadium_sectors', 'event_id', 'stadium_sector_id')->withTimestamps();
    }

    public function restrictions()
    {
        return $this->belongsToMany(Restriction::class, 'event_restrictions', 'event_id', 'restriction_id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function tickets()
    {
        return $this->hasMany(Ticket::class, 'event_id', 'id');
    }
}
