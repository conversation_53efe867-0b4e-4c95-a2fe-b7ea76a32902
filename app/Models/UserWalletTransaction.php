<?php

namespace App\Models;

use App\Enums\WalletEntryType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserWalletTransaction extends Model
{
    use CustomActivityLog, SoftDeletes;

    protected $casts = [
        'transaction_type' => WalletTransactionType::class,
        'entry_type' => WalletEntryType::class,
        'status' => WalletTransactionStatus::class,
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($transaction) {
            $transactionNo = 'TGWT'.str_pad($transaction->id, 3, '0', STR_PAD_LEFT);

            $transaction->transaction_no = $transactionNo;
            $transaction->save();
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public function withdrawal()
    {
        return $this->belongsTo(UserWithdrawal::class, 'withdrawal_id', 'id');
    }
}
