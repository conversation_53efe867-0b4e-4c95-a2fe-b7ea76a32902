<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use App\Traits\HasSlugs;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CmsPage extends Model
{
    /** @use HasFactory<\Database\Factories\CmsPageFactory> */
    use CustomActivityLog, HasFactory, HasSlugs, SoftDeletes;

    protected $fillable = ['slug', 'is_active'];

    public function translations()
    {
        return $this->hasMany(CmsPageTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(CmsPageTranslation::class, 'cms_page_id', 'id')
            ->where('locale', app()->getLocale());
    }
}
