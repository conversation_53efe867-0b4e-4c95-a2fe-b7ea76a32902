<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StadiumSectorTranslation extends Model
{
    /** @use HasFactory<\Database\Factories\StadiumSectorTranslationFactory> */
    use CustomActivityLog, HasFactory;

    protected $fillable = ['stadium_sector_id', 'locale', 'name'];

    public function stadiumSector()
    {
        return $this->belongsTo(StadiumSector::class, 'stadium_sector_id', 'id');
    }
}
