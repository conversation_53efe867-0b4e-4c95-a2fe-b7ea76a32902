<?php

namespace App\Models;

use App\Enums\OrderTransactionStatus;
use App\Enums\OrderTransactionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderTransaction extends Model
{
    use HasFactory;

    protected $table = 'order_transactions';

    protected $guarded = [];

    protected $casts = [
        'status' => OrderTransactionStatus::class,
        'transaction_type' => OrderTransactionType::class,
        'paid_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
