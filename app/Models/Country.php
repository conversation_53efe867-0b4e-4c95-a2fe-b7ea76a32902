<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Country extends Model
{
    use CustomActivityLog, HasFactory, SoftDeletes;

    protected $fillable = ['shortcode', 'slug', 'phone_code', 'currency_code', 'currency_name', 'currency_symbol'];

    public function translations()
    {
        return $this->hasMany(CountryTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(CountryTranslation::class, 'country_id', 'id')
            ->where('locale', app()->getLocale());
    }
}
