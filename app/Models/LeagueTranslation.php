<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeagueTranslation extends Model
{
    /** @use HasFactory<\Database\Factories\LeagueTranslationFactory> */
    use CustomActivityLog, HasFactory;

    protected $fillable = ['league_id', 'locale', 'name', 'description', 'meta_title', 'meta_description', 'meta_keywords'];
}
