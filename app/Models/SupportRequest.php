<?php

namespace App\Models;

use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestStatus;
use App\Enums\SupportRequestType;
use App\Traits\CascadeSoftDeletes;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportRequest extends Model
{
    /** @use HasFactory<\Database\Factories\SupportRequestFactory> */
    use CascadeSoftDeletes, CustomActivityLog, HasFactory, SoftDeletes;

    protected $cascadeDeletes = ['messages'];

    protected $fillable = ['user_id', 'subject', 'request_type', 'status', 'priority'];

    protected $casts = [
        'request_type' => SupportRequestType::class,
        'status' => SupportRequestStatus::class,
        'priority' => SupportRequestPriority::class,
    ];

    protected static function boot()
    {
        parent::boot();

        self::created(function ($model) {
            $id = 'TGSR'.str_pad($model->id, 3, '0', STR_PAD_LEFT);

            $model->sr_no = $id;
            $model->save();
        });
    }

    public function messages()
    {
        return $this->hasMany(SupportRequestMessage::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function latestMessage()
    {
        return $this->hasOne(SupportRequestMessage::class)->latest();
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
}
