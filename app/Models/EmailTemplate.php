<?php

namespace App\Models;

use App\Enums\EmailTemplateType;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailTemplate extends Model
{
    /** @use HasFactory<\Database\Factories\NotificationTemplateFactory> */
    use CustomActivityLog, HasFactory, SoftDeletes;

    protected $fillable = ['template_key', 'template_purpose', 'template_type', 'is_active'];

    protected function casts(): array
    {
        return [
            'template_type' => EmailTemplateType::class,
        ];
    }

    public function translations()
    {
        return $this->hasMany(EmailTemplateTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(EmailTemplateTranslation::class, 'email_template_id', 'id')
            ->where('locale', app()->getLocale());
    }
}
