<?php

namespace App\Models;

use App\Enums\TicketReservationStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TicketReservation extends Model
{
    /** @use HasFactory<\Database\Factories\TicketReservationFactory> */
    use HasFactory;

    protected $table = 'ticket_reservations';

    protected $guarded = [];

    protected $casts = [
        'status' => TicketReservationStatus::class,
        'expires_at' => 'datetime',
    ];

    public function ticket()
    {
        return $this->belongsTo(Ticket::class, 'ticket_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function order()
    {
        return $this->hasOne(Order::class, 'ticket_reservation_id', 'id');
    }

    public function scopeReserved($query)
    {
        return $query->whereIn('status', [TicketReservationStatus::ACTIVE, TicketReservationStatus::PROCESSING]);
    }
}
