<?php

namespace App\Models;

use App\Enums\MediaLibrary;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;

class SupportRequestMessage extends Model implements HasMedia
{
    /** @use HasFactory<\Database\Factories\SupportRequestMessageFactory> */
    use CustomActivityLog, HasFactory, InteractsWithMedia, SoftDeletes;

    protected $fillable = ['support_request_id', 'user_id', 'message'];

    protected $casts = [
        'documents' => 'array',
    ];

    public function supportRequest()
    {
        return $this->belongsTo(SupportRequest::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(MediaLibrary::SUPPORT_REQUEST_DOCUMENTS->value);
    }
}
