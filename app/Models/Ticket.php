<?php

namespace App\Models;

use App\Enums\TicketQuantitySplitType;
use App\Enums\TicketType;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ticket extends Model
{
    use CustomActivityLog, SoftDeletes;

    /** @use HasFactory<\Database\Factories\TicketFactory> */
    use HasFactory;

    protected $casts = [
        'ticket_type' => TicketType::class,
        'quantity_split_type' => TicketQuantitySplitType::class,
    ];

    protected $appends = ['badge_color_class'];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($ticket) {
            $id = 'TGT'.str_pad($ticket->id, 3, '0', STR_PAD_LEFT);

            $ticket->ticket_no = $id;
            $ticket->save();
        });
    }

    public function sector()
    {
        return $this->belongsto(StadiumSector::class, 'sector_id', 'id');
    }

    public function event()
    {
        return $this->belongsto(Event::class, 'event_id', 'id');
    }

    public function reservations()
    {
        return $this->hasMany(TicketReservation::class, 'ticket_id', 'id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'ticket_id', 'id');
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    public function translations()
    {
        return $this->hasMany(TicketTranslation::class, 'ticket_id', 'id');
    }

    public function translation()
    {
        return $this->hasOne(TicketTranslation::class, 'ticket_id', 'id')
            ->where('locale', app()->getLocale());
    }

    public function restrictions()
    {
        return $this->belongsToMany(Restriction::class, 'ticket_restrictions', 'ticket_id', 'restriction_id');
    }

    public function getBadgeColorClassAttribute()
    {
        return $this->ticket_type->getBadgeColour();
    }
}
