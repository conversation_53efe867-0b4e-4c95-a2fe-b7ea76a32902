<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Enums\OrderTransactionType;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Order extends Model implements HasMedia
{
    /** @use HasFactory<\Database\Factories\OrderFactory> */
    use CustomActivityLog, HasFactory, InteractsWithMedia, SoftDeletes;

    protected $table = 'orders';

    protected $casts = [
        'status' => OrderStatus::class,
        'order_meta_data' => 'object',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($order) {
            $id = 'TGO'.str_pad($order->id, 3, '0', STR_PAD_LEFT);

            $order->order_no = $id;
            $order->save();
        });
    }

    public function buyer()
    {
        return $this->belongsTo(User::class, 'buyer_id', 'id');
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    public function ticket()
    {
        return $this->belongsTo(Ticket::class, 'ticket_id', 'id');
    }

    public function parent()
    {
        return $this->belongsTo(Order::class, 'parent_id', 'id');
    }

    public function child()
    {
        return $this->hasOne(Order::class, 'parent_id', 'id');
    }

    public function createBy()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function statusHistories()
    {
        return $this->hasMany(OrderStatusHistory::class, 'order_id', 'id');
    }

    public function latestStatusChange()
    {
        return $this->hasOne(OrderStatusHistory::class)->latest();
    }

    public function transactions()
    {
        return $this->hasMany(OrderTransaction::class, 'order_id', 'id');
    }

    public function purchaseTransaction()
    {
        return $this->hasOne(OrderTransaction::class, 'order_id', 'id')
            ->where('transaction_type', OrderTransactionType::PURCHASE);
    }

    public function refundTransaction()
    {
        return $this->hasOne(OrderTransaction::class, 'order_id', 'id')
            ->where('transaction_type', OrderTransactionType::REFUND);
    }

    public function assignees()
    {
        return $this->hasMany(User::class, 'order_id', 'id');
    }

    public function attendees()
    {
        return $this->hasMany(Attendee::class, 'order_id', 'id');
    }
}
