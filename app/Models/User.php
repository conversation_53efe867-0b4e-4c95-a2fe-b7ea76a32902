<?php

namespace App\Models;

use App\Enums\EmailTemplateKeys;
use App\Enums\UserType;
use App\Jobs\SendEmailJob;
use App\Traits\CustomActivityLog;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser, MustVerifyEmail
{
    use CustomActivityLog, HasApiTokens, HasFactory, HasRoles, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'password',
        'user_type',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'user_type' => UserType::class,
        ];
    }

    public function userDetail()
    {
        return $this->hasOne(UserDetail::class, 'user_id', 'id');
    }

    public function tickets()
    {
        return $this->hasMany(Ticket::class, 'seller_id', 'id');
    }

    public function payoutMethods()
    {
        return $this->hasMany(UserPayoutMethod::class, 'user_id', 'id');
    }

    public function wallet()
    {
        return $this->hasOne(UserWallet::class, 'user_id', 'id');
    }

    public function walletTransactions()
    {
        return $this->hasMany(UserWalletTransaction::class, 'user_id');
    }

    public function withdrawals()
    {
        return $this->hasMany(UserWithdrawal::class, 'user_id');
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return in_array($this->user_type, [UserType::SUPERADMIN, UserType::ADMIN]);
    }

    public function isSuperAdmin(): bool
    {
        return $this->user_type === UserType::SUPERADMIN;
    }

    /**
     * Send reset password email link via custom email template
     */
    public function sendPasswordResetNotification($token)
    {
        $email = request()->email;
        $url = route('password.reset', ['token' => $token, 'email' => $email]);

        $meta = [
            'name' => '',
            'locale' => app()->getLocale(),
            'resetPasswordLink' => $url,
        ];
        // Send reset password email notification
        SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_RESET_PASSWORD_LINK->value, $meta, [], [$email]);
    }
}
