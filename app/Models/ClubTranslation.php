<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClubTranslation extends Model
{
    /** @use HasFactory<\Database\Factories\ClubTranslationFactory> */
    use CustomActivityLog, HasFactory;

    protected $fillable = ['club_id', 'locale', 'name', 'description', 'detailed_description', 'meta_title',
        'meta_description', 'meta_keywords'];
}
