<?php

namespace App\Models;

use App\Enums\PayoutBankAccountType;
use App\Enums\PayoutMethodStatus;
use App\Enums\PayoutMethodType;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserPayoutMethod extends Model
{
    use CustomActivityLog, SoftDeletes;

    protected $casts = [
        'payout_method_type' => PayoutMethodType::class,
        'account_type' => PayoutBankAccountType::class,
        'status' => PayoutMethodStatus::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
