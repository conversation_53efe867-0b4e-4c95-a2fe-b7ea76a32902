<?php

namespace App\Models;

use App\Enums\RestrictionType;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Restriction extends Model
{
    /** @use HasFactory<\Database\Factories\RestrictionFactory> */
    use CustomActivityLog, HasFactory, SoftDeletes;

    protected $casts = [
        'type' => RestrictionType::class,
    ];

    public function translations()
    {
        return $this->hasMany(RestrictionTranslation::class);
    }

    public function translation()
    {
        return $this->hasOne(RestrictionTranslation::class, 'restriction_id', 'id')
            ->where('locale', app()->getLocale());
    }

    public function events()
    {
        return $this->belongsToMany(Restriction::class, 'event_restrictions', 'restriction_id', 'event_id');
    }

    public function tickets()
    {
        return $this->belongsToMany(Restriction::class, 'ticket_restrictions', 'restriction_id', 'ticket_id');
    }
}
