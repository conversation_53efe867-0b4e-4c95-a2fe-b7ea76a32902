<?php

namespace App\Models;

use App\Enums\WithdrawalStatus;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserWithdrawal extends Model
{
    use CustomActivityLog, SoftDeletes;

    protected $casts = [
        'status' => WithdrawalStatus::class,
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($withdrawal) {
            $withdrawNo = 'TGW'.str_pad($withdrawal->id, 3, '0', STR_PAD_LEFT);

            $withdrawal->withdraw_no = $withdrawNo;
            $withdrawal->save();
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function payoutMethod()
    {
        return $this->belongsTo(UserPayoutMethod::class, 'payout_method_id', 'id');
    }
}
