<?php

namespace App\Models;

use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplateTranslation extends Model
{
    /** @use HasFactory<\Database\Factories\NotificationTemplateFactory> */
    use CustomActivityLog, HasFactory;

    public $timestamps = false;

    protected $fillable = ['email_template_id', 'locale', 'subject', 'body'];
}
