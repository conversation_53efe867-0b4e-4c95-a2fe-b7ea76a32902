<?php

namespace App\Models;

use App\Enums\GenderType;
use App\Traits\CustomActivityLog;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserDetail extends Model
{
    use CustomActivityLog, HasFactory, SoftDeletes;

    protected $fillable = [
        'gender',
        'surname',
        'address',
        'zip',
        'city',
        'country_id',
        'phone',
        'company',
        'government_id',
        'description',
        'other_details',
        'user_id',
    ];

    protected $casts = [
        'gender' => GenderType::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }
}
