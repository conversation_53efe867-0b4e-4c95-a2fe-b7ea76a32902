<?php

namespace App\Jobs;

use App\Traits\SendEmailTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SendEmailTrait;
    use SerializesModels;

    /**
     * Email Data
     * array $order
     */
    protected $templateKey;

    protected $meta;

    protected $userIds;

    protected $toEmails;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($templateKey, $meta, $userIds = [], $toEmails = [])
    {
        $this->templateKey = $templateKey;
        $this->meta = $meta;
        $this->userIds = $userIds;
        $this->toEmails = $toEmails;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->sendEmails($this->templateKey, $this->meta, $this->userIds, $this->toEmails);
        } catch (\Exception $e) {
            Log::error('Email Notification Job Failed: '.$e->getMessage());
        }
    }
}
