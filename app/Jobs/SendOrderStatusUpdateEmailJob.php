<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Enums\OrderStatus;
use App\Models\GeneralSetting;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendOrderStatusUpdateEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Order Data
     * array $orderId
     */
    protected $orderId;

    protected $statusChangeBy;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId, $statusChangeBy = 'seller')
    {
        $this->orderId = $orderId;
        $this->statusChangeBy = $statusChangeBy;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $order = Order::find($this->orderId);

            $ticket = $order->order_meta_data->ticket;
            $event = $order->order_meta_data->event;
            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'orderNo' => $order->order_no,
                'orderDate' => date('d/m/Y', strtotime($order->purchase_date)),
                'eventName' => $event->name->{$language},
                'eventDate' => date('d/m/Y', strtotime($order->ticket->event->date)),
                'stadiumName' => $event->stadium->name->{$language},
                'stadiumAddress' => $event->stadium->address_line_1.', '.$event->stadium->address_line_2.', '.$event->stadium->country->{$language}.' - '.$event->stadium->postcode,
                'sectorName' => $ticket->sector->name,
                'ticketType' => $ticket->ticket_type->label,
                'toStatus' => $order->status->getLabel(),
                'fromStatus' => $order->latestStatusChange ? $order->latestStatusChange->from_status->getLabel() : '',
                'sellerName' => $order->seller->name.' - '.$order->seller->email,
                'changeReason' => $order->latestStatusChange?->reason ?? '',
            ];

            if ($order->status === OrderStatus::CONFIRMED) {
                SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_ORDER_CONFIRMATION->value, $meta, [$order->buyer_id]);
            }

            if ($order->status === OrderStatus::CANCELED) {
                SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_ORDER_CANCELLATION->value, $meta, [$order->buyer_id]);
            }

            if ($order->status === OrderStatus::UNDER_REVIEW) {
                SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_ORDER_UNDER_REVIEW->value, $meta, [$order->buyer_id]);
            }

            if ($order->status === OrderStatus::SHIPPED) {
                SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_ORDER_SHIPPED->value, $meta, [$order->buyer_id]);
            }

            if ($this->statusChangeBy === 'seller') {
                $adminEmail = GeneralSetting::getValue('admin_email');
                SendEmailJob::dispatch(EmailTemplateKeys::ADMIN_ORDER_STATUS_UPDATE_EMAIL->value, $meta, [], [$adminEmail]);
            }

            if ($this->statusChangeBy === 'admin') {
                SendEmailJob::dispatch(EmailTemplateKeys::SELLER_ORDER_STATUS_UPDATE_EMAIL->value, $meta, [$order->seller_id], []);
            }

        } catch (\Exception $e) {
            Log::error('Send Order status update email job failed: '.$e->getMessage());
        }
    }
}
