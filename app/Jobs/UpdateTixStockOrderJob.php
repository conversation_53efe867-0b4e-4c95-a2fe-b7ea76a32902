<?php

namespace App\Jobs;

use App\Services\TixStock\SyncOrderService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateTixStockOrderJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Order ID
     * int $orderId
     */
    protected $orderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $orderService = app(SyncOrderService::class);

            $orderService->updateOrder($this->orderId);

        } catch (\Exception $e) {
            Log::error('Update tixstock order job failed: '.$e->getMessage());
        }
    }
}
