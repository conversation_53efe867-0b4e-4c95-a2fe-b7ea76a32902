<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Models\GeneralSetting;
use App\Models\SupportRequestMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendSupportRequestReplyEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Support Request Message ID
     */
    protected $messageId;

    /**
     * Who replied: 'admin' or 'user'
     */
    protected $repliedBy;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($messageId, $repliedBy = 'user')
    {
        $this->messageId = $messageId;
        $this->repliedBy = $repliedBy;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $message = SupportRequestMessage::with(['supportRequest.user', 'supportRequest.order', 'user'])
                ->find($this->messageId);

            if (! $message) {
                Log::error('Support request message not found: '.$this->messageId);

                return;
            }

            $supportRequest = $message->supportRequest;
            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'srNo' => $supportRequest->sr_no,
                'orderNo' => $supportRequest->order ? $supportRequest->order->order_no : '',
                'requestSubject' => $supportRequest->subject,
                'requestType' => $supportRequest->request_type->getLabel(),
                'replyMessage' => $message->message,
                'repliedBy' => $message->user->name.' - '.$message->user->email,
                'replyDate' => date('d/m/Y H:i', strtotime($message->created_at)),
                'supportRequestUrl' => route('api.support-requests.show', $supportRequest->id),
            ];

            if ($this->repliedBy === 'admin') {
                // Admin replied, send email to the user who created the support request
                SendEmailJob::dispatch(
                    EmailTemplateKeys::SUPPORT_REQUEST_REPLY_TO_USER->value,
                    $meta,
                    [$supportRequest->user_id],
                    []
                );

                // If there's an order, also notify buyer and seller (if different from the user)
                if ($supportRequest->order) {
                    $notifyUserIds = [];

                    if ($supportRequest->order->buyer_id !== $supportRequest->user_id) {
                        $notifyUserIds[] = $supportRequest->order->buyer_id;
                    }

                    if ($supportRequest->order->seller_id !== $supportRequest->user_id) {
                        $notifyUserIds[] = $supportRequest->order->seller_id;
                    }

                    if (! empty($notifyUserIds)) {
                        SendEmailJob::dispatch(
                            EmailTemplateKeys::SUPPORT_REQUEST_REPLY_TO_USER->value,
                            $meta,
                            $notifyUserIds,
                            []
                        );
                    }
                }
            } else {
                // User replied, send email to admin
                $supportEmail = GeneralSetting::getValue('support_email');

                if ($supportEmail) {
                    SendEmailJob::dispatch(
                        EmailTemplateKeys::SUPPORT_REQUEST_REPLY_TO_ADMIN->value,
                        $meta,
                        [],
                        [$supportEmail]
                    );
                }
            }

        } catch (\Exception $e) {
            Log::error('Send support request reply email job failed: '.$e->getMessage());
        }
    }
}
