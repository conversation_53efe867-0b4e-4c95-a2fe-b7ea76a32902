<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Models\GeneralSetting;
use App\Models\SupportRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendSupportRequestCreationEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Support Request Data
     * array $supportRequestId
     */
    protected $supportRequestId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($supportRequestId)
    {
        $this->supportRequestId = $supportRequestId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $supportRequest = SupportRequest::find($this->supportRequestId);

            $srMessage = $supportRequest->messages->first();

            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'srNo' => $supportRequest->sr_no,
                'orderNo' => $supportRequest->order ? $supportRequest->order->order_no : '',
                'submittedBy' => $supportRequest->user->name.' - '.$supportRequest->user->email,
                'requestSubject' => $supportRequest->subject,
                'requestType' => $supportRequest->request_type->getLabel(),
                'requestMessage' => $srMessage ? $srMessage->message : '',
                'requestDate' => date('d/m/Y', strtotime($supportRequest->created_at)),
            ];

            $supportEmail = GeneralSetting::getValue('support_email');
            SendEmailJob::dispatch(EmailTemplateKeys::SUPPORT_REQUEST_USER_EMAIL->value, $meta, [$supportRequest->user_id]);

            SendEmailJob::dispatch(EmailTemplateKeys::SUPPORT_REQUEST_ADMIN_EMAIL->value, $meta, [], [$supportEmail]);

        } catch (\Exception $e) {
            Log::error('Send support request detail email job failed: '.$e->getMessage());
        }
    }
}
