<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Models\GeneralSetting;
use App\Models\UserWithdrawal;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendNewWithdrawalRequestEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Withdrawal Request Data
     * array $withdrawalRequestId
     */
    protected $withdrawalRequestId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($withdrawalRequestId)
    {
        $this->withdrawalRequestId = $withdrawalRequestId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $withdrawal = UserWithdrawal::find($this->withdrawalRequestId);

            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'srNo' => $withdrawal->withdraw_no,
                'submittedBy' => $withdrawal->user->name.' - '.$withdrawal->user->email,
                'amount' => $withdrawal->amount,
                'currencySymbol' => '€',
                'requestDate' => date('d/m/Y', strtotime($withdrawal->created_at)),
            ];

            $adminEmail = GeneralSetting::getValue('admin_email');
            SendEmailJob::dispatch(EmailTemplateKeys::WITHDRAWAL_REQUEST_USER_EMAIL->value, $meta, [$withdrawal->user_id]);

            SendEmailJob::dispatch(EmailTemplateKeys::WITHDRAWAL_REQUEST_ADMIN_EMAIL->value, $meta, [], [$adminEmail]);

        } catch (\Exception $e) {
            Log::error('Send withdrawal request detail email job failed: '.$e->getMessage());
        }
    }
}
