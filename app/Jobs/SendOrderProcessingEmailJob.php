<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Enums\OrderStatus;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendOrderProcessingEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Order Data
     * array $orderId
     */
    protected $orderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $order = Order::find($this->orderId);

            $ticket = $order->order_meta_data->ticket;
            $event = $order->order_meta_data->event;
            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'orderNo' => $order->order_no,
                'orderDate' => date('d/m/Y', strtotime($order->purchase_date)),
                'eventName' => $event->name->{$language},
                'eventDate' => date('d/m/Y', strtotime($order->ticket->event->date)),
                'stadiumName' => $event->stadium->name->{$language},
                'stadiumAddress' => $event->stadium->address_line_1.', '.$event->stadium->address_line_2.', '.$event->stadium->country->{$language}.' - '.$event->stadium->postcode,
                'sectorName' => $ticket->sector->name,
                'ticketType' => $ticket->ticket_type->label,
                'price' => '€'.$order->price,
                'quantity' => $order->quantity,
                'totalPrice' => '€'.$order->total_price,
                'serviceChargeAmount' => '€'.$order->service_charge_amount,
                'taxAmount' => '€'.$order->tax_amount,
                'grandTotal' => '€'.$order->grand_total,
            ];

            if ($order->status === OrderStatus::CONFIRMED) {
                SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_ORDER_CONFIRMATION->value, $meta, [$order->buyer_id]);
            } else {
                SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_ORDER_PROCESSING->value, $meta, [$order->buyer_id]);
            }

            SendEmailJob::dispatch(EmailTemplateKeys::SELLER_NEW_ORDER->value, $meta, [$order->ticket->seller_id]);
        } catch (\Exception $e) {
            Log::error('Send Order processing / confirmation email job failed: '.$e->getMessage());
        }
    }
}
