<?php

namespace App\Jobs;

use App\Models\TicketReservation;
use App\Services\TixStock\SyncTicketsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ReleaseTixStockTicketJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Reservation ID
     * array $reservationId
     */
    protected $reservationId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($reservationId)
    {
        $this->reservationId = $reservationId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $reservation = TicketReservation::find($this->reservationId);

            $tixStockTicketService = app(SyncTicketsService::class);

            $tixStockTicketService->releaseTicket($reservation);

            Log::info('Release tixstock ticket job successfully completed');

        } catch (\Exception $e) {
            Log::error('Release tixstock ticket job failed: '.$e->getMessage());
        }
    }
}
