<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Models\SupportRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendSupportRequestClosedEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Support Request ID
     */
    protected $supportRequestId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($supportRequestId)
    {
        $this->supportRequestId = $supportRequestId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $supportRequest = SupportRequest::with(['user', 'order'])
                ->find($this->supportRequestId);

            if (! $supportRequest) {
                Log::error('Support request not found: '.$this->supportRequestId);

                return;
            }

            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'srNo' => $supportRequest->sr_no,
                'orderNo' => $supportRequest->order ? $supportRequest->order->order_no : '',
                'requestSubject' => $supportRequest->subject,
                'requestType' => $supportRequest->request_type->getLabel(),
                'closedDate' => date('d/m/Y H:i', strtotime($supportRequest->updated_at)),
                'userName' => $supportRequest->user->name,
            ];

            // Send email to the user who created the support request
            SendEmailJob::dispatch(
                EmailTemplateKeys::SUPPORT_REQUEST_CLOSED_EMAIL->value,
                $meta,
                [$supportRequest->user_id],
                []
            );

            // If there's an order, also notify buyer and seller (if different from the original user)
            if ($supportRequest->order) {
                $notifyUserIds = [];

                if ($supportRequest->order->buyer_id !== $supportRequest->user_id) {
                    $notifyUserIds[] = $supportRequest->order->buyer_id;
                }

                if ($supportRequest->order->seller_id !== $supportRequest->user_id) {
                    $notifyUserIds[] = $supportRequest->order->seller_id;
                }

                if (! empty($notifyUserIds)) {
                    SendEmailJob::dispatch(
                        EmailTemplateKeys::SUPPORT_REQUEST_CLOSED_EMAIL->value,
                        $meta,
                        $notifyUserIds,
                        []
                    );
                }
            }

        } catch (\Exception $e) {
            Log::error('Send support request closed email job failed: '.$e->getMessage());
        }
    }
}
