<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Models\GeneralSetting;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendOrderOpenDisputeEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Order Data
     * array $orderId
     */
    protected $orderId;

    protected $openDisputeData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId, $openDisputeData)
    {
        $this->orderId = $orderId;
        $this->openDisputeData = $openDisputeData;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $order = Order::find($this->orderId);

            $ticket = $order->order_meta_data->ticket;
            $event = $order->order_meta_data->event;
            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'orderNo' => $order->order_no,
                'eventName' => $event->name->{$language},
                'buyerName' => $order->buyer->name.' - '.$order->buyer->email,
                'disputeReason' => $this->openDisputeData['reason'] ?? '',
            ];

            $adminEmail = GeneralSetting::getValue('admin_email');

            SendEmailJob::dispatch(EmailTemplateKeys::BUYER_ORDER_OPEN_DISPUTE_EMAIL->value, $meta, [$order->seller_id], [$adminEmail]);

        } catch (\Exception $e) {
            Log::error('Send Order open dispute email job failed: '.$e->getMessage());
        }
    }
}
