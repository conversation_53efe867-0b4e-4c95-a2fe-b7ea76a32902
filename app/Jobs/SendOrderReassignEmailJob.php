<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendOrderReassignEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Order Data
     * array $oldOrderId
     */
    protected $oldOrderId;

    protected $newOrderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($oldOrderId, $newOrderId)
    {
        $this->oldOrderId = $oldOrderId;
        $this->newOrderId = $newOrderId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $oldOrder = Order::find($this->oldOrderId);
            $order = Order::find($this->newOrderId);

            $oldTicket = $oldOrder->order_meta_data->ticket;
            $ticket = $order->order_meta_data->ticket;
            $event = $order->order_meta_data->event;
            $language = LanguageCode::ENGLISH->value;

            $meta = [
                'locale' => $language,
                'orderNo' => $order->order_no,
                'oldOrderNo' => $oldOrder->order_no,
                'newOrderNo' => $order->order_no,
                'orderDate' => date('d/m/Y', strtotime($order->purchase_date)),
                'oldOrderDate' => date('d/m/Y', strtotime($oldOrder->purchase_date)),
                'eventName' => $event->name->{$language},
                'ticketNo' => $oldTicket->ticket_no,
                'newTicketNo' => $ticket->ticket_no,
                'reason' => $oldOrder->latestStatusChange?->reason ?? '',
            ];

            SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_ORDER_REASSIGN->value, $meta, [$order->buyer_id], []);
            SendEmailJob::dispatch(EmailTemplateKeys::OLD_SELLER_ORDER_REASSIGN->value, $meta, [$oldOrder->seller_id], []);
            SendEmailJob::dispatch(EmailTemplateKeys::NEW_SELLER_ORDER_REASSIGN->value, $meta, [$order->seller_id], []);

        } catch (\Exception $e) {
            Log::error('Send Order status update email job failed: '.$e->getMessage());
        }
    }
}
