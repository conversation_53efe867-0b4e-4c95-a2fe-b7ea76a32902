<?php

namespace App\Jobs;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Enums\WalletEntryType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Enums\WithdrawalStatus;
use App\Models\UserWithdrawal;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;

class SendWithdrawalRequestStatusChangeEmailJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Withdrawal Request Data
     * array $withdrawalRequestId
     */
    protected $withdrawalRequestId;

    protected $transactions;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($withdrawalRequestId, $transactions)
    {
        $this->withdrawalRequestId = $withdrawalRequestId;
        $this->transactions = $transactions;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $withdrawal = UserWithdrawal::find($this->withdrawalRequestId);

            $language = LanguageCode::ENGLISH->value;

            $statusMessage = '';
            $walletTransactionsList = '';

            if ($withdrawal->status === WithdrawalStatus::APPROVED) {
                $statusMessage = Lang::get('my_withdrawals.email_approved_message', [], $language);
            }

            if ($withdrawal->status === WithdrawalStatus::REJECTED) {
                $statusMessage = Lang::get('my_withdrawals.email_rejected_message', [], $language);
            }

            $amount = $withdrawal->amount;

            if ($withdrawal->status === WithdrawalStatus::PAID) {
                $statusMessage = Lang::get('my_withdrawals.email_paid_message', [], $language);

                if (! empty($this->transactions)) {
                    $walletTransactionsList .= '<tr>
                        <td align="left" valign="top" style="padding: 10px 10px 10px 20px;font-size: 12px;line-height: 25px;"
                        >
                            <table align="center" cellpadding="3" cellspacing="0" style="width: 100%;border-collapse: collapse;border: 1px solid #dbdbdb;font-family:Arial, Helvetica, sans-serif;font-size: 11px;color: #333333;"
                            >
                                <tbody>
                                    <tr>
                                        <td style="font-weight:bold;background-color:#f3f3f3;" align="left">
                                            '.Lang::get('my_withdrawals.labels.transaction', [], $language).' #
                                        </td>
                                        <td style="font-weight:bold;background-color:#f3f3f3;" align="left">
                                            '.Lang::get('my_withdrawals.labels.order', [], $language).' #
                                        </td>
                                        <td style="font-weight:bold;background-color:#f3f3f3;" align="left">
                                            '.Lang::get('my_withdrawals.labels.transaction_type', [], $language).'
                                        </td>
                                        <td style="font-weight:bold;background-color:#f3f3f3;" align="left">
                                            '.Lang::get('my_withdrawals.labels.withdrawn', [], $language).'
                                        </td>
                                        <td style="font-weight:bold;background-color:#f3f3f3;" align="left">
                                            '.Lang::get('my_withdrawals.labels.remaining', [], $language).'
                                        </td>
                                        <td style="font-weight:bold;background-color:#f3f3f3;" align="left">
                                            '.Lang::get('my_withdrawals.labels.status', [], $language).'
                                        </td>
                                    </tr>';

                    foreach ($this->transactions as $transaction) {
                        $remainedAmount = $transaction['remained_amount'];
                        $withdrawnAmount = $transaction['withdrawn_amount'];
                        $status = WalletTransactionStatus::WITHDRAWN->value;

                        if ($transaction['entry_type'] === WalletEntryType::CREDIT->value) {
                            if ($amount >= $transaction['remained_amount']) {
                                $amount -= $transaction['remained_amount'];
                                $withdrawnAmount = $transaction['remained_amount'];
                                $remainedAmount = 0;
                            } else {
                                $withdrawnAmount = $amount;
                                $remainedAmount -= $amount;
                                $amount = 0;
                                $status = WalletTransactionStatus::PARTIAL_WITHDRAWN->value;
                            }
                        }
                        $walletTransactionsList .= '<tr>
                            <td align="left">'.$transaction['transaction_no'].'</td>
                            <td align="left">'.(! empty($transaction['order']) ? $transaction['order']['order_no'] : '').'</td>
                            <td align="left">'.WalletTransactionType::from($transaction['transaction_type'])->getLabel().'</td>
                            <td align="left">€'.number_format($withdrawnAmount, 2).' ('.WalletEntryType::from($transaction['entry_type'])->getLabel().')</td>
                            <td align="left">€'.number_format($remainedAmount, 2).'</td>
                            <td align="left">'.WalletTransactionStatus::from($status)->getLabel().'</td>
                        </tr>';
                    }

                    $walletTransactionsList .= '</tbody></table></td></tr>';
                }
            }

            $meta = [
                'locale' => $language,
                'withdrawNo' => $withdrawal->withdraw_no,
                'amount' => $withdrawal->amount,
                'currencySymbol' => '€',
                'status' => $withdrawal->status->getLabel(),
                'requestDate' => date('d/m/Y', strtotime($withdrawal->created_at)),
                'statusMessage' => $statusMessage,
                'note' => $withdrawal->note,
                'walletTransactionsList' => $walletTransactionsList,
            ];

            SendEmailJob::dispatch(EmailTemplateKeys::WITHDRAWAL_REQUEST_STATUS_CHANGE_EMAIL->value, $meta, [$withdrawal->user_id]);

        } catch (\Exception $e) {
            Log::error('Send withdrawal request detail email job failed: '.$e->getMessage());
        }
    }
}
