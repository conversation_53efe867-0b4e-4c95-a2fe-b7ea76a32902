<?php

namespace App\Rules;

use App\Enums\UserType;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateTicketCreateQuantity implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = auth()->user();

        if (! in_array($user->user_type->value, [UserType::CUSTOMER->value, UserType::BROKER->value])) {
            $fail(__('message.TICKETS_CREATION_LIMIT_EXCEED'));
        }
        if ($user->user_type === UserType::CUSTOMER) {
            $totalTickets = $user->tickets->sum('quantity') + $user->tickets->sum('sold_quantity');

            $limit = config('services.ticketgol.max_ticket_creation_limit');

            if ($totalTickets >= $limit) {
                $fail(__('message.TICKETS_CREATION_LIMIT_EXCEED'));
            }
            if ($totalTickets + $value > $limit) {
                $fail(__('message.TICKETS_CREATION_LIMIT_EXCEED_WITH_REMAIN', ['remaining' => $limit - $totalTickets]));
            }
        }
    }
}
