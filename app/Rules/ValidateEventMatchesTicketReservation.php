<?php

namespace App\Rules;

use App\Repositories\TicketReservationRepository;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateEventMatchesTicketReservation implements DataAwareRule, ValidationRule
{
    protected $data = [];

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! isset($this->data['ticket_reservation_id'])) {
            $fail(__('validation.custom.ticket_reservation_id'));

            return;
        }

        $encryptedReservationId = $this->data['ticket_reservation_id'];
        $ticketReservationId = decrypt($encryptedReservationId);
        $ticketReservation = app(TicketReservationRepository::class)->findById($ticketReservationId);

        if ($ticketReservation->ticket->event_id !== (int) $value) {
            $fail(__('validation.custom.event_id'));
        }
    }
}
