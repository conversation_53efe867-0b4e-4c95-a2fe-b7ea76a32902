<?php

namespace App\Rules;

use App\Enums\OrderStatus;
use App\Models\Order;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateOpenDisputeUpdate implements ValidationRule
{
    private $orderId;

    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $order = Order::findOrFail($this->orderId);

        if ($order->buyer_id !== auth()->user()->id) {
            $fail(__('message.OPEN_DISPUTE_PERMISSION_RESTRICT'));
        }

        if ($order->status !== OrderStatus::SHIPPED) {
            $fail(__('message.OPEM_DISPUTE_NOT_ALLOWED'));
        }
    }
}
