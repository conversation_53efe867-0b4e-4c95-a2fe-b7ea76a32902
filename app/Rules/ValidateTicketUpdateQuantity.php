<?php

namespace App\Rules;

use App\Enums\UserType;
use App\Models\Ticket;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateTicketUpdateQuantity implements ValidationRule
{
    private $ticketId;

    public function __construct($ticketId)
    {
        $this->ticketId = $ticketId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $ticket = Ticket::withSum(['reservations' => function ($query) {
            $query->reserved();
        }], 'quantity')->findOrFail($this->ticketId);

        if ($ticket->reservations_sum_quantity && $value < $ticket->reservations_sum_quantity) {
            $fail(__('message.TICKETS_QUANTITY_UPDATE_ERROR', ['reserved' => $ticket->reservations_sum_quantity]));
        }

        $user = auth()->user();

        if ($ticket->seller_id !== $user->id) {
            $fail(__('message.TICKETS_UPDATE_USER_ERROR'));
        }

        if ($user->user_type === UserType::CUSTOMER) {
            $totalTickets = ($user->tickets->sum('quantity') + $user->tickets->sum('sold_quantity')) - $ticket->quantity;

            $limit = config('services.ticketgol.max_ticket_creation_limit');

            if ($totalTickets >= $limit) {
                $fail(__('message.TICKETS_CREATION_LIMIT_EXCEED'));
            }
            if ($totalTickets + $value > $limit) {
                $fail(__('message.TICKETS_CREATION_LIMIT_EXCEED_WITH_REMAIN', ['remaining' => $limit - $totalTickets]));
            }
        }
    }
}
