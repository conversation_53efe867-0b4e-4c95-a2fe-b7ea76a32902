<?php

namespace App\Rules;

use App\Services\TicketReservationService;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateTicketReservationKey implements ValidationRule
{
    private $ticketReservationService;

    public function __construct()
    {
        $this->ticketReservationService = app(TicketReservationService::class);
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! $this->ticketReservationService->isKeyValid($value)) {
            $fail(__('validation.custom.ticket_reservation_id'));
        }

        $ticketReservation = $this->ticketReservationService->getReservationDetail($value);

        if ($ticketReservation && $ticketReservation->order) {
            $fail(__('validation.custom.ticket_reservation_order_exists'));
        }
    }
}
