<?php

namespace App\Rules;

use App\Models\Ticket;
use App\Services\TicketReservationService;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateTicketQuantity implements ValidationRule
{
    private $ticketId;

    public function __construct($ticketId)
    {
        $this->ticketId = $ticketId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $ticket = Ticket::findOrFail($this->ticketId);

        if ($ticket->seller_id === auth()->user()->id) {
            $fail(__('message.OWN_TICKET_PURCHASE_RESTRICT'));
        }

        $ticketReservationService = app(TicketReservationService::class);
        $reservedTickets = $ticketReservationService->getAvailableTickets($this->ticketId);

        $remainingQuantity = (int) $ticket->quantity - $reservedTickets;

        if ($value > $remainingQuantity) {
            $fail(__('message.TICKET_PURCHASE_QUANTITY_EXCEED', ['remainingQuantity' => $remainingQuantity]));
        }
    }
}
