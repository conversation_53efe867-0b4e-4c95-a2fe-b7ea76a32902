<?php

namespace App\Rules;

use App\Enums\WithdrawalStatus;
use App\Models\UserWithdrawal;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateWithdrawalAmount implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = auth()->user();

        if ($user->wallet && $user->wallet->balance < $value) {
            $fail(__('message.WITHDRAWAL_INSUFFICIENT_AMOUNT'));
        }

        $checkWithdrawal = UserWithdrawal::where('user_id', $user->id)
            ->whereIn('status', [WithdrawalStatus::PENDING->value, WithdrawalStatus::APPROVED->value])
            ->latest()
            ->first();

        if ($checkWithdrawal) {
            $fail(__('message.WITHDRAWAL_REQUEST_PENDING'));
        }
    }
}
