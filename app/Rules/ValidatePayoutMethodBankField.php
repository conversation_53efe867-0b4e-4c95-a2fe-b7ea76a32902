<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidatePayoutMethodBankField implements ValidationRule
{
    private $countryCode;

    private $paymentMethodType;

    private array $regexRules = [
        'account_number' => '/^[0-9]{6,20}$/',
        'account_holder_name' => '/^[a-zA-Z\s]{2,50}$/',
        'iban' => '/^[A-Z]{2}[0-9A-Z]{13,32}$/',
        'swift_code' => '/^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$/',
        'ifsc_code' => '/^[A-Z]{4}0[A-Z0-9]{6}$/',
        'routing_number' => '/^[0-9]{9}$/',
        'sort_code' => '/^[0-9]{6}$/',
        'bsb' => '/^[0-9]{6}$/',
        'bank_code' => '/^[0-9]{3,5}$/',
        'branch_code' => '/^[0-9]{1,6}$/',
        'institution_number' => '/^[0-9]{3}$/',
        'transist_number' => '/^[0-9]{5}$/',
    ];

    public function __construct($countryCode, $paymentMethodType)
    {
        $this->countryCode = $countryCode;
        $this->paymentMethodType = $paymentMethodType;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->paymentMethodType !== 'bank') {
            return;
        }

        $path = resource_path('js/helpers/bankFieldsByCountry.json');
        $fieldsByCountry = json_decode(file_get_contents($path), true);
        $countryFields = $fieldsByCountry[$this->countryCode] ?? [];

        $fieldName = last(explode('.', $attribute));

        $field = collect($countryFields)->firstWhere('name', $fieldName);

        if (! $field) {
            return;
        }

        if (($field['required'] ?? false) && empty($value)) {
            $fail(trans('validation.required'));

            return;
        }

        if (! empty($value) && isset($this->regexRules[$fieldName])) {
            if (! preg_match($this->regexRules[$fieldName], $value)) {
                $fail(trans("validation.custom.{$fieldName}"));

                return;
            }
        }
    }
}
