<?php

namespace App\Rules;

use App\Enums\OrderStatus;
use App\Models\Order;
use App\Traits\OrderStatusActionTrait;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateOrderStatusUpdate implements ValidationRule
{
    use OrderStatusActionTrait;

    private $orderId;

    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $order = Order::findOrFail($this->orderId);

        if ($order->seller_id !== auth()->user()->id) {
            $fail(__('message.ORDER_STATUS_UPDATE_PERMISSION_RESTRICT'));
        }

        if ($value === OrderStatus::CANCELED->value && $order->parent_id) {
            $fail(__('message.ORDER_STATUS_CANCEL_PERMISSION_RESTRICT'));
        }

        $statusActions = $this->getOrderStatusActionList($order->status->value);

        $statusActionKeys = collect($statusActions)->keys()->all();

        $newStatus = OrderStatus::from($value)->getLabel();

        if (! in_array($value, $statusActionKeys)) {
            $fail(__('message.ORDER_STATUS_CHANGE_NOT_ALLOWED', ['currentStatus' => $order->status->getLabel(), 'newStatus' => $newStatus]));
        }
    }
}
