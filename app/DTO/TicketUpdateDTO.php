<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class TicketUpdateDTO implements DTOInterface
{
    public function __construct(
        public int $ticketId,
        public int $quantity,
        public float $price,
        public float $faceValuePrice,
        public ?string $ticketRows,
        public ?string $ticketSeats,
        public string $currencyCode,
        public string $description,
        public string $ticketType,
        public string $quantitySplitType,
        public ?array $restrictions,
    ) {}

    public function toArray(): array
    {
        return [
            'ticketId' => $this->ticketId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'faceValuePrice' => $this->faceValuePrice,
            'ticketRows' => $this->ticketRows,
            'ticketSeats' => $this->ticketSeats,
            'currencyCode' => $this->currencyCode,
            'description' => $this->description,
            'ticketType' => $this->ticketType,
            'quantitySplitType' => $this->quantitySplitType,
            'restrictions' => $this->restrictions,
        ];
    }
}
