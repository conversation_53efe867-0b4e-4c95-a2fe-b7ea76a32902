<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class SupportTicketFilterDTO implements DTOInterface
{
    public function __construct(
        public ?array $requestType = [],
        public ?array $status = [],
        public ?array $priority = [],
    ) {}

    public function toArray(): array
    {
        return [
            'request_type' => $this->requestType,
            'status' => $this->status,
            'priority' => $this->priority,
        ];
    }
}
