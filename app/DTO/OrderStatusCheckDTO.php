<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class OrderStatusCheckDTO implements DTOInterface
{
    public function __construct(
        public string $orderId,
        public bool $checkPaymentStatus,
    ) {}

    public function toArray(): array
    {
        return [
            'order_id' => $this->orderId,
            'checkPaymentStatus' => $this->checkPaymentStatus,
        ];
    }
}
