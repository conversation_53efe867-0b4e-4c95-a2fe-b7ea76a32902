<?php

namespace App\DTO;

use App\Contracts\DTOInterface;
use Illuminate\Http\UploadedFile;

class OrderStatusUpdateDTO implements DTOInterface
{
    public function __construct(
        public int $orderId,
        public string $status,
        public ?int $userId,
        public ?string $reason,
        /** @var UploadedFile[]|null */
        public ?array $tickets,
        public ?UploadedFile $additionalDoc,
    ) {}

    public function toArray(): array
    {
        return [
            'order_id' => $this->orderId,
            'status' => $this->status,
            'user_id' => $this->userId,
            'reason' => $this->reason,
        ];
    }
}
