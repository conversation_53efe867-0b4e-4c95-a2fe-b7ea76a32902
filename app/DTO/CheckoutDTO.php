<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class CheckoutDTO implements DTOInterface
{
    public function __construct(
        public int $eventId,
        public string $encryptedReservationId,
        public ?string $tempTicketReservationId,
        public int $ticketId,
        public int $quantity,
        public string $currencyCode,
        public array $attendees,
        public string $successUrl,
        public string $cancelUrl,
    ) {}

    public function toArray(): array
    {
        return [
            'event_id' => $this->eventId,
            'encrypted_reservation_id' => $this->encryptedReservationId,
            'ticket_id' => $this->ticketId,
            'quantity' => $this->quantity,
            'currency_code' => $this->currencyCode,
            'attendees' => $this->attendees,
            'success_url' => $this->successUrl,
            'cancel_url' => $this->cancelUrl,
        ];
    }
}
