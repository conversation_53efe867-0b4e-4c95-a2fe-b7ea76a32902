<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class StadiumFilterDTO implements DTOInterface
{
    public function __construct(
        public array $countries,
        public ?string $search = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'countries' => $this->countries,
            'search' => $this->search,
            'sort' => $this->sort,
        ];
    }
}
