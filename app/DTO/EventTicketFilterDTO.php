<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class EventTicketFilterDTO implements DTOInterface
{
    public function __construct(
        public int $eventId,
        public array $priceRange,
        public ?string $sector = '',
        public ?string $quantity = '',
        public ?string $ticketType = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'eventId' => $this->eventId,
            'priceRange' => $this->priceRange,
            'sector' => $this->sector,
            'quantity' => $this->quantity,
            'ticketType' => $this->ticketType,
            'sort' => $this->sort,
        ];
    }
}
