<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class PayoutMethodStoreDTO implements DTOInterface
{
    public function __construct(
        public string $payoutMethodType,
        public string $countryCode,
        public ?string $bankName,
        public ?string $branchName,
        public ?string $accountType,
        public ?string $accountHolderName,
        public ?string $accountNumber,
        public ?string $swiftCode,
        public ?string $iban,
        public ?string $ifscCode,
        public ?string $routingNumber,
        public ?string $sortCode,
        public ?string $bsb,
        public ?string $bankCode,
        public ?string $branchCode,
        public ?string $institutionNumber,
        public ?string $transitNumber,
    ) {}

    public function toArray(): array
    {
        return [
            'payout_method_type' => $this->payoutMethodType,
            'country_code' => $this->countryCode,
            'bank_name' => $this->bankName,
            'branch_name' => $this->branchName,
            'account_type' => $this->accountType,
            'account_holder_name' => $this->accountHolderName,
            'account_number' => $this->accountNumber,
            'swift_code' => $this->swiftCode,
            'iban' => $this->iban,
            'ifsc_code' => $this->ifscCode,
            'routing_number' => $this->routingNumber,
            'sort_code' => $this->sortCode,
            'bsb' => $this->bsb,
            'bank_code' => $this->bankCode,
            'branch_code' => $this->branchCode,
            'institution_number' => $this->institutionNumber,
            'transit_number' => $this->transitNumber,
        ];
    }
}
