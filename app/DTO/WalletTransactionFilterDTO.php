<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class WalletTransactionFilterDTO implements DTOInterface
{
    public function __construct(
        public ?string $search = '',
        public ?string $dateFrom = '',
        public ?string $dateTo = '',
        public ?string $transactionType = '',
        public ?string $entryType = '',
        public ?string $status = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'search' => $this->search,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
            'transaction_type' => $this->transactionType,
            'entry_type' => $this->entryType,
            'status' => $this->status,
            'sort' => $this->sort,
        ];
    }
}
