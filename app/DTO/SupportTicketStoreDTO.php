<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class SupportTicketStoreDTO implements DTOInterface
{
    public function __construct(
        public string $subject,
        public string $message,
        public string $request_type,
        public string $priority,
        public array $files = [],
    ) {}

    public function toArray(): array
    {
        return [
            'subject' => $this->subject,
            'message' => $this->message,
            'request_type' => $this->request_type,
            'priority' => $this->priority,
            'files' => $this->files,
        ];
    }
}
