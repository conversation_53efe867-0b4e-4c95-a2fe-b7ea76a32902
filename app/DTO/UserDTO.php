<?php

namespace App\DTO;

use App\Contracts\UserDTOInterface;
use Illuminate\Support\Facades\Hash;

class UserDTO implements UserDTOInterface
{
    public function __construct(
        public string $name,
        public string $user_name,
        public string $email,
        public string $surname,
        public string $gender,
        public string $phone,
        public string $address,
        public string $city,
        public int $country_id,
        public string $zip,
        public ?string $password = null,
        public ?string $company = null,
        public ?string $government_id = null,
    ) {}

    public function toArray(): array
    {
        $data = [
            'name' => $this->name,
            'user_name' => $this->user_name,
            'email' => $this->email,
        ];

        if (! empty($this->password)) {
            $data['password'] = Hash::make($this->password);
        }

        return $data;
    }

    public function toUserDetailArray(int $userId): array
    {
        return [
            'user_id' => $userId,
            'surname' => $this->surname,
            'phone' => $this->phone,
            'gender' => $this->gender,
            'address' => $this->address,
            'city' => $this->city,
            'zip' => $this->zip,
            'country_id' => $this->country_id,
            'company' => $this->company,
        ];
    }
}
