<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class OrderOpenDisputeDTO implements DTOInterface
{
    public function __construct(
        public int $orderId,
        public int $userId,
        public string $status,
        public ?string $reason,
    ) {}

    public function toArray(): array
    {
        return [
            'order_id' => $this->orderId,
            'user_id' => $this->userId,
            'status' => $this->status,
            'reason' => $this->reason,
        ];
    }
}
