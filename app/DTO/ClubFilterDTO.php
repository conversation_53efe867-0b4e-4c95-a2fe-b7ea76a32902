<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class ClubFilterDTO implements DTOInterface
{
    public function __construct(
        public array $stadiums,
        public array $countries,
        public ?string $search = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'stadiums' => $this->stadiums,
            'countries' => $this->countries,
            'search' => $this->search,
            'sort' => $this->sort,
        ];
    }
}
