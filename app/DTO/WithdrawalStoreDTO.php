<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class WithdrawalStoreDTO implements DTOInterface
{
    public function __construct(
        public int $userId,
        public float $amount,
        public float $previousAmount,
        public string $currencyCode,
        public int $payoutMethodId,
    ) {}

    public function toArray(): array
    {
        return [
            'user_id' => $this->userId,
            'amount' => $this->amount,
            'previous_amount' => $this->previousAmount,
            'currency_code' => $this->currencyCode,
            'payout_method_id' => $this->payoutMethodId,
        ];
    }
}
