<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class OrderStoreDTO implements DTOInterface
{
    public function __construct(
        public int $eventId,
        public string $encryptedReservationId,
        public ?string $tempTicketReservationId,
        public int $ticketId,
        public int $sellerId,
        public int $quantity,
        public string $price,
        public string $totalPrice,
        public string $serviceChargeAmount,
        public string $taxAmount,
        public string $grandTotal,
        public string $currencyCode,
        public array $attendees,
    ) {}

    public function toArray(): array
    {
        return [
            'event_id' => $this->eventId,
            'encrypted_reservation_id' => $this->encryptedReservationId,
            'ticket_id' => $this->ticketId,
            'seller_id' => $this->sellerId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'total_price' => $this->totalPrice,
            'service_charge_amount' => $this->serviceChargeAmount,
            'tax_amount' => $this->taxAmount,
            'grand_total' => $this->grandTotal,
            'currency_code' => $this->currencyCode,
            'attendees' => $this->attendees,
        ];
    }
}
