<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class TicketStoreDTO implements DTOInterface
{
    public function __construct(
        public int $eventId,
        public int $quantity,
        public float $price,
        public float $faceValuePrice,
        public int $sectorId,
        public ?string $ticketRows,
        public ?string $ticketSeats,
        public string $currencyCode,
        public string $description,
        public string $ticketType,
        public string $quantitySplitType,
        public ?int $sellInMultiples,
        public ?array $restrictions,
    ) {}

    public function toArray(): array
    {
        return [
            'eventId' => $this->eventId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'faceValuePrice' => $this->faceValuePrice,
            'sectorId' => $this->sectorId,
            'ticketRows' => $this->ticketRows,
            'ticketSeats' => $this->ticketSeats,
            'description' => $this->description,
            'currencyCode' => $this->currencyCode,
            'ticketType' => $this->ticketType,
            'quantitySplitType' => $this->quantitySplitType,
            'sellInMultiples' => $this->sellInMultiples,
            'restrictions' => $this->restrictions,
        ];
    }
}
