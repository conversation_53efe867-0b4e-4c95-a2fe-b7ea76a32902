<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class MyTicketFilterDTO implements DTOInterface
{
    public function __construct(
        public ?string $search = '',
        public ?string $ticketType = '',
        public ?string $dateFrom = '',
        public ?string $dateTo = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'search' => $this->search,
            'ticketType' => $this->ticketType,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
            'sort' => $this->sort,
        ];
    }
}
