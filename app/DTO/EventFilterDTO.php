<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class EventFilterDTO implements DTOInterface
{
    public function __construct(
        public array $categories,
        public array $clubs,
        public array $countries,
        public array $leagues,
        public array $stadiums,
        public ?string $search = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'categories' => $this->categories,
            'clubs' => $this->clubs,
            'countries' => $this->countries,
            'leagues' => $this->leagues,
            'stadiums' => $this->stadiums,
            'search' => $this->search,
            'sort' => $this->sort,
        ];
    }
}
