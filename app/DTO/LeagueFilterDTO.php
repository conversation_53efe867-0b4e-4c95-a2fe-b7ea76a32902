<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class LeagueFilterDTO implements DTOInterface
{
    public function __construct(
        public array $seasons,
        public array $countries,
        public ?string $search = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'seasons' => $this->seasons,
            'countries' => $this->countries,
            'search' => $this->search,
            'sort' => $this->sort,
        ];
    }
}
