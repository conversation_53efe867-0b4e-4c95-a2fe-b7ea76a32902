<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class SellTicketsFilterDTO implements DTOInterface
{
    public function __construct(
        public array $categories = [],
        public array $clubs = [],
        public array $countries = [],
        public array $leagues = [],
        public array $stadiums = [],
        public ?string $search = '',
        public ?string $sort = '',
        public ?string $date_from = '',
        public ?string $date_to = '',
    ) {}

    public function toArray(): array
    {
        return [
            'categories' => $this->categories,
            'clubs' => $this->clubs,
            'countries' => $this->countries,
            'leagues' => $this->leagues,
            'stadiums' => $this->stadiums,
            'search' => $this->search,
            'sort' => $this->sort,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
        ];
    }
}
