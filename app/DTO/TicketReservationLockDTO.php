<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class TicketReservationLockDTO implements DTOInterface
{
    public function __construct(
        public int $ticketId,
        public int $quantity,
        public string $price,
        public bool $canOverrideLock,
    ) {}

    public function toArray(): array
    {
        return [
            'ticket_id' => $this->ticketId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'can_override_lock' => $this->canOverrideLock,
        ];
    }
}
