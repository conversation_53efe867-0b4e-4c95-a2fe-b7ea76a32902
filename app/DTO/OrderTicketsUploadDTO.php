<?php

namespace App\DTO;

use App\Contracts\DTOInterface;
use Illuminate\Http\UploadedFile;

class OrderTicketsUploadDTO implements DTOInterface
{
    public function __construct(
        public int $orderId,
        /** @var UploadedFile[]|null */
        public ?array $tickets,
        public ?UploadedFile $additionalDoc,
    ) {}

    public function toArray(): array
    {
        return [
            'order_id' => $this->orderId,
        ];
    }
}
