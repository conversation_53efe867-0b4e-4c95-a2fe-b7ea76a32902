<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class WithdrawalFilterDTO implements DTOInterface
{
    public function __construct(
        public ?string $search = '',
        public ?string $dateFrom = '',
        public ?string $dateTo = '',
        public ?string $status = '',
        public ?string $sort = '',
    ) {}

    public function toArray(): array
    {
        return [
            'search' => $this->search,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
            'status' => $this->status,
            'sort' => $this->sort,
        ];
    }
}
