<?php

namespace App\Traits;

use App\Events\SendMailEvent;
use App\Models\EmailTemplate;
use App\Models\GeneralSetting;
use App\Models\User;
use Illuminate\Support\Facades\Event;

trait SendEmailTrait
{
    /**
     * purpose Send email notification
     *
     * @param  $templateKey  string
     * @param  $meta  array
     * @param  $userIds  array
     * @param  $toEmails  array
     * @return bool true/false
     */
    public function sendEmails($templateKey, $meta, $userIds, $toEmails)
    {

        // get template
        $emailTemplate = EmailTemplate::where('template_key', $templateKey)->with('translations')->first();
        $templateData = collect($emailTemplate->translations)->firstWhere('locale', $meta['locale'])->toArray();
        if (! empty($userIds)) {
            $this->sendEmailToUsers($userIds, $templateData, $meta);
        }

        if (! empty($toEmails)) {
            $this->sendEmailDirectToEmailIds($toEmails, $templateData, $meta);
        }

        return false;
    }

    /**
     * purpose Send email notification to users
     *
     * @param  $userIds  array
     * @param  $templateData  array
     * @param  $meta  array
     * @return bool true/false
     */
    protected function sendEmailToUsers($userIds, $templateData, $meta)
    {
        $userIds = array_unique($userIds);

        foreach ($userIds as $id) {
            $user = User::select(['name', 'email'])
                ->where(['id' => $id])
                ->orWhere(['email' => $id])
                ->first();
            $toEmail = $user->email;
            $toUserName = $user->name;

            $search = $replace = [];

            if (! isset($meta['name'])) {
                $meta['name'] = $toUserName;
            }

            $mailDataArr = [$toEmail, $toUserName, $templateData];
            $mailData = $this->setMailObject($meta, $search, $replace, $mailDataArr);

            Event::dispatch(new SendMailEvent($mailData));
        }

        return true;
    }

    /**
     * purpose Send email notification to specific emails
     *
     * @param  $toEmails  array
     * @param  $templateData  array
     * @param  $meta  array
     * @return bool true/false
     */
    protected function sendEmailDirectToEmailIds($toEmails, $templateData, $meta)
    {
        $toEmails = array_unique($toEmails);

        foreach ($toEmails as $id) {
            $toEmail = $id;
            $toUserFullname = isset($meta['userName']) && ! empty($meta['userName']) ? $meta['userName'] : '';
            $search = $replace = [];

            $mailDataArr = [$toEmail, $toUserFullname, $templateData];
            $mailData = $this->setMailObject($meta, $search, $replace, $mailDataArr);
            Event::dispatch(new SendMailEvent($mailData));
        }

        return true;
    }

    /**
     * Set MailData Object
     *
     * @param  array  $meta  email template replacement tag detail
     * @param  string  $search  finding tag in email template
     * @param  string  $replace  replace tag in email template
     * @param  array  $mailDataArr  sending user email detail
     * @return array $mailData
     */
    private function setMailObject($meta, $search, $replace, $mailDataArr): array
    {
        $meta['siteName'] = config('app.name');
        $meta['siteUrl'] = config('app.url');
        $meta['logo'] = config('mail.logo');
        $meta['salesEmail'] = GeneralSetting::getValue('sales_email');
        $meta['contactUrl'] = config('app.url').'/contact';
        $meta['copyRightYears'] = date('Y');

        foreach ($meta as $k => $v) {
            array_push($search, "{{{$k}}}");
            array_push($replace, $v);
        }

        $mailData['fromName'] = $meta['fromName'] ?? config('mail.from.name');
        $mailData['fromEmail'] = $meta['fromEmail'] ?? config('mail.from.address');
        $mailData['toEmail'] = $mailDataArr[0];
        $mailData['toName'] = $mailDataArr[1];
        $mailData['emailSubject'] =
            nl2br(str_replace($search, $replace, $mailDataArr[2]['subject']));

        if (isset($meta['subject'])) {
            $mailData['emailSubject'] = $meta['subject'];
        }
        $mailData['emailContent'] =
            str_replace($search, $replace, $mailDataArr[2]['body']);

        if (isset($meta['attachments'])) {
            $mailData['attachments'] = $meta['attachments'];
        }

        return $mailData;
    }
}
