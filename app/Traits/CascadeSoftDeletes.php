<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

trait CascadeSoftDeletes
{
    protected static function bootCascadeSoftDeletes()
    {
        static::deleting(function (Model $model) {
            if (! method_exists($model, 'isForceDeleting') || ! $model->isForceDeleting()) {
                $model->cascadeSoftDelete();
            }
        });

        static::restoring(function (Model $model) {
            $model->cascadeRestore();
        });
    }

    protected function cascadeSoftDelete()
    {
        if (! property_exists($this, 'cascadeDeletes') || empty($this->cascadeDeletes)) {
            return;
        }

        foreach ($this->cascadeDeletes as $relationship) {
            $relatedRecords = $this->{$relationship}()->withTrashed()->get();

            foreach ($relatedRecords as $record) {
                // Only delete if the record uses SoftDeletes trait
                if (in_array(SoftDeletes::class, class_uses_recursive($record))) {
                    $record->delete();
                }
            }
        }
    }

    protected function cascadeRestore()
    {
        if (! property_exists($this, 'cascadeDeletes') || empty($this->cascadeDeletes)) {
            return;
        }

        foreach ($this->cascadeDeletes as $relationship) {
            $this->{$relationship}()->onlyTrashed()->get()->each->restore();
        }
    }
}
