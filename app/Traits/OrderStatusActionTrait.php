<?php

namespace App\Traits;

use App\Enums\OrderStatus;

trait OrderStatusActionTrait
{
    protected function getOrderStatusActionList($orderStatus)
    {
        $statuses = [
            OrderStatus::PENDING->value => [],
            OrderStatus::PROCESSING->value => OrderStatus::getOptionsWithKeyValuePair([
                OrderStatus::CONFIRMED->value,
                OrderStatus::UNDER_REVIEW->value,
            ]),
            OrderStatus::CONFIRMED->value => OrderStatus::getOptionsWithKeyValuePair([
                OrderStatus::UNDER_REVIEW->value,
                OrderStatus::SHIPPED->value,
            ]),
            OrderStatus::UNDER_REVIEW->value => OrderStatus::getOptionsWithKeyValuePair([
                OrderStatus::CONFIRMED->value,
            ]),
            OrderStatus::SHIPPED->value => OrderStatus::getOptionsWithKeyValuePair([
                OrderStatus::ON_DISPUTE->value,
            ]),
            OrderStatus::ON_DISPUTE->value => [],
            OrderStatus::COMPLETED->value => [],
            OrderStatus::CANCELED->value => [],
        ];

        return $statuses[$orderStatus] ?? [];
    }
}
