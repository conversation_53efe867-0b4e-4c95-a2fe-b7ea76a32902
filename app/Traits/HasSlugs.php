<?php

namespace App\Traits;

use App\Models\Slug;
use Illuminate\Support\Str;

trait HasSlugs
{
    protected static function booted()
    {
        static::addGlobalScope('withLocalizedSlug', function ($query) {
            $query->with('localizedSlug:sluggable_id,slug,locale');
        });
    }

    public function slugs()
    {
        return $this->morphMany(Slug::class, 'sluggable');
    }

    public function localizedSlug()
    {
        return $this->morphOne(Slug::class, 'sluggable')->where('locale', app()->getLocale());
    }

    public function setSlugs(array $slugs)
    {
        foreach ($slugs as $locale => $slug) {
            Slug::updateOrCreate(
                [
                    'sluggable_type' => static::class,
                    'sluggable_id' => $this->id,
                    'locale' => $locale,
                ],
                [
                    'slug' => Str::slug($slug),
                ]
            );
        }
    }

    public function scopeWhereSlug($query, string $slug)
    {
        return $query->whereHas('localizedSlug', function ($q) use ($slug) {
            $q->where('slug', $slug)->where('locale', app()->getLocale());
        });
    }
}
