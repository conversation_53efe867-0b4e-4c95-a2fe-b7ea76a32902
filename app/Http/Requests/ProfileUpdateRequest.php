<?php

namespace App\Http\Requests;

use App\DTO\UserDTO;
use App\Enums\GenderType;
use App\Enums\UserType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'user_name' => [
                'required',
                'string',
                'lowercase',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
            'surname' => 'required|string|max:50',
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{9,15}$/'],
            'gender' => ['required', Rule::in(GenderType::getValues())],
            'address' => 'required|string|max:100',
            'city' => 'required|string|max:100',
            'country_id' => 'required',
            'zip' => 'required|string|max:10',
            'company' => [
                'max:100',
                Rule::requiredIf($this->user()->user_type === UserType::BROKER),
            ],
            'government_id' => [
                'max:100',
                Rule::requiredIf($this->user()->user_type === UserType::BROKER),
            ],
        ];
    }

    public function toDTO(): UserDTO
    {
        return new UserDTO(...$this->validated());
    }
}
