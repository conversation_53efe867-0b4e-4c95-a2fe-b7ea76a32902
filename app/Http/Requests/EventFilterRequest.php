<?php

namespace App\Http\Requests;

use App\DTO\EventFilterDTO;
use App\Enums\EventCategoryType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EventFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'categories' => ['nullable', 'array', Rule::in(EventCategoryType::getValues())],
            'countries' => ['nullable', 'array', 'exists:countries,id'],
            'clubs' => ['nullable', 'array', 'exists:clubs,id'],
            'leagues' => ['nullable', 'array', 'exists:leagues,id'],
            'stadiums' => ['nullable', 'array', 'exists:stadiums,id'],
            'search' => ['nullable', 'string', 'max:255'],
            'sort' => ['nullable', 'string'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
            'price_from' => ['nullable', 'numeric', 'min:0'],
            'price_to' => ['nullable', 'numeric', 'min:0', 'gte:price_from'],
        ];
    }

    /**
     * Convert the validated request data to DTO.
     */
    public function toDTO(): EventFilterDTO
    {
        return new EventFilterDTO(
            categories: $this->input('categories', []),
            countries: $this->input('countries', []),
            clubs: $this->input('clubs', []),
            leagues: $this->input('leagues', []),
            stadiums: $this->input('stadiums', []),
            search: $this->input('search', ''),
            sort: $this->input('sort', ''),
            date_from: $this->input('date_from', ''),
            date_to: $this->input('date_to', ''),
            price_from: $this->input('price_from', ''),
            price_to: $this->input('price_to', ''),
        );
    }
}
