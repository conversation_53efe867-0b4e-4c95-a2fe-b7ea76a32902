<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\SupportTicketFilterDTO;
use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestStatus;
use App\Enums\SupportRequestType;
use Illuminate\Validation\Rule;

class SupportTicketRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'requestType' => ['nullable', 'array', Rule::in(SupportRequestType::getValues())],
            'status' => ['nullable', 'array', Rule::in(SupportRequestStatus::getValues())],
            'priority' => ['nullable', 'array', Rule::in(SupportRequestPriority::getValues())],
        ];
    }

    public function toDTO(): SupportTicketFilterDTO
    {
        return new SupportTicketFilterDTO(
            requestType: $this->input('requestType') ?? [],
            status: $this->input('status') ?? [],
            priority: $this->input('priority') ?? [],
        );
    }
}
