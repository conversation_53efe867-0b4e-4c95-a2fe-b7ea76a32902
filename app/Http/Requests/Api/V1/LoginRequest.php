<?php

namespace App\Http\Requests\Api\V1;

use App\Helpers\ApiResponse;
use App\Models\User;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class LoginRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'password' => 'required',
            'device_name' => 'required',
        ];
    }

    public function authenticate(): User
    {
        $data = $this->validated();
        $credentials = [
            'email' => $data['email'],
            'password' => $data['password'],
        ];

        if (! Auth::attempt($credentials)) {
            throw new HttpResponseException(
                ApiResponse::validationError([
                    'email' => ['Invalid credentials'],
                ])
            );
        }

        return User::findOrFail(Auth::id());
    }
}
