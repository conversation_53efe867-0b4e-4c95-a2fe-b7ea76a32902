<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\TicketStoreDTO;
use App\Enums\TicketQuantitySplitType;
use App\Enums\TicketType;
use App\Rules\ValidateTicketCreateQuantity;
use Illuminate\Validation\Rule;

class TicketStoreRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'event_id' => ['required', 'exists:events,id'],
            'quantity' => ['required', 'numeric', 'min:1', 'max:'.config('services.ticketgol.max_quantity_per_ticket'), new ValidateTicketCreateQuantity],
            'price' => ['required', 'decimal:2', 'min:0', 'max:'.config('services.ticketgol.max_price_limit')],
            'face_value_price' => ['required', 'decimal:2', 'min:0', 'max:'.config('services.ticketgol.max_price_limit')],
            'sector_id' => ['required', 'exists:stadium_sectors,id'],
            'ticket_rows' => ['nullable', 'max:100'],
            'ticket_seats' => ['nullable', 'max:200'],
            'description' => ['required', 'max:255'],
            'currency_code' => ['required'],
            'ticket_type' => ['required', Rule::in(TicketType::getValues())],
            'quantity_split_type' => ['required', Rule::in(TicketQuantitySplitType::getValues())],
            'sell_in_multiples' => [
                'nullable',
                'required_if:quantity_split_type,'.TicketQuantitySplitType::IN_MULTIPLE->value,
                'numeric',
                'min:1',
                'max:'.$this->input('quantity'),
                function ($attribute, $value, $fail) {
                    $quantity = $this->input('quantity');
                    if ($this->input('quantity_split_type') === TicketQuantitySplitType::IN_MULTIPLE->value && $quantity % $value !== 0) {
                        $fail(__('validation.custom.sell_in_multiples'));
                    }
                },
            ],
            'restrictions' => ['sometimes', 'array'],
        ];
    }

    public function toDTO(): TicketStoreDTO
    {
        return new TicketStoreDTO(
            eventId: $this->validated('event_id'),
            quantity: $this->validated('quantity'),
            price: $this->validated('price'),
            faceValuePrice: $this->validated('face_value_price'),
            sectorId: $this->validated('sector_id'),
            ticketRows: $this->validated('ticket_rows'),
            ticketSeats: $this->validated('ticket_seats'),
            description: $this->validated('description'),
            currencyCode: $this->validated('currency_code'),
            ticketType: $this->validated('ticket_type'),
            quantitySplitType: $this->validated('quantity_split_type'),
            sellInMultiples: $this->validated('sell_in_multiples'),
            restrictions: $this->validated('restrictions'),
        );
    }
}
