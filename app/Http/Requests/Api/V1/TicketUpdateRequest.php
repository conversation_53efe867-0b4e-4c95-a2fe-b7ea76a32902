<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\TicketUpdateDTO;
use App\Enums\TicketQuantitySplitType;
use App\Enums\TicketType;
use App\Rules\ValidateTicketUpdateQuantity;
use Illuminate\Validation\Rule;

class TicketUpdateRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'ticket_id' => ['required', 'exists:tickets,id'],
            'quantity' => ['required', 'numeric', 'min:1', 'max:'.config('services.ticketgol.max_quantity_per_ticket'), new ValidateTicketUpdateQuantity($this->ticket_id)],
            'price' => ['required', 'decimal:2', 'min:0', 'max:'.config('services.ticketgol.max_price_limit')],
            'face_value_price' => ['required', 'decimal:2', 'min:0', 'max:'.config('services.ticketgol.max_price_limit')],
            'ticket_rows' => ['nullable', 'max:100'],
            'ticket_seats' => ['nullable', 'max:200'],
            'description' => ['required', 'max:255'],
            'currency_code' => ['required'],
            'ticket_type' => ['required', Rule::in(TicketType::getValues())],
            'quantity_split_type' => ['required', Rule::in(TicketQuantitySplitType::getValues())],
            'sell_in_multiples' => [
                'nullable',
                'required_if:quantity_split_type,'.TicketQuantitySplitType::IN_MULTIPLE->value,
                'numeric',
                'min:1',
                'max:'.$this->input('quantity'),
                function ($attribute, $value, $fail) {
                    $quantity = $this->input('quantity');
                    if ($this->input('quantity_split_type') === TicketQuantitySplitType::IN_MULTIPLE->value && $quantity % $value !== 0) {
                        $fail(__('validation.custom.sell_in_multiples'));
                    }
                },
            ],
            'restrictions' => ['sometimes', 'array'],
        ];
    }

    public function toDTO(): TicketUpdateDTO
    {
        return new TicketUpdateDTO(
            ticketId: $this->validated('ticket_id'),
            quantity: $this->validated('quantity'),
            price: $this->validated('price'),
            faceValuePrice: $this->validated('face_value_price'),
            ticketRows: $this->validated('ticket_rows'),
            ticketSeats: $this->validated('ticket_seats'),
            description: $this->validated('description'),
            currencyCode: $this->validated('currency_code'),
            ticketType: $this->validated('ticket_type'),
            quantitySplitType: $this->validated('quantity_split_type'),
            sellInMultiples: $this->validated('sell_in_multiples'),
            restrictions: $this->validated('restrictions'),
        );
    }
}
