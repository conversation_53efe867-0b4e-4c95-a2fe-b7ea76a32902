<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\EventTicketFilterDTO;
use App\Enums\TicketType;
use Illuminate\Validation\Rule;

class EventTicketFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'eventId' => ['required', 'exists:events,id'],
            'priceRange' => ['present', 'array'],
            'sector' => ['present', 'nullable', 'exists:stadium_sectors,id'],
            'quantity' => ['present', 'nullable'],
            'ticketType' => ['present', 'nullable', Rule::in(TicketType::getValues())],
            'sort' => ['present', 'nullable'],
        ];
    }

    public function toDTO(): EventTicketFilterDTO
    {
        return new EventTicketFilterDTO(
            eventId: $this->validated('eventId'),
            priceRange: $this->validated('priceRange'),
            sector: $this->validated('sector'),
            quantity: $this->validated('quantity'),
            ticketType: $this->validated('ticketType'),
            sort: $this->validated('sort'),
        );
    }
}
