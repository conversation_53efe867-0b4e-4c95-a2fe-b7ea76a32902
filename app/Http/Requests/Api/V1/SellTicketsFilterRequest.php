<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\SellTicketsFilterDTO;

class SellTicketsFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['sometimes', 'nullable'],
            'sort' => ['sometimes', 'nullable'],
        ];
    }

    public function toDTO(): SellTicketsFilterDTO
    {
        return new SellTicketsFilterDTO(
            search: $this->validated('search', ''),
            sort: $this->validated('sort', ''),
        );
    }
}
