<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\ClubFilterDTO;

class ClubFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'stadiums' => ['nullable', 'array', 'exists:stadiums,id'],
            'countries' => ['nullable', 'array', 'exists:countries,id'],
            'search' => ['nullable', 'string', 'max:255'],
            'sort' => ['nullable', 'string'],
        ];
    }

    public function toDTO(): ClubFilterDTO
    {
        return new ClubFilterDTO(
            stadiums: $this->input('stadiums', []),
            countries: $this->input('countries', []),
            search: $this->input('search', ''),
            sort: $this->input('sort', ''),
        );
    }
}
