<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\OrderStatusCheckDTO;

class OrderStatusCheckRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_id' => ['required'],
            'check_payment_status' => ['sometimes', 'boolean'],
        ];
    }

    public function toDTO(): OrderStatusCheckDTO
    {
        return new OrderStatusCheckDTO(
            orderId: $this->validated('order_id'),
            checkPaymentStatus: $this->validated('check_payment_status', false),
        );
    }
}
