<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\WithdrawalFilterDTO;
use App\Enums\WithdrawalStatus;
use Illuminate\Validation\Rule;

class WithdrawalFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string'],
            'date_from' => ['nullable', 'date_format:Y-m-d'],
            'date_to' => ['nullable', 'date_format:Y-m-d', 'after_or_equal:date_from'],
            'status' => ['nullable', Rule::in(WithdrawalStatus::getValues())],
            'sort' => ['nullable', 'string'],
        ];
    }

    public function toDTO(): WithdrawalFilterDTO
    {
        return new WithdrawalFilterDTO(
            search: $this->input('search'),
            dateFrom: $this->input('date_from'),
            dateTo: $this->input('date_to'),
            status: $this->input('status'),
            sort: $this->input('sort'),
        );
    }
}
