<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\TicketReservationLockDTO;
use App\Rules\ValidateTicketQuantity;

class TicketReservationLockRequest extends BaseApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'ticket_id' => ['required', 'exists:tickets,id'],
            'quantity' => ['required', 'integer', 'min:1', new ValidateTicketQuantity($this->input('ticket_id'))],
            'price' => ['required'],
            'override_lock' => ['sometimes', 'boolean', 'nullable'],
        ];
    }

    public function toDTO(): TicketReservationLockDTO
    {
        return new TicketReservationLockDTO(
            ticketId: $this->validated('ticket_id'),
            quantity: $this->validated('quantity'),
            price: $this->validated('price'),
            canOverrideLock: $this->validated('override_lock', true),  // if override_lock is not provided, default to true
        );
    }
}
