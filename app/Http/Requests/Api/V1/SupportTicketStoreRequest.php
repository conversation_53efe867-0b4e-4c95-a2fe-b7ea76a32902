<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\SupportTicketStoreDTO;
use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestType;
use Illuminate\Validation\Rule;

class SupportTicketStoreRequest extends BaseApiRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'request_type' => ['required', 'string', Rule::in(SupportRequestType::getValues())],
            'priority' => ['required', 'string', Rule::in(SupportRequestPriority::getValues())],
            'files' => 'nullable|array|max:1',
            'files.*' => 'mimes:png,jpg,jpeg,webp,pdf,docx,doc,xlsx,xls,csv|max:5120',
        ];
    }

    public function toDTO(): SupportTicketStoreDTO
    {
        return new SupportTicketStoreDTO(
            $this->subject,
            $this->message,
            $this->request_type,
            $this->priority,
            $this->file('files') ?? [],
        );
    }
}
