<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\OrderStatusUpdateDTO;
use App\Enums\OrderStatus;
use App\Rules\ValidateOrderStatusUpdate;

class OrderStatusUpdateRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_id' => ['required', 'exists:orders,id'],
            'status' => ['required', new ValidateOrderStatusUpdate($this->order_id)],
            'reason' => [
                'nullable',
                'required_if:status,'.OrderStatus::CANCELED->value.','.OrderStatus::UNDER_REVIEW->value,
                'max:400',
            ],
            'tickets' => [
                'nullable',
                'required_if:status,'.OrderStatus::SHIPPED->value,
                'array',
            ],
            'tickets.*' => [
                'file',
                'mimes:jpg,jpeg,png,pdf',
                'max:5120',
            ],
            'additionalDoc' => [
                'nullable',
                'mimes:jpg,jpeg,png,pdf',
                'max:5120',
            ],
        ];
    }

    public function toDTO(): OrderStatusUpdateDTO
    {
        $userId = auth()->user()->id;

        return new OrderStatusUpdateDTO(
            orderId: $this->validated('order_id'),
            userId: $userId,
            status: $this->validated('status'),
            reason: $this->validated('reason'),
            tickets: $this->validated('tickets'),
            additionalDoc: $this->validated('additionalDoc'),
        );
    }
}
