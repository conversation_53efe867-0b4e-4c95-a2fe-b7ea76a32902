<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\WalletTransactionFilterDTO;
use App\Enums\WalletEntryType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use Illuminate\Validation\Rule;

class WalletTransactionFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string'],
            'date_from' => ['nullable', 'date_format:Y-m-d'],
            'date_to' => ['nullable', 'date_format:Y-m-d', 'after_or_equal:date_from'],
            'transaction_type' => ['nullable', Rule::in(WalletTransactionType::getValues())],
            'entry_type' => ['nullable', Rule::in(WalletEntryType::getValues())],
            'status' => ['nullable', Rule::in(WalletTransactionStatus::getValues())],
            'sort' => ['nullable', 'string'],
        ];
    }

    public function toDTO(): WalletTransactionFilterDTO
    {
        return new WalletTransactionFilterDTO(
            search: $this->input('search'),
            dateFrom: $this->input('date_from'),
            dateTo: $this->input('date_to'),
            transactionType: $this->input('transaction_type'),
            entryType: $this->input('entry_type'),
            status: $this->input('status'),
            sort: $this->input('sort'),
        );
    }
}
