<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\StadiumFilterDTO;

class StadiumFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'countries' => ['sometimes', 'array', 'exists:countries,id'],
            'search' => ['sometimes', 'nullable'],
            'sort' => ['sometimes', 'nullable'],
        ];
    }

    public function toDTO(): StadiumFilterDTO
    {
        return new StadiumFilterDTO(
            countries: $this->validated('countries', []),
            search: $this->validated('search', ''),
            sort: $this->validated('sort', ''),
        );
    }
}
