<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\EventFilterDTO;
use App\Enums\EventCategoryType;
use Illuminate\Validation\Rule;

class EventFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'categories' => ['sometimes', 'nullable', 'array', Rule::in(EventCategoryType::getValues())],
            'countries' => ['sometimes', 'nullable', 'array', 'exists:countries,id'],
            'clubs' => ['sometimes', 'nullable', 'array', 'exists:clubs,id'],
            'leagues' => ['sometimes', 'nullable', 'array', 'exists:leagues,id'],
            'stadiums' => ['sometimes', 'nullable', 'array', 'exists:stadiums,id'],
            'search' => ['sometimes', 'nullable'],
            'sort' => ['sometimes', 'nullable'],
        ];
    }

    public function toDTO(): EventFilterDTO
    {
        return new EventFilterDTO(
            categories: $this->validated('categories', []),
            countries: $this->validated('countries', []),
            clubs: $this->validated('clubs', []),
            leagues: $this->validated('leagues', []),
            stadiums: $this->validated('stadiums', []),
            search: $this->validated('search', ''),
            sort: $this->validated('sort', ''),
        );
    }
}
