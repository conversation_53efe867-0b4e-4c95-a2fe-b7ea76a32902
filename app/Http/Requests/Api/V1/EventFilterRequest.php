<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\EventFilterDTO;
use App\Enums\EventCategoryType;
use Illuminate\Validation\Rule;

class EventFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'categories' => ['present', 'array', Rule::in(EventCategoryType::getValues())],
            'countries' => ['present', 'array', 'exists:countries,id'],
            'clubs' => ['present', 'array', 'exists:clubs,id'],
            'leagues' => ['present', 'array', 'exists:leagues,id'],
            'stadiums' => ['present', 'array', 'exists:stadiums,id'],
            'search' => ['present', 'nullable'],
            'sort' => ['present', 'nullable'],
        ];
    }

    public function toDTO(): EventFilterDTO
    {
        return new EventFilterDTO(
            categories: $this->validated('categories'),
            countries: $this->validated('countries'),
            clubs: $this->validated('clubs'),
            leagues: $this->validated('leagues'),
            stadiums: $this->validated('stadiums'),
            search: $this->validated('search'),
            sort: $this->validated('sort'),
        );
    }
}
