<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\OrderTicketsUploadDTO;

class OrderTicketsUploadRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_id' => ['required', 'exists:orders,id'],
            'tickets' => [
                'array',
            ],
            'tickets.*' => [
                'file',
                'mimes:jpg,jpeg,png,pdf',
                'max:5120',
            ],
            'additionalDoc' => [
                'nullable',
                'mimes:jpg,jpeg,png,pdf',
                'max:5120',
            ],
        ];
    }

    public function toDTO(): OrderTicketsUploadDTO
    {
        return new OrderTicketsUploadDTO(
            orderId: $this->validated('order_id'),
            tickets: $this->validated('tickets'),
            additionalDoc: $this->validated('additionalDoc'),
        );
    }
}
