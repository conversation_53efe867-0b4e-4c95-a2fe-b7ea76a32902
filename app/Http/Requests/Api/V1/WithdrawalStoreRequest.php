<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\WithdrawalStoreDTO;
use App\Rules\ValidateWithdrawalAmount;
use Illuminate\Support\Facades\Auth;

class WithdrawalStoreRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $minLimit = config('services.ticketgol.min_withdrawal_limit');
        $maxLimit = config('services.ticketgol.max_withdrawal_limit');

        return [
            'amount' => ['required', 'decimal:0,2', 'min:'.$minLimit, 'max:'.$maxLimit, new ValidateWithdrawalAmount],
            'previous_amount' => ['required'],
            'currency_code' => ['required'],
            'payout_method' => ['required', 'exists:user_payout_methods,id'],
        ];
    }

    public function toDTO(): WithdrawalStoreDTO
    {
        $userId = Auth::id();

        return new WithdrawalStoreDTO(
            userId: $userId,
            amount: $this->input('amount'),
            previousAmount: $this->input('previous_amount'),
            currencyCode: $this->input('currency_code'),
            payoutMethodId: $this->input('payout_method'),
        );
    }
}
