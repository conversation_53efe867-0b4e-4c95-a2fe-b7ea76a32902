<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\MyTicketFilterDTO;
use App\Enums\TicketType;
use Illuminate\Validation\Rule;

class MyTicketFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string'],
            'ticket_type' => ['nullable', Rule::in(TicketType::getValues())],
            'date_from' => ['nullable', 'date_format:Y-m-d'],
            'date_to' => ['nullable', 'date_format:Y-m-d', 'after_or_equal:date_from'],
            'sort' => ['nullable', 'string'],
        ];
    }

    public function toDTO(): MyTicketFilterDTO
    {
        return new MyTicketFilterDTO(
            search: $this->input('search'),
            ticketType: $this->input('ticket_type'),
            dateFrom: $this->input('date_from'),
            dateTo: $this->input('date_to'),
            sort: $this->input('sort'),
        );
    }
}
