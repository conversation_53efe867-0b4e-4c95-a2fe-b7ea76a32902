<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\PayoutMethodStoreDTO;
use App\Enums\PayoutBankAccountType;
use App\Enums\PayoutMethodType;
use App\Rules\ValidatePayoutMethodBankField;
use Illuminate\Validation\Rule;

class UserPayoutMethodStoreRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'payout_method_type' => ['required', Rule::in(PayoutMethodType::getValues())],
            'country_code' => ['required'],
            'bank_name' => ['nullable', 'required_if:payout_method_type,bank', 'max:100'],
            'branch_name' => ['nullable', 'max:100'],
            'account_type' => ['nullable', 'required_if:payout_method_type,bank', Rule::in(PayoutBankAccountType::getValues())],
            'account_holder_name' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'account_number' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'swift_code' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'iban' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'ifsc_code' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'routing_number' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'sort_code' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'bsb' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'bank_code' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'branch_code' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'institution_number' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
            'transit_number' => [new ValidatePayoutMethodBankField($this->country_code, $this->payout_method_type)],
        ];
    }

    public function toDTO(): PayoutMethodStoreDTO
    {
        return new PayoutMethodStoreDTO(
            payoutMethodType: $this->validated('payout_method_type'),
            countryCode: $this->validated('country_code'),
            bankName: $this->validated('bank_name'),
            branchName: $this->validated('branch_name'),
            accountType: $this->validated('account_type'),
            accountHolderName: $this->validated('account_holder_name'),
            accountNumber: $this->validated('account_number'),
            swiftCode: $this->validated('swift_code'),
            iban: $this->validated('iban'),
            ifscCode: $this->validated('ifsc_code'),
            routingNumber: $this->validated('routing_number'),
            sortCode: $this->validated('sort_code'),
            bsb: $this->validated('bsb'),
            bankCode: $this->validated('bank_code'),
            branchCode: $this->validated('branch_code'),
            institutionNumber: $this->validated('institution_number'),
            transitNumber: $this->validated('transit_number'),
        );
    }
}
