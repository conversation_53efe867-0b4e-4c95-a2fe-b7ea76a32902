<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\MySalesFilterDTO;

class MySalesFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string'],
            'status' => ['nullable', 'string'],
            'date_from' => ['nullable', 'date_format:Y-m-d'],
            'date_to' => ['nullable', 'date_format:Y-m-d', 'after_or_equal:date_from'],
            'sort' => ['nullable', 'string'],
        ];
    }

    public function toDTO(): MySalesFilterDTO
    {
        return new MySalesFilterDTO(
            search: $this->input('search'),
            status: $this->input('status'),
            dateFrom: $this->input('date_from'),
            dateTo: $this->input('date_to'),
            sort: $this->input('sort'),
        );
    }
}
