<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\OrderStoreDTO;
use App\Enums\GenderType;
use App\Repositories\TicketReservationRepository;
use App\Rules\ValidateEventMatchesTicketReservation;
use App\Rules\ValidateTicketReservationKey;
use Illuminate\Validation\Rule;

class OrderStoreRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'ticket_reservation_id' => ['required', 'string', new ValidateTicketReservationKey],
            'event_id' => ['required', 'exists:events,id', new ValidateEventMatchesTicketReservation],
            'currency_code' => ['required', 'string'],
            'attendees.*' => ['required', 'array'],
            'attendees.*.name' => ['required', 'string'],
            'attendees.*.email' => ['required', 'email'],
            'attendees.*.gender' => ['required', Rule::in(GenderType::getValues())],
            'attendees.*.dob' => ['required', 'date'],
        ];
    }

    public function toDTO(): OrderStoreDTO
    {
        $encrpytedReservationId = $this->validated('ticket_reservation_id');
        $ticketReservationId = decrypt($encrpytedReservationId);
        $ticketReservation = app(TicketReservationRepository::class)->findById($ticketReservationId);

        $totalPrice = round($ticketReservation->price * $ticketReservation->quantity, 2);
        $serviceChargeAmount = round($totalPrice * config('services.ticketgol.service_charge_rate'), 2);
        $taxAmount = round($serviceChargeAmount * config('services.ticketgol.tax_rate'), 2);
        $grandTotal = round($totalPrice + $serviceChargeAmount + $taxAmount, 2);

        return new OrderStoreDTO(
            eventId: $this->validated('event_id'),
            encryptedReservationId: $encrpytedReservationId,
            tempTicketReservationId: $ticketReservationId,
            ticketId: $ticketReservation->ticket_id,
            sellerId: $ticketReservation->ticket->seller_id,
            quantity: $ticketReservation->quantity,
            price: $ticketReservation->price,
            totalPrice: $totalPrice,
            serviceChargeAmount: $serviceChargeAmount,
            taxAmount: $taxAmount,
            grandTotal: $grandTotal,
            currencyCode: $this->validated('currency_code'),
            attendees: $this->validated('attendees')
        );
    }
}
