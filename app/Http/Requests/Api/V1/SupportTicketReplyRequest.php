<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\SupportRequestReplyDTO;

class SupportTicketReplyRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'message' => ['required', 'string'],
            'files' => 'nullable|array|max:1',
            'files.*' => 'mimes:png,jpg,jpeg,webp,pdf,docx,doc,xlsx,xls,csv|max:2048',
        ];
    }

    public function toDTO()
    {
        return new SupportRequestReplyDTO(
            message: $this->validated('message'),
            files: $this->validated('files'),
        );
    }
}
