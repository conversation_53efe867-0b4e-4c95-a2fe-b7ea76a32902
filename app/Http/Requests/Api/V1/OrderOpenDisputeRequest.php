<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\OrderOpenDisputeDTO;
use App\Rules\ValidateOpenDisputeUpdate;

class OrderOpenDisputeRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'order_id' => ['required', 'exists:orders,id'],
            'status' => ['required', new ValidateOpenDisputeUpdate($this->order_id)],
            'reason' => ['required', 'max:400'],
        ];
    }

    public function toDTO(): OrderOpenDisputeDTO
    {
        $userId = auth()->user()->id;

        return new OrderOpenDisputeDTO(
            orderId: $this->validated('order_id'),
            userId: $userId,
            status: $this->validated('status'),
            reason: $this->validated('reason'),
        );
    }
}
