<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\LeagueFilterDTO;

class LeagueFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'seasons' => ['nullable', 'array', 'exists:seasons,id'],
            'countries' => ['nullable', 'array', 'exists:countries,id'],
            'search' => ['nullable', 'string', 'max:255'],
            'sort' => ['nullable', 'string'],
        ];
    }

    public function toDTO(): LeagueFilterDTO
    {
        return new LeagueFilterDTO(
            seasons: $this->input('seasons', []),
            countries: $this->input('countries', []),
            search: $this->input('search', ''),
            sort: $this->input('sort', ''),
        );
    }
}
