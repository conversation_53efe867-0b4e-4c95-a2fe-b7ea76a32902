<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\SearchFilterDTO;

class SearchFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'q' => ['required'],
            'page' => ['sometimes'],
        ];
    }

    public function toDTO(): SearchFilterDTO
    {
        return new SearchFilterDTO(
            search: $this->validated('q'),
            page: $this->validated('page'),
        );
    }
}
