<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\CheckoutDTO;
use App\Repositories\TicketReservationRepository;
use App\Rules\ValidateEventMatchesTicketReservation;
use App\Rules\ValidateTicketReservationKey;

class CheckoutSessionRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'success_url' => ['required', 'url'],
            'cancel_url' => ['required', 'url'],
            'ticket_reservation_id' => ['required', 'string', new ValidateTicketReservationKey],
            'event_id' => ['required', 'exists:events,id', new ValidateEventMatchesTicketReservation],
            'currency_code' => ['required', 'string'],
            'attendees.*' => ['required', 'array'],
            'attendees.*.name' => ['required', 'string'],
            'attendees.*.email' => ['required', 'email'],
            'attendees.*.gender' => ['required', 'string'],
            'attendees.*.dob' => ['required', 'date'],
        ];
    }

    public function toDTO(): CheckoutDTO
    {
        $encrpytedReservationId = $this->validated('ticket_reservation_id');
        $ticketReservationId = decrypt($encrpytedReservationId);
        $ticketReservation = app(TicketReservationRepository::class)->findById($ticketReservationId);

        return new CheckoutDTO(
            encryptedReservationId: $encrpytedReservationId,
            tempTicketReservationId: $ticketReservationId,
            successUrl: $this->validated('success_url'),
            cancelUrl: $this->validated('cancel_url'),
            eventId: $this->validated('event_id'),
            ticketId: $ticketReservation->ticket_id,
            quantity: $ticketReservation->quantity,
            currencyCode: $this->validated('currency_code'),
            attendees: $this->validated('attendees')
        );
    }
}
