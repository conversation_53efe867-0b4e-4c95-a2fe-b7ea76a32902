<?php

namespace App\Http\Requests\Api\V1;

use App\DTO\DashboardFilterDTO;

class DashboardFilterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'period' => ['present', 'nullable'],
        ];
    }

    public function toDTO(): DashboardFilterDTO
    {
        return new DashboardFilterDTO(
            period: $this->validated('period'),
        );
    }
}
