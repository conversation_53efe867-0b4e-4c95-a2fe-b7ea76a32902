<?php

namespace App\Http\Requests\Auth;

use App\DTO\UserDTO;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'user_name' => 'required|string|lowercase|max:50|unique:'.User::class,
            'email' => 'required|string|lowercase|email|max:100|unique:'.User::class,
            'password' => ['required', 'confirmed', Password::min(8)->mixedCase()->numbers()->symbols()],
            'surname' => 'required|string|max:50',
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{9,15}$/'],
            'gender' => 'required',
            'address' => 'required|string|max:100',
            'city' => 'required|string|max:100',
            'country_id' => 'required',
            'zip' => 'required|string|max:10',
        ];
    }

    public function toDTO(): UserDTO
    {
        return new UserDTO(...$this->validated());
    }
}
