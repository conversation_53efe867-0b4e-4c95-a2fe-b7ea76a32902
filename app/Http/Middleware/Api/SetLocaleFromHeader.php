<?php

namespace App\Http\Middleware\Api;

use App\Enums\LanguageCode;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class SetLocaleFromHeader
{
    public function handle(Request $request, Closure $next)
    {
        $locale = $request->header('X-Locale') ?? LanguageCode::ENGLISH->value;

        $supportedLanguages = array_column(LanguageCode::cases(), 'value');

        if ($locale && in_array($locale, $supportedLanguages)) {
            App::setLocale($locale);
        }

        return $next($request);
    }
}
