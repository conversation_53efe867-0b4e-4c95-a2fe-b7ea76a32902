<?php

namespace App\Http\Middleware;

use App\Services\SlugService;
use Closure;
use Inertia\Inertia;

class VerifySlugMiddleware
{
    protected $slugService;

    public function __construct(SlugService $slugService)
    {
        $this->slugService = $slugService;
    }

    public function handle($request, Closure $next)
    {
        $slug = $request->route('slug');

        $slugRecord = $this->slugService->getPageDetailBySlug($slug);

        if (! $slugRecord) {
            return Inertia::render('Error', ['status' => 404]);
        }

        return $next($request);
    }
}
