<?php

namespace App\Http\Middleware;

use App\Enums\LanguageCode;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Symfony\Component\HttpFoundation\Response;

class InjectLocaleDataMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $selectedLocale = Cookie::get('selected_locale') ?? $this->detectLanguage($request) ?? config('app.locale') ?? 'en';

        // Set the application locale
        app()->setLocale($selectedLocale);

        // Create a cookie that's accessible via JavaScript
        $cookie = cookie()->make(
            'selected_locale',
            $selectedLocale,
            60 * 24 * 365,
            '/',
            null,
            false,
            false,
            false,
            'Lax'
        );

        return $next($request)->withCookie($cookie);
    }

    private function detectLanguage(Request $request): ?string
    {
        $languages = $request->getLanguages();
        $supportedLanguages = array_column(LanguageCode::cases(), 'value');

        foreach ($languages as $language) {
            $langCode = substr($language, 0, 2);
            if (in_array($langCode, $supportedLanguages)) {
                return $langCode;
            }
        }

        return null;
    }
}
