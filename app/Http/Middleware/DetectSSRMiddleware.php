<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class DetectSSRMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply SSR detection to non-authenticated routes
        if ($this->shouldApplySSRDetection($request)) {
            // Check if this is an SSR request by looking for Inertia SSR indicators
            if ($this->isSSRRequest($request)) {
                // Add X-SSR header to indicate this is an SSR request
                $request->headers->set('X-SSR', 'true');

                // Log SSR detection for debugging (only in non-production environments)
                if (config('app.debug')) {
                    Log::info('SSR Request Detected', [
                        'url' => $request->fullUrl(),
                        'user_agent' => $request->userAgent(),
                        'ip' => $request->ip(),
                        'headers' => $request->headers->all()
                    ]);
                }
            }
        }

        return $next($request);
    }

    /**
     * Determine if SSR detection should be applied to this request
     */
    private function shouldApplySSRDetection(Request $request): bool
    {
        // Don't apply SSR detection to authenticated routes
        if ($request->user()) {
            return false;
        }

        // Don't apply to API routes
        if ($request->is('api/*')) {
            return false;
        }

        // Don't apply to admin routes
        if ($request->is('admin.*')) {
            return false;
        }

        // Don't apply to debug/utility routes
        if ($request->is('_debugbar/*') || $request->is('telescope/*')) {
            return false;
        }

        // Apply to all other routes (public pages)
        return true;
    }

    /**
     * Determine if the current request is an SSR request
     */
    private function isSSRRequest(Request $request): bool
    {
        $userAgent = $request->userAgent() ?? '';

        // Primary check: Look for Node.js in user agent (most reliable for Inertia SSR)
        if (str_contains($userAgent, 'Node.js')) {
            return true;
        }

        // Check for specific SSR-related headers
        if ($request->hasHeader('X-Inertia-SSR') ||
            $request->hasHeader('X-SSR-Request')) {
            return true;
        }

        // Check for Inertia request without X-Requested-With header
        // SSR requests typically don't have X-Requested-With: XMLHttpRequest
        if ($request->hasHeader('X-Inertia') &&
            !$request->hasHeader('X-Requested-With')) {
            return true;
        }

        // Check for requests from SSR server (localhost with specific patterns)
        $clientIp = $request->ip();
        if (in_array($clientIp, ['127.0.0.1', '::1']) || $clientIp === 'localhost') {
            // Look for Node.js or missing typical browser headers
            if (str_contains($userAgent, 'Node.js') ||
                empty($userAgent) ||
                (!$request->hasHeader('Accept-Language') && $request->hasHeader('X-Inertia'))) {
                return true;
            }
        }

        // Check for server-side rendering specific user agents
        $ssrPatterns = [
            'InertiaJS',
            'SSR',
            'server-side',
            'headless',
            'puppeteer',
            'playwright'
        ];

        foreach ($ssrPatterns as $pattern) {
            if (str_contains(strtolower($userAgent), strtolower($pattern))) {
                return true;
            }
        }

        return false;
    }
}
