<?php

namespace App\Http\Resources\League;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LeagueDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->whenLoaded('media')?->first();

        return [
            'id' => $this->id,
            'name' => $this->translation->name,
            'locale' => $this->translation->locale,
            'description' => $this->translation->description,
            'meta_title' => $this->translation->meta_title,
            'meta_description' => $this->translation->meta_description,
            'meta_keywords' => $this->translation->meta_keywords,
            'image' => $media?->getUrl() ?? '',
            'image_alt' => $media?->custom_properties['alt'] ?? '',
            'country' => [
                'shortcode' => $this->country->shortcode,
                'name' => $this->country->translation->name,
            ],
        ];
    }
}
