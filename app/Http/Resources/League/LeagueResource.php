<?php

namespace App\Http\Resources\League;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LeagueResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->media->first();

        return [
            'id' => $this->id,
            'name' => $this->league_name,
            'image' => $media?->getUrl() ?? '',
            'image_alt' => $media?->custom_properties['alt'] ?? '',
            'slug' => $this->localizedSlug->slug,
        ];
    }
}
