<?php

namespace App\Http\Resources\Stadium;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StadiumResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->media->first();

        return [
            'id' => $this->id,
            'name' => $this->stadium_name,
            'address_line_1' => $this->address_line_1,
            'address_line_2' => $this->address_line_2,
            'postcode' => $this->postcode,
            'image' => $media?->getUrl() ?? '',
            'image_alt' => $media?->custom_properties['alt'] ?? '',
            'slug' => $this->localizedSlug->slug,
        ];
    }
}
