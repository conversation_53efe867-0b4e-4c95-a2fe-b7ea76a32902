<?php

namespace App\Http\Resources\Stadium;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StadiumDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->media->first();

        return [
            'id' => $this->id,
            'address_line_1' => $this->address_line_1,
            'address_line_2' => $this->address_line_2,
            'postcode' => $this->postcode,
            'name' => $this->translation->name,
            'locale' => $this->translation->locale,
            'description' => $this->translation->description,
            'meta_title' => $this->translation->meta_title,
            'meta_description' => $this->translation->meta_description,
            'meta_keywords' => $this->translation->meta_keywords,
            'image' => $media?->getUrl() ?? '',
            'image_alt' => $media?->custom_properties['alt'] ?? '',
            'country' => [
                'shortcode' => $this->country->shortcode,
                'name' => $this->country->translation->name,
            ],
        ];
    }
}
