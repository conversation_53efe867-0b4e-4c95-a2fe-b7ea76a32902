<?php

namespace App\Http\Resources\Event;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EventDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->media->first();

        return [
            'id' => $this->id,
            'name' => $this->translation?->name ?? null,
            'description' => $this->translation?->description ?? null,
            'meta_title' => $this->translation?->meta_title ?? null,
            'meta_description' => $this->translation?->meta_description ?? null,
            'meta_keywords' => $this->translation?->meta_keywords ?? null,
            'date' => $this->date,
            'time' => $this->time,
            'timezone' => $this->timezone,
            'category' => [
                'value' => $this->category,
                'label' => $this->category->getLabel(),
            ],
            'is_feature_event' => $this->is_feature_event,
            'min_price' => $this->min_price,
            'max_price' => $this->max_price,
            'max_quantity' => $this->max_quantity,
            'quantitySplitEnums' => $this->quantitySplitEnums,
            'tempReservationMinutes' => $this->tempReservationMinutes,
            'image' => $media?->getUrl() ?? '',
            'image_alt' => $media?->custom_properties['alt'] ?? '',
            'slug' => $this->localizedSlug->slug,
            'stadium' => [
                'address_line_1' => $this->stadium->address_line_1,
                'address_line_2' => $this->stadium->address_line_2,
                'postcode' => $this->stadium->postcode,
                'name' => $this->stadium->translation->name,
                'slug' => $this->stadium->localizedSlug->slug,
                'country' => [
                    'shortcode' => $this->stadium->country->shortcode,
                    'name' => $this->stadium->country->translation->name,
                ],
            ],
            'league' => [
                'name' => $this->league->translation->name,
                'slug' => $this->league->localizedSlug->slug,
            ],
            'home_club' => [
                'name' => $this->homeClub->translation->name,
                'slug' => $this->homeClub->localizedSlug->slug,
            ],
            'guest_club' => [
                'name' => $this->guestClub->translation->name,
                'slug' => $this->guestClub->localizedSlug->slug,
            ],
            'stadium_sectors' => $this->stadiumSectors
                ->sortBy('id')
                ->map(function ($sector) {
                    $parentName = $sector->parent?->name;

                    return [
                        'id' => $sector->id,
                        'name' => $parentName
                            ? "{$parentName} - {$sector->name}"
                            : $sector->name,
                    ];
                })
                ->pluck('name', 'id'),
            'restrictions' => $this->restrictions->pluck('translation.name', 'translation.restriction_id'),
        ];
    }
}
