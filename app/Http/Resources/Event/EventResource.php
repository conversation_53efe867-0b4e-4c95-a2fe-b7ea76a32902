<?php

namespace App\Http\Resources\Event;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->media->first();

        return [
            'id' => $this->id,
            'name' => $this->event_name,
            'date' => $this->date,
            'stadium_name' => $this->stadium_name,
            'league_name' => $this->league_name,
            'min_price' => $this->min_price,
            'image' => $media?->getUrl() ?? '',
            'image_alt' => $media?->custom_properties['alt'] ?? '',
            'slug' => $this->localizedSlug->slug,
            'stadium' => [
                'slug' => $this->stadium->localizedSlug->slug,
            ],
            'league' => [
                'slug' => $this->league->localizedSlug->slug,
            ],
        ];
    }
}
