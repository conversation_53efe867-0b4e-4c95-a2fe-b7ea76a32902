<?php

namespace App\Http\Resources\Ticket;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EventTicketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'price' => $this->price,
            'quantity' => $this->quantity,
            'ticket_type' => [
                'value' => $this->ticket_type,
                'label' => $this->ticket_type->getLabel(),
                'color' => $this->ticket_type->getBadgeColour(),
            ],
            'quantity_split_type' => $this->quantity_split_type,
            'sell_in_multiples' => $this->sell_in_multiples ?? 0,
            'sector_name' => $this->sector->parent ? $this->sector->parent->name.' - '.$this->sector->name : $this->sector->name,
            'remain_qty' => $this->remain_qty,
        ];
    }
}
