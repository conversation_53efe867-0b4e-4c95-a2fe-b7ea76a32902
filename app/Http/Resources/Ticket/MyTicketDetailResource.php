<?php

namespace App\Http\Resources\Ticket;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MyTicketDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'ticket_no' => $this->ticket_no,
            'price' => $this->price,
            'quantity' => $this->quantity,
            'currency_code' => $this->currency_code,
            'face_value_price' => $this->face_value_price,
            'quantity_split_type' => $this->quantity_split_type,
            'sell_in_multiples' => $this->sell_in_multiples,
            'ticket_rows' => $this->ticket_rows,
            'ticket_seats' => $this->ticket_seats,
            'description' => $this->translation->description,
            'restrictions' => $this->restrictions->pluck('translation.name', 'translation.restriction_id'),
            'selectedRestrictions' => $this->restrictions->pluck('id'),
            'configurations' => $this->configurations ?? [],
            'reservations_sum_quantity' => $this->reservations_sum_quantity,
            'ticket_type' => [
                'value' => $this->ticket_type,
                'label' => $this->ticket_type->getLabel(),
                'color' => $this->ticket_type->getBadgeColour(),
            ],
            'event' => [
                'name' => $this->event->translation->name,
            ],
            'sector' => [
                'name' => $this->sector->parent ? $this->sector->parent->name.' - '.$this->sector->name : $this->sector->name,
            ],
        ];
    }
}
