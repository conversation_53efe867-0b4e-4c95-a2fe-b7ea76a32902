<?php

namespace App\Http\Resources\Ticket;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MyTicketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $media = $this->event->media->first();

        return [
            'id' => $this->id,
            'ticket_no' => $this->ticket_no,
            'price' => $this->price,
            'quantity' => $this->quantity,
            'sold_quantity' => $this->sold_quantity,
            'ticket_type' => [
                'value' => $this->ticket_type,
                'label' => $this->ticket_type->getLabel(),
                'color' => $this->ticket_type->getBadgeColour(),
            ],
            'reservations_count' => $this->reservations_count,
            'orders_count' => $this->orders_count,
            'reservations_sum_quantity' => $this->reservations_sum_quantity,
            'created_at' => $this->created_at,
            'event' => [
                'name' => $this->event->translation->name,
                'date' => $this->event->date,
                'image' => $media?->getUrl() ?? '',
                'image_alt' => $media?->custom_properties['alt'] ?? '',
            ],
            'sector' => [
                'name' => $this->sector->parent ? $this->sector->parent->name.' - '.$this->sector->name : $this->sector->name,
            ],
        ];
    }
}
