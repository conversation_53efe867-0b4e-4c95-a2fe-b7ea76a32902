<?php

namespace App\Http\Resources\TicketReservation;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TicketReservationDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'ticket_id' => $this->ticket_id,
            'price' => $this->price,
            'quantity' => $this->quantity,
            'status' => $this->status,
            'expires_at' => $this->expires_at,
            'subTotal' => $this->subTotal,
            'grandTotal' => $this->grandTotal,
            'serviceCharge' => $this->serviceCharge,
            'taxRate' => $this->taxRate,
            'ticket' => [
                'currency_code' => $this->ticket->currency_code,
                'ticket_type' => [
                    'value' => $this->ticket->ticket_type,
                    'label' => $this->ticket->ticket_type->getLabel(),
                    'color' => $this->ticket->ticket_type->getBadgeColour(),
                ],
                'event_id' => $this->ticket->event_id,
                'event' => [
                    'id' => $this->ticket->event->id,
                    'date' => $this->ticket->event->date,
                    'time' => $this->ticket->event->time,
                    'timezone' => $this->ticket->event->timezone,
                    'name' => $this->ticket->event->translation->name,
                    'slug' => $this->ticket->event->localizedSlug->slug,
                    'stadium' => [
                        'address_line_1' => $this->ticket->event->stadium->address_line_1,
                        'address_line_2' => $this->ticket->event->stadium->address_line_2,
                        'postcode' => $this->ticket->event->stadium->postcode,
                        'name' => $this->ticket->event->stadium->translation->name,
                        'slug' => $this->ticket->event->stadium->localizedSlug->slug,
                        'country' => [
                            'shortcode' => $this->ticket->event->stadium->country->shortcode,
                            'name' => $this->ticket->event->stadium->country->translation->name,
                        ],
                    ],
                    'restrictions' => $this->ticket->event->restrictions->pluck('translation.name', 'translation.restriction_id'),
                ],
                'sector' => [
                    'name' => $this->ticket->sector->parent ? $this->ticket->sector->parent->name.' - '.$this->ticket->sector->name : $this->ticket->sector->name,
                ],
                'restrictions' => $this->ticket->restrictions->pluck('translation.name', 'translation.restriction_id'),
            ],
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
                'user_type' => $this->user->user_type,
                'user_detail' => [
                    'phone' => $this->user->userDetail->phone,
                    'address' => $this->user->userDetail->address,
                    'city' => $this->user->userDetail->city,
                    'zip' => $this->user->userDetail->zip,
                    'country' => $this->user->userDetail->country ? [
                        'shortcode' => $this->user->userDetail->country->shortcode,
                        'name' => $this->user->userDetail->country->translation->name,
                    ] : null,
                ],
            ],
            'order' => $this->order ? [
                'id' => $this->order->id,
                'encryptedOrderId' => $this->order->encryptedOrderId,
                'clientSecret' => $this->order->clientSecret,
                'attendees' => $this->order->attendees,
            ] : null,
        ];
    }
}
