<?php

namespace App\Http\Resources\UserWithdrawal;

use App\Traits\CommonTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserWithdrawalResource extends JsonResource
{
    use CommonTrait;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'withdraw_no' => $this->withdraw_no,
            'amount' => $this->amount,
            'payout_method' => [
                'bank_name' => $this->payoutMethod->bank_name,
                'account_number' => $this->maskAccountNumber($this->payoutMethod->account_number),
            ],
            'status' => [
                'value' => $this->status,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColor(),
            ],
            'note' => $this->note,
            'payment_reference_number' => $this->payment_reference_number,
            'approved_at' => $this->approved_at,
            'paid_at' => $this->paid_at,
            'created_at' => $this->created_at,
        ];
    }
}
