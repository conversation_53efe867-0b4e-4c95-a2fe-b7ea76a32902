<?php

namespace App\Http\Resources\UserWalletTransaction;

use App\Enums\OrderStatus;
use App\Enums\OrderTransactionType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserWalletTransactionDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $lang = app()->getLocale();

        $paymentTransaction = $this->order->transactions->firstWhere('transaction_type', OrderTransactionType::PURCHASE->value);

        $refundTransaction = $this->order->transactions->firstWhere('transaction_type', OrderTransactionType::REFUND->value);

        if ($this->order->status === OrderStatus::CANCELED && $refundTransaction) {
            $paymentTransaction = $refundTransaction;
        }

        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'transaction_no' => $this->transaction_no,
            'transaction_type' => [
                'value' => $this->transaction_type,
                'label' => $this->transaction_type->getLabel(),
                'color' => $this->transaction_type->getBadgeColor(),
            ],
            'entry_type' => [
                'value' => $this->entry_type,
                'label' => $this->entry_type->getLabel(),
                'color' => $this->entry_type->getBadgeColor(),
            ],
            'total_amount' => $this->total_amount,
            'withdrawn_amount' => $this->withdrawn_amount,
            'remained_amount' => $this->remained_amount,
            'note' => $this->note,
            'status' => [
                'value' => $this->status,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColor(),
            ],
            'order' => $this->order ? [
                'id' => $this->order->id,
                'order_no' => $this->order->order_no,
                'price' => $this->order->price,
                'quantity' => $this->order->quantity,
                'total_price' => $this->order->total_price,
                'service_charge_amount' => $this->order->service_charge_amount,
                'tax_amount' => $this->order->tax_amount,
                'grand_total' => $this->order->grand_total,
                'penalty_amount' => $this->order->seller_id === $this->order->penalty_user_id ? $this->order->penalty_amount : 0,
                'status' => [
                    'value' => $this->order->status->value,
                    'label' => $this->order->status->getLabel(),
                    'color' => $this->order->status->getBadgeColour(),
                ],
                'payment_status' => [
                    'value' => $paymentTransaction->status->value,
                    'label' => $paymentTransaction->status->getLabel(),
                    'color' => $paymentTransaction->status->getBadgeColour(),
                ],
                'purchase_date' => $this->order->purchase_date,
                'ticket' => [
                    'ticket_no' => $this->order->order_meta_data->ticket->ticket_no,
                ],

            ] : null,
            'created_at' => $this->created_at,
        ];
    }
}
