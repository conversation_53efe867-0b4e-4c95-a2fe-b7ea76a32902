<?php

namespace App\Http\Resources\Order;

use App\Enums\OrderStatus;
use App\Enums\OrderTransactionType;
use App\Traits\OrderStatusActionTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MySalesOrderDetailResource extends JsonResource
{
    use OrderStatusActionTrait;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $lang = app()->getLocale();

        $statusActions = $this->getOrderStatusActionList($this->status->value);

        $paymentTransaction = $this->transactions->firstWhere('transaction_type', OrderTransactionType::PURCHASE->value);

        $refundTransaction = $this->transactions->firstWhere('transaction_type', OrderTransactionType::REFUND->value);

        if ($this->status === OrderStatus::CANCELED && $refundTransaction) {
            $paymentTransaction = $refundTransaction;
        }

        if ($this->parent_id && isset($statusActions[OrderStatus::CANCELED->value])) {
            unset($statusActions[OrderStatus::CANCELED->value]);
        }

        $ticketsFiles = $this->media->where('collection_name', 'tickets')->values();
        $additionalDocFile = $this->media->where('collection_name', 'additional_doc')->values();

        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'price' => $this->price,
            'quantity' => $this->quantity,
            'total_price' => $this->total_price,
            'service_charge_amount' => $this->service_charge_amount,
            'tax_amount' => $this->tax_amount,
            'grand_total' => $this->grand_total,
            'penalty_amount' => $this->seller_id === $this->penalty_user_id ? $this->penalty_amount : 0,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'payment_status' => [
                'value' => $paymentTransaction->status->value,
                'label' => $paymentTransaction->status->getLabel(),
                'color' => $paymentTransaction->status->getBadgeColour(),
            ],
            'status_actions' => $statusActions,
            'latestStatusChange' => [
                'from_status' => $this->latestStatusChange?->from_status,
                'to_status' => $this->latestStatusChange?->to_status,
                'reason' => nl2br($this->latestStatusChange?->reason),
            ],
            'purchase_date' => $this->purchase_date,
            'ticket_downloaded_at' => $this->ticket_downloaded_at,
            'attendees' => $this->attendees,
            'created_at' => $this->created_at,
            'ticket_files' => $ticketsFiles ? $ticketsFiles->map(function ($file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name,
                    'file_name' => $file->file_name,
                    'mime_type' => $file->mime_type,
                    'original_url' => $file->original_url,
                ];
            }) : [],
            'additional_doc' => $additionalDocFile ? $additionalDocFile->map(function ($file) {
                return [
                    'id' => $file->id,
                    'name' => $file->name,
                    'file_name' => $file->file_name,
                    'mime_type' => $file->mime_type,
                    'original_url' => $file->original_url,
                ];
            }) : [],
            'buyer' => [
                'name' => $this->buyer->name,
                'email' => $this->buyer->email,
                'phone' => $this->buyer->userDetail->phone,
                'address' => $this->buyer->userDetail->address,
                'city' => $this->buyer->userDetail->city,
                'zip' => $this->buyer->userDetail->zip,
                'country' => $this->buyer->userDetail->country?->translation->name ?? '',
            ],
            'ticket' => [
                'ticket_no' => $this->order_meta_data->ticket->ticket_no,
                'face_value_price' => $this->order_meta_data->ticket->face_value_price,
                'ticket_type' => $this->order_meta_data->ticket->ticket_type,
                'ticket_rows' => $this->order_meta_data->ticket->ticket_rows,
                'ticket_seats' => $this->order_meta_data->ticket->ticket_seats,
                'description' => $this->order_meta_data->ticket->description->{$lang},
                'sector' => [
                    'name' => $this->order_meta_data->ticket->sector->name ?? null,
                ],
                'restrictions' => $this->order_meta_data->ticket->restrictions ? collect($this->order_meta_data->ticket->restrictions)->map(function ($restriction) use ($lang) {
                    return [
                        'id' => $restriction->id,
                        'type' => $restriction->type,
                        'name' => $restriction->name->{$lang},
                    ];
                }) : [],
            ],
            'event' => [
                'name' => $this->order_meta_data->event->name->{$lang},
                'category' => $this->order_meta_data->event->category,
                'date' => $this->ticket->event->date,
                'time' => $this->ticket->event->time,
                'timezone' => $this->ticket->event->timezone,
                'image' => $this->order_meta_data->event->image,
                'image_alt' => $this->order_meta_data->event->image_alt,
                'stadium' => [
                    'name' => $this->order_meta_data->event->stadium->name->{$lang},
                    'address_line_1' => $this->order_meta_data->event->stadium->address_line_1,
                    'address_line_2' => $this->order_meta_data->event->stadium->address_line_2,
                    'postcode' => $this->order_meta_data->event->stadium->postcode,
                    'country' => $this->order_meta_data->event->stadium->country->{$lang},
                ],
                'league' => [
                    'name' => $this->order_meta_data->event->league->name->{$lang},
                ],
                'home_club' => [
                    'name' => $this->order_meta_data->event->home_club->name->{$lang},
                ],
                'guest_club' => [
                    'name' => $this->order_meta_data->event->guest_club->name->{$lang},
                ],
                'restrictions' => $this->order_meta_data->event->restrictions ? collect($this->order_meta_data->event->restrictions)->map(function ($restriction) use ($lang) {
                    return [
                        'id' => $restriction->id,
                        'type' => $restriction->type,
                        'name' => $restriction->name->{$lang},
                    ];
                }) : [],
            ],
        ];
    }
}
