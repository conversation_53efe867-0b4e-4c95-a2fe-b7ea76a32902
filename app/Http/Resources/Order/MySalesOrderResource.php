<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MySalesOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $lang = app()->getLocale();

        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'price' => $this->price,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'buyer' => [
                'name' => $this->buyer->name,
            ],
            'ticket' => [
                'ticket_no' => $this->order_meta_data->ticket->ticket_no,
            ],
            'event' => [
                'name' => $this->order_meta_data->event->name->{$lang},
                'date' => $this->ticket->event->date,
                'image' => $this->order_meta_data->event->image,
                'image_alt' => $this->order_meta_data->event->image_alt,
                'stadium' => [
                    'name' => $this->order_meta_data->event->stadium->name->{$lang},
                ],
            ],
            'purchase_date' => $this->purchase_date,
            'created_at' => $this->created_at,
        ];
    }
}
