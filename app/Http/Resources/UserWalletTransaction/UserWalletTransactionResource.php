<?php

namespace App\Http\Resources\UserWalletTransaction;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserWalletTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'transaction_no' => $this->transaction_no,
            'transaction_type' => [
                'value' => $this->transaction_type,
                'label' => $this->transaction_type->getLabel(),
                'color' => $this->transaction_type->getBadgeColor(),
            ],
            'entry_type' => [
                'value' => $this->entry_type,
                'label' => $this->entry_type->getLabel(),
                'color' => $this->entry_type->getBadgeColor(),
            ],
            'total_amount' => $this->total_amount,
            'withdrawn_amount' => $this->withdrawn_amount,
            'remained_amount' => $this->remained_amount,
            'status' => [
                'value' => $this->status,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColor(),
            ],
            'order' => $this->order ? [
                'id' => $this->order->id,
                'order_no' => $this->order->order_no,
            ] : null,
            'created_at' => $this->created_at,
        ];
    }
}
