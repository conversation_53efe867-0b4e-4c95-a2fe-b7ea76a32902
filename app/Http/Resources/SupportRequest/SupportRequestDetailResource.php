<?php

namespace App\Http\Resources\SupportRequest;

use App\Enums\MediaLibrary;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupportRequestDetailResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sr_no' => $this->sr_no,
            'subject' => $this->subject,
            'status' => [
                'value' => $this->status?->value,
                'label' => $this->status?->getLabel(),
            ],
            'user_id' => $this->user_id,
            'request_type' => [
                'value' => $this->request_type?->value,
                'label' => $this->request_type?->getLabel(),
            ],
            'priority' => [
                'value' => $this->priority?->value,
                'label' => $this->priority?->getLabel(),
            ],
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'messages' => $this->whenLoaded('messages', function () {
                return $this->messages->map(function ($message) {
                    return [
                        'id' => $message->id,
                        'user_id' => $message->user_id,
                        'content' => $message->message,
                        'updated_at' => $message->updated_at->format('Y-m-d H:i:s'),
                        'media' => $message->getMedia(MediaLibrary::SUPPORT_REQUEST_DOCUMENTS->value)->map(function ($media) {
                            return [
                                'id' => $media->id,
                                'url' => $media->getUrl(),
                                'name' => $media->file_name,
                            ];
                        }),
                    ];
                });
            }),
        ];
    }
}
