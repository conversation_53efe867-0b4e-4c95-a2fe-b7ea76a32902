<?php

namespace App\Http\Resources\SupportRequest;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupportRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sr_no' => $this->sr_no,
            'subject' => $this->subject,
            'status' => $this->status,
            'request_type' => $this->request_type,
            'priority' => $this->priority,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'message_count' => $this->whenCounted('messages'),
            'latest_message' => $this->whenLoaded('latestMessage', function () {
                return [
                    'content' => $this->latestMessage->message,
                    'updated_at' => $this->latestMessage->updated_at?->format('Y-m-d H:i:s'),
                ];
            }),
        ];
    }
}
