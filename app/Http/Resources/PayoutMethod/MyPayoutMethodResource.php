<?php

namespace App\Http\Resources\PayoutMethod;

use App\Traits\CommonTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MyPayoutMethodResource extends JsonResource
{
    use CommonTrait;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'bank_name' => $this->bank_name,
            'is_default' => $this->is_default,
            'account_number' => $this->maskAccountNumber($this->account_number),
            'payout_method_type' => [
                'value' => $this->payout_method_type,
                'label' => $this->payout_method_type->getLabel(),
            ],
        ];
    }
}
