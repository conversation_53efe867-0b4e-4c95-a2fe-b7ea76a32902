<?php

namespace App\Http\Resources\PayoutMethod;

use Illuminate\Http\Resources\Json\ResourceCollection;

class MyPayoutMethodCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'payoutMethods' => MyPayoutMethodResource::collection($this->collection),
        ];
    }
}
