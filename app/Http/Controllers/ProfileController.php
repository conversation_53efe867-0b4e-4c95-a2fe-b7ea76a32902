<?php

namespace App\Http\Controllers;

use App\Enums\GenderType;
use App\Http\Requests\ProfileUpdateRequest;
use App\Services\CountryService;
use App\Services\UserService;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * @var UserService The service for handling user-related operations.
     */
    protected $userService;

    /**
     * @var CountryService The service for handling country-related operations.
     */
    protected $countryService;

    /**
     * RegisteredUserController constructor.
     *
     * @param  UserService  $userService  The service for handling user-related operations.
     * @param  CountryService  $countryService  The service for handling country-related operations.
     */
    public function __construct(
        UserService $userService,
        CountryService $countryService,
    ) {
        $this->userService = $userService;
        $this->countryService = $countryService;
    }

    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        $countries = $this->countryService->getAllCountries();
        $genders = GenderType::getOptionsWithKeyValuePair();

        return Inertia::render('MyAccount/Profile/Edit', [
            'mustVerifyEmail' => $request->user() instanceof MustVerifyEmail,
            'status' => session('status'),
            'countries' => $countries,
            'genders' => $genders,
            'auth' => [
                'user' => $request->user()->load('userDetail'),
            ],
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $userId = $request->user()->id;
        $userDTO = $request->toDTO();

        $this->userService->updateUser($userId, $userDTO);

        return Redirect::route('profile.edit');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
