<?php

namespace App\Http\Controllers;

use App\Services\HomeService;
use Exception;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    protected $homeService;

    public function __construct(HomeService $homeService)
    {
        $this->homeService = $homeService;
    }

    public function index(): Response
    {
        try {
            $upcoming = $this->homeService->getUpcomingEvents();
            $featured = $this->homeService->getFeaturedEvents();

            return Inertia::render('Home', [
                'initialData' => [
                    'upcoming' => $upcoming->resolve(),
                    'featured' => $featured->resolve(),
                ],
            ]);
        } catch (Exception $e) {
            Log::info($e->getMessage(), [
                'exception' => $e,
            ]);

            return Inertia::render('Home', [
                'initialData' => [
                    'upcoming' => [],
                    'featured' => [],
                ],
                'error' => 'Failed to load home page data',
            ]);
        }
    }
}
