<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class PaymentController extends Controller
{
    public function success(Request $request)
    {
        return Inertia::render('Checkout/Success', [
            'orderId' => $request->query('orderId'),
        ]);
    }

    public function cancel(Request $request)
    {
        return Inertia::render('Checkout/Cancel', [
            'sessionId' => $request->query('session_id'),
        ]);
    }
}
