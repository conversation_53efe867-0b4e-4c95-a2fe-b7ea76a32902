<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;

class MyAccountController extends Controller
{
    public function index()
    {
        return Inertia::render('MyAccount/Dashboard');
    }

    public function settings()
    {
        return Inertia::render('MyAccount/Settings');
    }

    public function orders()
    {
        return Inertia::render('MyAccount/Orders');
    }

    public function orderDetail(Request $request, $id)
    {
        return Inertia::render('MyAccount/OrderDetail', [
            'id' => $id,
        ]);
    }

    public function support()
    {
        return Inertia::render('MyAccount/Support/Index');
    }
}
