<?php

namespace App\Http\Controllers;

use App\Models\CmsPage;
use App\Services\CmsPageService;
use Inertia\Inertia;

class CMSPageController extends Controller
{
    /**
     * @var CmsPageService The service for handling cmspage-related operations.
     */
    protected $cmsPageService;

    /**
     * CMSPageController constructor.
     *
     * @param  CmsPageService  $cmsPageService  The service for handling cmspage-related operations.
     */
    public function __construct(
        CmsPageService $cmsPageService,
    ) {
        $this->cmsPageService = $cmsPageService;
    }

    public function show($slug)
    {
        try {
            $cmsPage = $this->cmsPageService->getCmsPageDetailBySlug($slug);

            if (! $cmsPage) {
                return Inertia::render('Error', ['status' => 404])
                    ->toResponse(request())
                    ->setStatusCode(404);
            }

            return Inertia::render('CMSPage', [
                'cmsData' => $cmsPage,
            ]);
        } catch (\Exception $e) {
            return Inertia::render('Error')
                ->toResponse(request())
                ->setStatusCode(404);
        }
    }
}
