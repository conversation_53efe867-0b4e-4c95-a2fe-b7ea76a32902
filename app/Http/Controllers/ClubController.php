<?php

namespace App\Http\Controllers;

use App\Http\Requests\Api\V1\ClubFilterRequest;
use App\Services\ClubService;
use App\Services\CountryService;
use App\Services\StadiumService;
use Exception;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class ClubController extends Controller
{
    protected $clubService;

    protected $countryService;

    protected $stadiumService;

    public function __construct(
        ClubService $clubService,
        CountryService $countryService,
        StadiumService $stadiumService
    ) {
        $this->clubService = $clubService;
        $this->countryService = $countryService;
        $this->stadiumService = $stadiumService;
    }

    /**
     * Display the Clubs listings
     */
    public function index(ClubFilterRequest $request): Response
    {
        try {
            $filtersDTO = $request->toDTO();
            $clubs = $this->clubService->clubsList($filtersDTO);
            $countries = $this->countryService->countryOptionsList();
            $stadiums = $this->stadiumService->stadiumOptionsList();

            return Inertia::render('Clubs/Index', [
                'initialData' => [
                    'clubs' => $clubs->resolve(),
                    'filters' => [
                        'countries' => $countries,
                        'stadiums' => $stadiums,
                    ],
                ],
            ]);
        } catch (Exception $e) {
            Log::error('Error in ClubController@index: '.$e->getMessage());

            return Inertia::render('Clubs/Index', [
                'initialData' => [
                    'clubs' => [],
                    'filters' => [
                        'countries' => [],
                        'stadiums' => [],
                    ],
                ],
            ]);
        }
    }
}
