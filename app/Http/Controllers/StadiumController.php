<?php

namespace App\Http\Controllers;

use App\Http\Requests\Api\V1\StadiumFilterRequest;
use App\Services\CountryService;
use App\Services\StadiumService;
use Exception;
use Inertia\Inertia;
use Inertia\Response;

class StadiumController extends Controller
{
    protected $stadiumService;

    protected $countryService;

    public function __construct(
        StadiumService $stadiumService,
        CountryService $countryService
    ) {
        $this->stadiumService = $stadiumService;
        $this->countryService = $countryService;
    }

    public function index(StadiumFilterRequest $request): Response
    {
        try {
            $filtersDTO = $request->toDTO();

            // Fetch stadiums data using service
            $stadiums = $this->stadiumService->stadiumsList($filtersDTO);

            // Fetch filter options using service
            $countries = $this->countryService->countryOptionsList();
            $filters = [
                'countries' => $countries,
            ];

            return Inertia::render('Stadiums/Index', [
                'initialData' => [
                    'stadiums' => $stadiums->resolve(),
                    'filters' => $filters,
                ],
            ]);
        } catch (Exception $e) {
            return Inertia::render('Stadiums/Index', [
                'initialData' => [
                    'stadiums' => [],
                    'filters' => [],
                ],
            ]);
        }
    }
}
