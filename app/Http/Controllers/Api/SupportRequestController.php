<?php

namespace App\Http\Controllers\Api;

use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestStatus;
use App\Enums\SupportRequestType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\SupportTicketReplyRequest;
use App\Http\Requests\Api\V1\SupportTicketRequest;
use App\Http\Requests\Api\V1\SupportTicketStoreRequest;
use App\Http\Resources\SupportRequest\SupportRequestCollection;
use App\Http\Resources\SupportRequest\SupportRequestDetailResource;
use App\Http\Resources\SupportRequest\SupportRequestResource;
use App\Services\SupportRequestService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SupportRequestController extends Controller
{
    private $supportRequestService;

    public function __construct(SupportRequestService $supportRequestService)
    {
        $this->supportRequestService = $supportRequestService;
    }

    public function index(SupportTicketRequest $request)
    {
        try {
            $user = Auth::user();
            $supportRequestDTO = $request->toDTO();

            $supportRequests = $this->supportRequestService->getSupportRequests($user->id, $supportRequestDTO);

            return ApiResponse::success(new SupportRequestCollection($supportRequests), 'SUCCESS');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('GET_SUPPORT_REQUESTS_FAILD', $e, 500);
        }
    }

    public function store(SupportTicketStoreRequest $request): JsonResponse
    {
        try {
            // Begin transaction
            DB::beginTransaction();

            $dto = $request->toDTO();
            $supportRequest = $this->supportRequestService->createSupportRequest($dto);

            // Commit transaction if everything is successful
            DB::commit();

            return ApiResponse::success([
                'support_request' => new SupportRequestResource($supportRequest),
            ], 'CREATE_SUPPORT_REQUEST_SUCCESS', 201);
        } catch (\Exception $e) {
            // Rollback transaction if any error occurs
            DB::rollBack();

            return ApiResponse::errorWithLog('CREATE_SUPPORT_REQUEST_FAILED', $e, 500);
        }
    }

    public function show($id): JsonResponse
    {
        try {
            $supportRequest = $this->supportRequestService->getSupportDetail($id);

            return ApiResponse::success([
                'support_request' => new SupportRequestDetailResource($supportRequest),
            ], 'SUCCESS');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('GET_SUPPORT_REQUEST_FAILED', $e, 500);
        }
    }

    public function reply(SupportTicketReplyRequest $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();
            $dto = $request->toDTO();

            $supportRequest = $this->supportRequestService->addReply($id, $dto);

            DB::commit();

            return ApiResponse::success([
                'support_request' => new SupportRequestDetailResource($supportRequest),
            ], 'REPLY_SUPPORT_REQUEST_SUCCESS');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('REPLY_SUPPORT_REQUEST_FAILED', $e, 500);
        }
    }

    public function getEnums(): JsonResponse
    {
        try {
            return ApiResponse::success([
                'types' => SupportRequestType::getOptionsWithKeyValuePair(),
                'statuses' => SupportRequestStatus::getOptionsWithKeyValuePair(),
                'priorities' => SupportRequestPriority::getOptionsWithKeyValuePair(),
            ]);
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('GET_SUPPORT_ENUMS_FAILED', $e, 500);
        }
    }
}
