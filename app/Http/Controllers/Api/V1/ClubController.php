<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\ClubFilterRequest;
use App\Services\ClubService;
use App\Services\CountryService;
use App\Services\StadiumService;
use Exception;
use Illuminate\Http\JsonResponse;

class ClubController extends Controller
{
    protected $clubService;

    protected $countryService;

    protected $stadiumService;

    public function __construct(
        ClubService $clubService,
        CountryService $countryService,
        StadiumService $stadiumService,
    ) {
        $this->clubService = $clubService;
        $this->countryService = $countryService;
        $this->stadiumService = $stadiumService;
    }

    /**
     * Clubs listing API
     */
    public function index(ClubFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $clubs = $this->clubService->clubsList($filtersDTO);
            if ($clubs->isEmpty()) {
                return ApiResponse::error('CLUBS_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($clubs, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Get Filters listing on events page
     */
    public function getFilters(): JsonResponse
    {
        try {
            $countries = $this->countryService->countryOptionsList();
            $stadiums = $this->stadiumService->stadiumOptionsList();

            $data = [
                'countries' => $countries,
                'stadiums' => $stadiums,
            ];

            return ApiResponse::success($data, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Club detail page
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $club = $this->clubService->getClubDetailBySlug($slug);

            if (! $club) {
                return ApiResponse::error('CLUB_NOT_EXISTS', 404);
            }

            return ApiResponse::success(['club' => $club], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
