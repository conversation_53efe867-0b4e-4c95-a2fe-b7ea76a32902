<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\LoginRequest;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    public function login(LoginRequest $request)
    {
        $data = $request->validated();
        $user = $request->authenticate();

        $user->tokens()->where('name', $data['device_name'])->delete();
        $token = $user->createToken($data['device_name']);

        return ApiResponse::success([
            'user' => $user,
            'token' => $token->plainTextToken,
        ]);
    }

    public function user()
    {
        return ApiResponse::success([
            'user' => Auth::user(),
        ]);
    }

    public function logout()
    {
        Auth::user()->tokens()->delete();

        return response()->json(['message' => 'Logged out successfully']);
    }
}
