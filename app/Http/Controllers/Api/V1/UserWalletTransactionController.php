<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\WalletTransactionFilterRequest;
use App\Services\UserWalletService;
use Exception;
use Illuminate\Http\JsonResponse;

class UserWalletTransactionController extends Controller
{
    protected $userWalletService;

    public function __construct(UserWalletService $userWalletService)
    {
        $this->userWalletService = $userWalletService;
    }

    public function index(WalletTransactionFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $transactions = $this->userWalletService->getUserWalletTransactionList($filtersDTO);
            if ($transactions->isEmpty()) {
                return ApiResponse::error('WALLET_TRANSACTIONS_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($transactions, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    public function show($transactionNo): JsonResponse
    {
        try {
            $transaction = $this->userWalletService->getUserWalletTransactionDetail($transactionNo);

            if (! $transaction) {
                return ApiResponse::error('WALLET_TRANSACTION_NOT_FOUND', 404);
            }

            return ApiResponse::success(['transaction' => $transaction], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
