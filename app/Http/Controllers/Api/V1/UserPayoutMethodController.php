<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\UserPayoutMethodStoreRequest;
use App\Services\UserPayoutMethodService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserPayoutMethodController extends Controller
{
    protected $userPayoutMethodService;

    public function __construct(UserPayoutMethodService $userPayoutMethodService)
    {
        $this->userPayoutMethodService = $userPayoutMethodService;
    }

    public function index()
    {
        try {
            $payoutMethods = $this->userPayoutMethodService->getPayoutMethods();

            return ApiResponse::success($payoutMethods, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('ERROR', $e, 500);
        }
    }

    public function store(UserPayoutMethodStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $payoutMethodDTO = $request->toDTO();

            $payoutMethod = $this->userPayoutMethodService->createPayoutMethod($payoutMethodDTO);
            DB::commit();

            return ApiResponse::success([], 'PAYOUT_METHOD_CREATED');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('PAYOUT_METHOD_CREATE_FAILED', $e, 500);
        }
    }

    /**
     * Get configurations list for the create page
     */
    public function getConfigurations(): JsonResponse
    {
        try {
            $data = $this->userPayoutMethodService->getPayoutConfigurations();

            return ApiResponse::success($data, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Mark as Default payout method API
     */
    public function markDefaultPayoutMethod(Request $request)
    {
        try {
            $payoutMethodId = $request->get('id');
            $result = $this->userPayoutMethodService->markDefaultPayoutMethod($payoutMethodId);
            if (! $result) {
                return ApiResponse::error('ERROR', 400);
            }

            return ApiResponse::success([], 'PAYOUT_METHOD_MARKED_AS_DEFAULT');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Delete payout method API
     */
    public function delete($payoutMethodId)
    {
        try {
            $result = $this->userPayoutMethodService->deletePayoutMethod($payoutMethodId);
            if ($result !== 'PAYOUT_METHOD_DELETED') {
                return ApiResponse::error($result, 400);
            }

            return ApiResponse::success([], $result);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
