<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\MySalesFilterRequest;
use App\Http\Requests\Api\V1\OrderFilterRequest;
use App\Http\Requests\Api\V1\OrderOpenDisputeRequest;
use App\Http\Requests\Api\V1\OrderStatusCheckRequest;
use App\Http\Requests\Api\V1\OrderStatusUpdateRequest;
use App\Http\Requests\Api\V1\OrderStoreRequest;
use App\Http\Requests\Api\V1\OrderTicketsUploadRequest;
use App\Http\Resources\OrderDetailResource;
use App\Models\GeneralSetting;
use App\Services\OrderService;
use App\Services\OrderStatusService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    protected $orderService;

    protected $orderStatusService;

    public function __construct(OrderService $orderService, OrderStatusService $orderStatusService)
    {
        $this->orderService = $orderService;
        $this->orderStatusService = $orderStatusService;
    }

    public function index(OrderFilterRequest $request)
    {
        try {
            $filtersDTO = $request->toDTO();
            $orders = $this->orderService->getPaginatedOrdersForUser(Auth::id(), $filtersDTO);

            return ApiResponse::success($orders, 'SUCCESS');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('ERROR', $e, 500);
        }
    }

    public function mySales(MySalesFilterRequest $request)
    {
        try {
            $filtersDTO = $request->toDTO();
            $orders = $this->orderService->getMySalesOrders($filtersDTO);

            return ApiResponse::success($orders, 'SUCCESS');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('ERROR', $e, 500);
        }
    }

    public function store(OrderStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $orderDTO = $request->toDTO();

            [$order, $orderTransaction] = $this->orderService->createOrder($orderDTO);
            DB::commit();

            $paymentIntent = $this->orderService->createStripePaymentIntent($order);
            $orderTransaction->update(['payment_intent_id' => $paymentIntent->id]);

            $orderData = [
                'order_id' => $order->id,
                'clientSecret' => $paymentIntent->client_secret,
                'encryptedOrderId' => encrypt($order->id),
            ];

            return ApiResponse::success($orderData, 'ORDER_CREATED');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('ORDER_CREATE_FAILED', $e, 500);
        }
    }

    public function checkOrderStatus(OrderStatusCheckRequest $request)
    {
        DB::beginTransaction();
        try {
            $orderStatusDTO = $request->toDTO();

            $orderData = $this->orderStatusService->checkOrderStatus($orderStatusDTO);
            DB::commit();
            if (! $orderData) {
                return ApiResponse::error('ORDER_NOT_FOUND', 404);
            }

            return ApiResponse::success($orderData, 'SUCCESS');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('ORDER_STATUS_VERIFICATION_FAILD', $e, 500);
        }
    }

    public function show($id)
    {
        try {
            $order = $this->orderService->getOrderDetails(Auth::id(), $id);

            if (! $order) {
                return ApiResponse::error('ORDERS_NOT_FOUND', 404);
            }

            return ApiResponse::success([
                'order' => new OrderDetailResource($order),
            ]);
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('ERROR', $e, 500);
        }
    }

    public function salesOrderShow($orderNo)
    {
        try {
            $order = $this->orderService->getSalesOrderDetails($orderNo);

            if (! $order) {
                return ApiResponse::error('ORDERS_NOT_FOUND', 404);
            }

            return ApiResponse::success(['order' => $order], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    public function updateOrderStatus(OrderStatusUpdateRequest $request)
    {
        DB::beginTransaction();
        try {
            $orderStatusDTO = $request->toDTO();

            $orderData = $this->orderStatusService->updateOrderStatus($orderStatusDTO);
            DB::commit();
            if (! $orderData) {
                return ApiResponse::error('ORDER_NOT_FOUND', 404);
            }

            return ApiResponse::success([], 'SUCCESS');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('ORDER_STATUS_UPDATE_FAILD', $e, 500);
        }
    }

    public function openDispute(OrderOpenDisputeRequest $request)
    {
        DB::beginTransaction();
        try {
            $openDisputeDTO = $request->toDTO();

            $orderData = $this->orderStatusService->openOrderDispute($openDisputeDTO);
            DB::commit();
            if (! $orderData) {
                return ApiResponse::error('ORDER_NOT_FOUND', 404);
            }

            return ApiResponse::success([], 'SUCCESS');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('ORDER_OPEN_DISPUTE_FAILD', $e, 500);
        }
    }

    public function uploadOrderTickets(OrderTicketsUploadRequest $request)
    {
        DB::beginTransaction();
        try {
            $orderStatusDTO = $request->toDTO();

            $orderData = $this->orderStatusService->reUploadOrderTickets($orderStatusDTO);
            DB::commit();
            if (! $orderData) {
                return ApiResponse::error('ORDER_NOT_FOUND', 404);
            }

            return ApiResponse::success([], 'SUCCESS');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('ORDER_TICKETS_UPLOAD_FAILD', $e, 500);
        }
    }

    public function markTicketsDownloaded(Request $request)
    {
        DB::beginTransaction();
        try {
            $orderId = $request->get('order_id');

            $orderData = $this->orderService->markTicketsDownloaded($orderId);
            DB::commit();
            if (! $orderData) {
                return ApiResponse::error('ORDER_NOT_FOUND', 404);
            }

            return ApiResponse::success([], 'SUCCESS');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('ORDER_TICKETS_UPLOAD_FAILD', $e, 500);
        }
    }

    public function getStatuses()
    {
        $statuses = $this->orderService->getStatusOptions();

        return ApiResponse::success(['statuses' => $statuses], 'SUCCESS');
    }

    public function downloadInvoice($orderId)
    {
        try {

            $order = $this->orderService->getOrderDetail($orderId);

            if (! $order) {
                return ApiResponse::error('ORDER_NOT_FOUND', 404);
            }

            $language = app()->getLocale();
            $generalSettings = GeneralSetting::pluck('setting_value', 'setting_key');

            $pdf = Pdf::loadView('pdf.invoice', compact('order', 'language', 'generalSettings'));

            return $pdf->download($order->order_no.'_invoice.pdf');

        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('ERROR', $e, 500);
        }

    }
}
