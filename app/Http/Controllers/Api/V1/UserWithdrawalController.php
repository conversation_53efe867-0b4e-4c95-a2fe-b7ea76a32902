<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\WithdrawalFilterRequest;
use App\Http\Requests\Api\V1\WithdrawalStoreRequest;
use App\Services\UserWithdrawalService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class UserWithdrawalController extends Controller
{
    protected $userWithdrawalService;

    public function __construct(UserWithdrawalService $userWithdrawalService)
    {
        $this->userWithdrawalService = $userWithdrawalService;
    }

    public function index(WithdrawalFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $transactions = $this->userWithdrawalService->getUserWithdrawalList($filtersDTO);
            if ($transactions->isEmpty()) {
                return ApiResponse::error('WITHDRAWAL_REQUESTS_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($transactions, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    public function getConfigurations(): JsonResponse
    {
        try {
            $data = $this->userWithdrawalService->getWithdrawalConfigurations();

            return ApiResponse::success($data, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    public function store(WithdrawalStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $withdrawalData = $request->toDTO();
            $withdrawal = $this->userWithdrawalService->addWithdrawalRequest($withdrawalData);
            DB::commit();

            return ApiResponse::success(['withdraw_no' => $withdrawal->withdraw_no], 'WITHDRAW_REQUEST_CREATED');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    public function checkUserCanWithdraw()
    {
        try {
            $result = $this->userWithdrawalService->checkUserCanWithdraw();

            return ApiResponse::success($result, 'SUCCESS');
        } catch (Exception $e) {

            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Delete withdrawal requset API
     */
    public function delete($withdrawalId)
    {
        DB::beginTransaction();
        try {
            $result = $this->userWithdrawalService->deleteWithdrawalRequest($withdrawalId);
            DB::commit();
            if ($result !== 'WITHDRAWAL_REQUEST_DELETED') {
                return ApiResponse::error($result, 400);
            }

            return ApiResponse::success([], $result);
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
