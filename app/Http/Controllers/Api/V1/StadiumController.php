<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\StadiumFilterRequest;
use App\Services\CountryService;
use App\Services\StadiumService;
use Exception;
use Illuminate\Http\JsonResponse;

class StadiumController extends Controller
{
    protected $stadiumService;

    protected $countryService;

    public function __construct(StadiumService $stadiumService, CountryService $countryService)
    {
        $this->stadiumService = $stadiumService;
        $this->countryService = $countryService;
    }

    /**
     * Stadiums listing API
     */
    public function index(StadiumFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $stadiums = $this->stadiumService->stadiumsList($filtersDTO);
            if ($stadiums->isEmpty()) {
                return ApiResponse::error('STADIUMS_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($stadiums, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Get Filters listing on events page
     */
    public function getFilters(): JsonResponse
    {
        try {
            $countries = $this->countryService->countryOptionsList();

            $data = [
                'countries' => $countries,
            ];

            return ApiResponse::success($data, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Stadium detail page
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $stadium = $this->stadiumService->getStadiumDetailBySlug($slug);

            if (! $stadium) {
                return ApiResponse::error('STADIUM_NOT_EXISTS', 404);
            }

            return ApiResponse::success(['stadium' => $stadium], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
