<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Services\TranslationService;
use Exception;
use Illuminate\Http\JsonResponse;

class TranslationsController extends Controller
{
    private TranslationService $translationService;

    public function __construct(TranslationService $translationService)
    {
        $this->translationService = $translationService;
    }

    /**
     * Translations API
     */
    public function index(): JsonResponse
    {
        try {
            $result = $this->translationService->getTranslationsWithLocale();

            return ApiResponse::success($result, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
