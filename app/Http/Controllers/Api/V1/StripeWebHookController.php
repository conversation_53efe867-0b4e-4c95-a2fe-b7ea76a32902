<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Services\StripeWebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StripeWebHookController extends Controller
{
    private $stripeWebhookService;

    public function __construct(StripeWebhookService $stripeWebhookService)
    {
        $this->stripeWebhookService = $stripeWebhookService;
    }

    public function handleCheckoutWebhook(Request $request)
    {
        try {
            $webhookSecret = config('services.stripe.webhook_secret');

            $payload = $request->getContent();
            $sigHeader = $request->header('Stripe-Signature');

            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $sigHeader,
                $webhookSecret
            );
            $this->stripeWebhookService->addPaymentLog($event);

            DB::beginTransaction();
            switch ($event->type) {
                case 'payment_intent.created':
                    $this->stripeWebhookService->handlePaymentIntentCreated($event);
                    break;
                case 'charge.succeeded':
                    $this->stripeWebhookService->handleChargeSucceeded($event);
                    break;
                case 'payment_intent.succeeded':
                    $this->stripeWebhookService->handlePaymentIntentSucceeded($event);
                    break;
                case 'charge.refunded':
                    $this->stripeWebhookService->handleChargeRefunded($event);
                    break;
                default:
                    break;
            }
            DB::commit();

            return ApiResponse::success([], 'Webhook handled successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to handle webhook', $e, 500);
        }
    }
}
