<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Services\HomeService;
use Exception;
use Illuminate\Http\JsonResponse;

class HomeController extends Controller
{
    protected $homeService;

    public function __construct(
        HomeService $homeService,
    ) {
        $this->homeService = $homeService;
    }

    /**
     * Home Page Content listing API
     */
    public function index(): JsonResponse
    {
        try {
            $upcoming = $this->homeService->getUpcomingEvents();
            $featured = $this->homeService->getFeaturedEvents();

            return ApiResponse::success(
                [
                    'upcoming' => $upcoming,
                    'featured' => $featured,
                ],
                'SUCCESS'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
