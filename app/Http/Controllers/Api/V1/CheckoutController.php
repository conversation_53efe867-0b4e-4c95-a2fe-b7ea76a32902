<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\CheckoutSessionRequest;
use App\Repositories\OrderRepository;
use App\Repositories\OrderTransactionRepository;
use App\Services\CheckoutService;
use Illuminate\Support\Facades\DB;

class CheckoutController extends Controller
{
    private $checkoutService;

    private $orderTransactionRepository;

    private $orderRepository;

    public function __construct()
    {
        $this->checkoutService = app(CheckoutService::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->orderRepository = app(OrderRepository::class);
    }

    public function createSession(CheckoutSessionRequest $request)
    {
        try {
            DB::beginTransaction();
            $checkoutDTO = $request->toDTO();

            $session = $this->checkoutService->createCheckoutSession($checkoutDTO);
            DB::commit();

            return ApiResponse::success([
                'url' => $session->url,
                'session_id' => $session->id,
            ], 'Checkout session created successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create checkout session', $e, 500);
        }
    }
}
