<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\SearchFilterRequest;
use App\Services\SearchService;
use Exception;

class SearchController extends Controller
{
    protected $searchService;

    public function __construct(
        SearchService $searchService,
    ) {
        $this->searchService = $searchService;
    }

    public function index(SearchFilterRequest $request)
    {
        try {
            $search = $request->toDTO();
            $results = $this->searchService->getSearchResults($search);

            return ApiResponse::success(['results' => $results], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    public function suggestions(SearchFilterRequest $request)
    {
        try {
            $search = $request->toDTO();
            $suggestions = $this->searchService->getSearchSuggestions($search);

            return ApiResponse::success(['suggestions' => $suggestions], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
