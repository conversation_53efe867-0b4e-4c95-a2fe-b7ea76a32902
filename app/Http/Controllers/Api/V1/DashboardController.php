<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\DashboardFilterRequest;
use App\Services\DashboardService;
use Exception;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(
        DashboardService $dashboardService,
    ) {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Dashboard my stats API
     */
    public function myStats(DashboardFilterRequest $request): JsonResponse
    {
        try {

            $filtersDTO = $request->toDTO();
            $result = $this->dashboardService->getDashboardStats($filtersDTO);

            return ApiResponse::success($result, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
