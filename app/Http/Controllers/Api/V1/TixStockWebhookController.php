<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Services\TixStock\TixStockWebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TixStockWebhookController extends Controller
{
    private $tistockWebhookService;

    public function __construct(TixStockWebhookService $tistockWebhookService)
    {
        $this->tistockWebhookService = $tistockWebhookService;
    }

    public function handleWebhook(Request $request)
    {
        try {
            $payload = $request->getContent();
            $signatureHeader = $request->header('X-TIXSTOCK-SIGNATURE');

            $computedSignature = hash_hmac('sha256', $payload, config('services.tixstock.bearer_token'));

            // Compare signatures securely
            if (! hash_equals($computedSignature, $signatureHeader)) {
                return ApiResponse::error('Invalid signature');
            }

            $event = json_decode($payload);

            $this->tistockWebhookService->addWebhookLog($event);

            DB::beginTransaction();
            switch ($event->meta->type) {
                case 'ticket.hold':
                    $this->tistockWebhookService->handleHoldAndReleaseTicket($event);
                    break;
                case 'ticket.release':
                    $this->tistockWebhookService->handleHoldAndReleaseTicket($event);
                    break;
                case 'order.update':
                    $this->tistockWebhookService->handleOrderUpdate($event);
                    break;
                case 'order.eticket_fulfilment':
                    $this->tistockWebhookService->handleOrderTicketsFile($event);
                    break;
                case 'order.mobile_ticket_fulfilment':
                    $this->tistockWebhookService->handleOrderTicketsFile($event);
                    break;
                default:
                    break;
            }
            DB::commit();

            return ApiResponse::success([], 'Tixstock Webhook handled successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to handle webhook', $e, 500);
        }
    }
}
