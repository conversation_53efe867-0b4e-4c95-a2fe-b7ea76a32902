<?php

namespace App\Http\Controllers\Api\V1;

use App\Enums\EventCategoryType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\EventFilterRequest;
use App\Http\Requests\Api\V1\SellTicketsFilterRequest;
use App\Services\ClubService;
use App\Services\CountryService;
use App\Services\EventService;
use App\Services\LeagueService;
use App\Services\StadiumService;
use Exception;
use Illuminate\Http\JsonResponse;

class EventController extends Controller
{
    protected $eventService;

    protected $stadiumService;

    protected $clubService;

    protected $countryService;

    protected $leagueService;

    public function __construct(
        EventService $eventService,
        StadiumService $stadiumService,
        ClubService $clubService,
        CountryService $countryService,
        LeagueService $leagueService,
    ) {
        $this->eventService = $eventService;
        $this->stadiumService = $stadiumService;
        $this->clubService = $clubService;
        $this->countryService = $countryService;
        $this->leagueService = $leagueService;
    }

    /**
     * Events listing API
     */
    public function index(EventFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $events = $this->eventService->eventsList($filtersDTO);

            return ApiResponse::success($events, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Get Filters listing on events page
     */
    public function getFilters(): JsonResponse
    {
        try {
            $stadiums = $this->stadiumService->stadiumOptionsList();
            $clubs = $this->clubService->clubOptionsList();
            $leagues = $this->leagueService->leagueOptionsList();
            $countries = $this->countryService->countryOptionsList();
            $categories = EventCategoryType::getOptionsWithKeyValuePair();

            $data = [
                'clubs' => $clubs,
                'stadiums' => $stadiums,
                'leagues' => $leagues,
                'countries' => $countries,
                'categories' => $categories,
            ];

            return ApiResponse::success($data, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Events listing API for sell tickets
     */
    public function sellTickets(SellTicketsFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $events = $this->eventService->eventsListForSellTickets($filtersDTO);
            if ($events->isEmpty()) {
                return ApiResponse::error('EVENTS_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($events, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Event detail page
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $event = $this->eventService->getEventDetailBySlug($slug);

            if (! $event) {
                return ApiResponse::error('EVENT_NOT_EXISTS', 404);
            }

            return ApiResponse::success(['event' => $event], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
