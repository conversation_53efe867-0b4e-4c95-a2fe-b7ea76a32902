<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\TicketReservationLockRequest;
use App\Services\TicketReservationService;
use Illuminate\Support\Facades\DB;

class TicketReservationController extends Controller
{
    private $ticketReservationService;

    public function __construct()
    {
        $this->ticketReservationService = app(TicketReservationService::class);
    }

    public function createPreliminaryReservation(TicketReservationLockRequest $request)
    {
        DB::beginTransaction();
        try {
            $ticketReservationDTO = $request->toDTO();

            if ($this->ticketReservationService->getUserActiveReservation() !== '') {
                return ApiResponse::error('TRANSACTION_IN_PROGRESS', 400);
            }

            $encryptedReservationId = $this->ticketReservationService->lockTheTransaction($ticketReservationDTO);
            DB::commit();

            return ApiResponse::success([
                'temp_ticket_reservation_id' => $encryptedReservationId,
                'expires_in' => config('services.ticketgol.temp_reservation_minutes'),
            ], 'TICKET_RESERVATION_CREATED');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('TICKET_RESERVATION_CREATE_FAILED', $e, 500);
        }
    }

    public function getPreliminaryReservation($reservationId)
    {
        try {
            $reservation = $this->ticketReservationService->getReservationDetail($reservationId);

            return ApiResponse::success(['reservation' => $reservation], 'SUCCESS');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('TICKET_RESERVATION_GET_ERROR', $e, 500);
        }
    }

    public function checkActiveReservation()
    {
        try {
            $reservationId = $this->ticketReservationService->getUserActiveReservation();

            return ApiResponse::success([
                'is_active' => $reservationId !== '' ? true : false,
                'reservationId' => $reservationId,
            ], 'TICKET_RESERVATION_CHECK_DONE');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('TICKET_RESERVATION_GET_ERROR', $e, 500);
        }
    }
}
