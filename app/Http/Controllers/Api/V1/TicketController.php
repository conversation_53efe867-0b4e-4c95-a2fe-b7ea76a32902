<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\EventTicketFilterRequest;
use App\Http\Requests\Api\V1\MyTicketFilterRequest;
use App\Http\Requests\Api\V1\TicketStoreRequest;
use App\Http\Requests\Api\V1\TicketUpdateRequest;
use App\Services\TicketService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class TicketController extends Controller
{
    protected $ticketService;

    public function __construct(
        TicketService $ticketService,
    ) {
        $this->ticketService = $ticketService;
    }

    /**
     * Tickets listing for specific event API
     */
    public function index(EventTicketFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $tickets = $this->ticketService->eventTicketsList($filtersDTO);
            if ($tickets->isEmpty()) {
                return ApiResponse::error('TICKETS_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($tickets, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Tickets listing for logged in user API
     */
    public function myTickets(MyTicketFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $tickets = $this->ticketService->myTicketsList($filtersDTO);
            if ($tickets->isEmpty()) {
                return ApiResponse::error('TICKETS_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($tickets, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Tickets common configurations API
     */
    public function configurations(): JsonResponse
    {
        try {
            $data = $this->ticketService->getConfigurations();

            return ApiResponse::success($data, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Ticket store API
     */
    public function store(TicketStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $ticketData = $request->toDTO();
            $ticket = $this->ticketService->addTicket($ticketData);
            DB::commit();

            return ApiResponse::success(['ticket_no' => $ticket->ticket_no], 'TICKET_CREATED');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Ticket update API
     */
    public function update(TicketUpdateRequest $request)
    {
        DB::beginTransaction();
        try {
            $ticketData = $request->toDTO();
            $ticket = $this->ticketService->updateTicket($ticketData);
            DB::commit();

            return ApiResponse::success(['ticket_no' => $ticket->ticket_no], 'TICKET_UPDATED');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Get the ticket details API
     */
    public function show($ticketId)
    {
        try {
            $ticket = $this->ticketService->showMyTicket($ticketId);
            if (! $ticket) {
                return ApiResponse::error('TICKETS_NOT_FOUND', 404);
            }

            return ApiResponse::success(['ticket' => $ticket], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Ticket store API
     */
    public function delete($ticketId)
    {
        try {
            $result = $this->ticketService->deleteMyTicket($ticketId);
            if ($result !== 'TICKET_DELETED') {
                return ApiResponse::error($result, 400);
            }

            return ApiResponse::success([], $result);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
