<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\LeagueFilterRequest;
use App\Services\CountryService;
use App\Services\LeagueService;
use App\Services\SeasonService;
use Exception;
use Illuminate\Http\JsonResponse;

class LeagueController extends Controller
{
    protected $leagueService;

    protected $countryService;

    protected $seasonService;

    public function __construct(
        LeagueService $leagueService,
        CountryService $countryService,
        SeasonService $seasonService,
    ) {
        $this->leagueService = $leagueService;
        $this->countryService = $countryService;
        $this->seasonService = $seasonService;
    }

    /**
     * Leagues listing API
     */
    public function index(LeagueFilterRequest $request): JsonResponse
    {
        try {
            $filtersDTO = $request->toDTO();

            $leagues = $this->leagueService->leaguesList($filtersDTO);
            if ($leagues->isEmpty()) {
                return ApiResponse::error('LEAGUES_NOT_FOUND', 404);
            } else {
                return ApiResponse::success($leagues, 'SUCCESS');
            }
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * Get Filters listing on events page
     */
    public function getFilters(): JsonResponse
    {
        try {
            $countries = $this->countryService->countryOptionsList();
            $seasons = $this->seasonService->seasonOptionsList();

            $data = [
                'countries' => $countries,
                'seasons' => $seasons,
            ];

            return ApiResponse::success($data, 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }

    /**
     * League detail page
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $league = $this->leagueService->getLeagueDetailBySlug($slug);

            if (! $league) {
                return ApiResponse::error('LEAGUE_NOT_EXISTS', 404);
            }

            return ApiResponse::success(['league' => $league], 'SUCCESS');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog($e->getMessage(), $e, 422);
        }
    }
}
