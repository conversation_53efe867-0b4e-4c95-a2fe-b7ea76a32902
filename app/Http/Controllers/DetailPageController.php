<?php

namespace App\Http\Controllers;

use App\DTO\EventTicketFilterDTO;
use App\Services\ClubService;
use App\Services\CmsPageService;
use App\Services\EventService;
use App\Services\LeagueService;
use App\Services\SlugService;
use App\Services\StadiumService;
use App\Services\TicketService;
use Inertia\Inertia;
use Inertia\Response;

class DetailPageController extends Controller
{
    protected $cmsPageService;

    protected $slugService;

    protected $eventService;

    protected $clubService;

    protected $stadiumService;

    protected $leagueService;

    protected $ticketService;

    public function __construct(
        CmsPageService $cmsPageService,
        SlugService $slugService,
        EventService $eventService,
        ClubService $clubService,
        StadiumService $stadiumService,
        LeagueService $leagueService,
        TicketService $ticketService,
    ) {
        $this->cmsPageService = $cmsPageService;
        $this->slugService = $slugService;
        $this->eventService = $eventService;
        $this->clubService = $clubService;
        $this->stadiumService = $stadiumService;
        $this->leagueService = $leagueService;
        $this->ticketService = $ticketService;
    }

    /**
     * Display the detail pages of Event, Club, Stadium, League, CmsPage
     */
    public function show($slug): Response
    {
        try {
            $slugRecord = $this->slugService->getPageDetailBySlug($slug);

            $model = $slugRecord->sluggable;
            $modelName = class_basename($model);

            if ($modelName == 'CmsPage') {
                $cmsPage = $this->cmsPageService->getCmsPageDetailBySlug($slug);

                return Inertia::render('CMSPage', [
                    'cmsData' => $cmsPage,
                    'pageSlugs' => $model->slugs->pluck('slug', 'locale'),
                ]);
            } else {
                $component = "{$modelName}s/Show";
                $detailData = null;

                // Get detail data based on model type for SSR
                $additionalData = [];
                switch ($modelName) {
                    case 'Event':
                        $eventDetails = $this->eventService->getEventDetailBySlug($slug);

                        // Fetch initial tickets for SSR
                        if ($eventDetails && $eventDetails->id) {
                            $ticketFiltersDTO = new EventTicketFilterDTO(
                                eventId: $eventDetails->id,
                                priceRange: [],
                                sector: null,
                                quantity: null,
                                ticketType: null,
                                sort: null
                            );
                            $tickets = $this->ticketService->eventTicketsList($ticketFiltersDTO);

                            $detailData = [
                                'event' => $eventDetails,
                                'tickets' => $tickets->resolve(),
                            ];
                        }
                        break;
                    case 'Club':
                        $detailData = $this->clubService->getClubDetailBySlug($slug);
                        break;
                    case 'Stadium':
                        $detailData = $this->stadiumService->getStadiumDetailBySlug($slug);
                        break;
                    case 'League':
                        $detailData = $this->leagueService->getLeagueDetailBySlug($slug);
                        break;
                }

                return Inertia::render($component, array_merge([
                    'slug' => $model->localizedSlug->slug,
                    'pageSlugs' => $model->slugs->pluck('slug', 'locale'),
                    'initialData' => $detailData,
                ], $additionalData));
            }
        } catch (\Exception $e) {
            return Inertia::render('Error');
        }
    }
}
