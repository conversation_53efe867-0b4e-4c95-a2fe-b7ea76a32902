<?php

namespace App\Http\Controllers;

use App\Http\Requests\Api\V1\LeagueFilterRequest;
use App\Services\CountryService;
use App\Services\LeagueService;
use App\Services\SeasonService;
use Exception;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class LeagueController extends Controller
{
    protected $leagueService;

    protected $countryService;

    protected $seasonService;

    public function __construct(
        LeagueService $leagueService,
        CountryService $countryService,
        SeasonService $seasonService
    ) {
        $this->leagueService = $leagueService;
        $this->countryService = $countryService;
        $this->seasonService = $seasonService;
    }

    /**
     * Display the Leagues listings
     */
    public function index(LeagueFilterRequest $request): Response
    {
        try {
            $filtersDTO = $request->toDTO();
            $leagues = $this->leagueService->leaguesList($filtersDTO);
            $countries = $this->countryService->countryOptionsList();
            $seasons = $this->seasonService->seasonOptionsList();

            return Inertia::render('Leagues/Index', [
                'initialData' => [
                    'leagues' => $leagues->resolve(),
                    'filters' => [
                        'countries' => $countries,
                        'seasons' => $seasons,
                    ],
                ],
            ]);
        } catch (Exception $e) {
            Log::error('Error in LeagueController@index: '.$e->getMessage());

            return Inertia::render('Leagues/Index', [
                'initialData' => [
                    'leagues' => [],
                    'filters' => [
                        'countries' => [],
                        'seasons' => [],
                    ],
                ],
            ]);
        }
    }
}
