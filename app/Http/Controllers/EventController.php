<?php

namespace App\Http\Controllers;

use App\Enums\EventCategoryType;
use App\Http\Requests\Api\V1\SellTicketsFilterRequest;
use App\Http\Requests\EventFilterRequest;
use App\Services\ClubService;
use App\Services\CountryService;
use App\Services\EventService;
use App\Services\LeagueService;
use App\Services\StadiumService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class EventController extends Controller
{
    /**
     * @var EventService
     */
    protected $eventService;

    /**
     * @var StadiumService
     */
    protected $stadiumService;

    /**
     * @var ClubService
     */
    protected $clubService;

    /**
     * @var CountryService
     */
    protected $countryService;

    /**
     * @var LeagueService
     */
    protected $leagueService;

    /**
     * EventController constructor.
     */
    public function __construct(
        EventService $eventService,
        StadiumService $stadiumService,
        ClubService $clubService,
        CountryService $countryService,
        LeagueService $leagueService,
    ) {
        $this->eventService = $eventService;
        $this->stadiumService = $stadiumService;
        $this->clubService = $clubService;
        $this->countryService = $countryService;
        $this->leagueService = $leagueService;
    }

    /**
     * Display the Events listings
     */
    public function index(EventFilterRequest $request): Response
    {
        try {
            // Convert request to DTO
            $filterDTO = $request->toDTO();

            // Fetch events data from service
            $eventsData = $this->eventService->eventsList($filterDTO);

            // Fetch filter options from services
            $filtersData = $this->getFilters();

            return Inertia::render('Events/Index', [
                'initialData' => [
                    'events' => $eventsData,
                    'filters' => $filtersData,
                ],
            ]);
        } catch (Exception $e) {
            // Handle error appropriately
            return Inertia::render('Events/Index', [
                'initialData' => [
                    'events' => [],
                    'filters' => [],
                ],
                'error' => 'Failed to load events data',
            ]);
        }
    }

    public function sellTickets(SellTicketsFilterRequest $request): Response
    {
        try {
            $filtersDTO = $request->toDTO();

            $events = $this->eventService->eventsListForSellTickets($filtersDTO);

            return Inertia::render('Events/SellTickets', [
                'initialData' => $events->resolve(),
            ]);
        } catch (Exception $e) {
            Log::info($e->getMessage(), $e->getTrace());

            return Inertia::render('Events/SellTickets', [
                'initialData' => [],
            ]);
        }
    }

    private function getFilters(): array
    {
        try {
            return [
                'stadiums' => $this->stadiumService->stadiumOptionsList(),
                'clubs' => $this->clubService->clubOptionsList(),
                'leagues' => $this->leagueService->leagueOptionsList(),
                'countries' => $this->countryService->countryOptionsList(),
                'categories' => EventCategoryType::cases(),
            ];
        } catch (Exception $e) {
            return [
                'stadiums' => [],
                'clubs' => [],
                'leagues' => [],
                'countries' => [],
                'categories' => [],
            ];
        }
    }
}
