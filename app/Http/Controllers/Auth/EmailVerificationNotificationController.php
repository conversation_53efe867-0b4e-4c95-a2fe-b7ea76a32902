<?php

namespace App\Http\Controllers\Auth;

use App\Enums\EmailTemplateKeys;
use App\Http\Controllers\Controller;
use App\Jobs\SendEmailJob;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;

class EmailVerificationNotificationController extends Controller
{
    /**
     * Send a new email verification notification.
     */
    public function store(Request $request): RedirectResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect()->intended(route('dashboard', absolute: false));
        }

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $request->user()->id, 'hash' => sha1($request->user()->email)]
        );
        $meta = [
            'name' => $request->user()->name,
            'locale' => app()->getLocale(),
            'verificationLink' => $verificationUrl,
        ];
        // Send verification email notification
        SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_EMAIL_VERIFICATION->value, $meta, [$request->user()->id]);

        return back()->with('status', 'verification-link-sent');
    }
}
