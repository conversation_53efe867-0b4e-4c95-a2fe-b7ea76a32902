<?php

namespace App\Http\Controllers\Auth;

use App\Enums\GenderType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use App\Services\CountryService;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * @var UserService The service for handling user-related operations.
     */
    protected $userService;

    /**
     * @var CountryService The service for handling country-related operations.
     */
    protected $countryService;

    /**
     * RegisteredUserController constructor.
     *
     * @param  UserService  $userService  The service for handling user-related operations.
     * @param  CountryService  $countryService  The service for handling country-related operations.
     */
    public function __construct(
        UserService $userService,
        CountryService $countryService,
    ) {
        $this->userService = $userService;
        $this->countryService = $countryService;
    }

    /**
     * Display the registration view.
     */
    public function create(): Response
    {
        $countries = $this->countryService->getAllCountries();
        $genders = GenderType::getOptionsWithKeyValuePair();

        return Inertia::render('Auth/Register', compact('countries', 'genders'));
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(RegisterRequest $request): RedirectResponse
    {
        $userDTO = $request->toDTO();
        $this->userService->createUser($userDTO);

        return redirect(route('dashboard', absolute: false));
    }
}
