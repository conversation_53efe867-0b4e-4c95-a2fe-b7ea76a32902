<?php

/**
 * Events
 *
 * @category App\Events
 */

namespace App\Events;

use Illuminate\Queue\SerializesModels;

/**
 * Class SendMailEvent
 *
 * @category App\Events;
 */
class SendMailEvent
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param  array  $mailData
     * @return void
     */
    public function __construct($mailData)
    {
        $this->mailData = $mailData;
    }
}
