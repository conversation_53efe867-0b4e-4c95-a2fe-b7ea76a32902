<?php

namespace App\Filament\Resources;

use App\Enums\WithdrawalStatus;
use App\Filament\Resources\UserWithdrawalResource\Pages;
use App\Jobs\SendWithdrawalRequestStatusChangeEmailJob;
use App\Models\UserWithdrawal;
use App\Services\UserWalletService;
use App\Traits\NavigationBadgeTrait;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class UserWithdrawalResource extends Resource
{
    use NavigationBadgeTrait;

    protected static ?string $model = UserWithdrawal::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $modelLabel = 'Customer Withdrawals';

    protected static ?string $navigationGroup = 'User Mangement';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('withdraw_no')
                    ->disabled()
                    ->dehydrated(false)
                    ->visible(fn ($record) => $record != null),
                TextInput::make('user')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->user->name),
                TextInput::make('amount')
                    ->disabled()
                    ->numeric(),
                TextInput::make('currency_code')
                    ->disabled(),
                TextInput::make('bank_name')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->bank_name),
                TextInput::make('account_number')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->account_number),
                TextInput::make('account_holder_name')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->account_holder_name),
                TextInput::make('account_type')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->account_type->getLabel()),
                TextInput::make('swift_code')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->swift_code),
                TextInput::make('branch_name')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->branch_name)
                    ->visible(fn ($state, $record) => $record->payoutMethod->branch_name),
                TextInput::make('iban')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->iban)
                    ->visible(fn ($state, $record) => $record->payoutMethod->iban),
                TextInput::make('ifsc_code')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->ifsc_code)
                    ->visible(fn ($state, $record) => $record->payoutMethod->ifsc_code),
                TextInput::make('routing_number')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->routing_number)
                    ->visible(fn ($state, $record) => $record->payoutMethod->routing_number),
                TextInput::make('sort_code')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->sort_code)
                    ->visible(fn ($state, $record) => $record->payoutMethod->sort_code),
                TextInput::make('bsb')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->bsb)
                    ->visible(fn ($state, $record) => $record->payoutMethod->bsb),
                TextInput::make('bank_code')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->bank_code)
                    ->visible(fn ($state, $record) => $record->payoutMethod->bank_code),
                TextInput::make('branch_code')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->branch_code)
                    ->visible(fn ($state, $record) => $record->payoutMethod->branch_code),
                TextInput::make('institution_number')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->institution_number)
                    ->visible(fn ($state, $record) => $record->payoutMethod->institution_number),
                TextInput::make('transit_number')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record->payoutMethod->transit_number)
                    ->visible(fn ($state, $record) => $record->payoutMethod->transit_number),

                Select::make('status')
                    ->options(function ($get, $record) {
                        $current = $record->status->value ?? null;

                        return match ($current) {
                            WithdrawalStatus::PENDING->value => WithdrawalStatus::getOptionsWithKeyValuePair([
                                WithdrawalStatus::APPROVED->value,
                                WithdrawalStatus::REJECTED->value,
                            ]),
                            WithdrawalStatus::REJECTED->value => WithdrawalStatus::getOptionsWithKeyValuePair([
                                WithdrawalStatus::APPROVED->value,
                            ]),
                            WithdrawalStatus::APPROVED->value => WithdrawalStatus::getOptionsWithKeyValuePair([
                                WithdrawalStatus::PAID->value,
                            ]),
                            WithdrawalStatus::PAID->value => WithdrawalStatus::getOptionsWithKeyValuePair([
                                WithdrawalStatus::PAID->value,
                            ]),
                            default => [],
                        };
                    })
                    ->preload()
                    ->reactive()
                    ->default(WithdrawalStatus::PENDING)
                    ->required()
                    ->disabled(fn ($record) => $record?->status === WithdrawalStatus::PAID
                    ),
                TextInput::make('payment_reference_number')
                    ->maxLength(100)
                    ->visible(fn ($get) => $get('status') === WithdrawalStatus::PAID->value)
                    ->required(fn ($get) => $get('status') === WithdrawalStatus::PAID->value),
                Textarea::make('note')
                    ->visible(fn ($get) => $get('status') !== WithdrawalStatus::PENDING->value)
                    ->required(fn ($get) => $get('status') === WithdrawalStatus::REJECTED->value),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('withdraw_no')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label(label: 'Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency_code')
                    ->label('Currency')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('previous_amount')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(function (WithdrawalStatus $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (WithdrawalStatus $state) => $state->getBadgeColor())
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => ! in_array($record->status, [WithdrawalStatus::PAID]))
                    ->using(function (Model $record, array $data): Model {

                        $transactions = [];

                        if ($data['status'] === WithdrawalStatus::APPROVED->value) {
                            $userWalletService = app(UserWalletService::class);
                            $approvedIds = $userWalletService->markWalletTransactionToApproved($record->user_id, $record->amount);

                            $data['approved_at'] = now();
                            $data['used_transactions'] = json_encode($approvedIds);
                        }

                        if ($data['status'] === WithdrawalStatus::PAID->value) {
                            $userWalletService = app(UserWalletService::class);
                            $transactions = $userWalletService->markWalletTransactionToPaid(
                                $record->user_id,
                                $record->amount
                            );

                            $transactions = $transactions->toArray();

                            $data['paid_at'] = now();

                            $userWalletService->addWithdrawalWalletTransaction(
                                $record->user_id,
                                $record->id,
                                $record->amount
                            );
                        }

                        // Update record
                        $record->update($data);

                        SendWithdrawalRequestStatusChangeEmailJob::dispatch($record->id, $transactions);

                        return $record;
                    })
                    ->slideOver(),
                Tables\Actions\ViewAction::make()
                    ->visible(fn ($record) => in_array($record->status, [WithdrawalStatus::PAID]))
                    ->slideOver(),
            ])
            ->defaultSort('id', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageUserWithdrawals::route('/'),
        ];
    }
}
