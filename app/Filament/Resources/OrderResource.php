<?php

namespace App\Filament\Resources;

use App\Enums\GenderType;
use App\Enums\OrderStatus;
use App\Enums\UserType;
use App\Filament\Resources\OrderResource\Actions\ReassignOrderAction;
use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers\StatusHistoriesRelationManager;
use App\Filament\Resources\OrderResource\RelationManagers\TransactionsRelationManager;
use App\Models\Order;
use App\Traits\NavigationBadgeTrait;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

class OrderResource extends Resource
{
    use NavigationBadgeTrait;

    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        $lang = app()->getLocale();

        return $form
            ->schema([
                Forms\Components\Section::make('Order Details')->schema([
                    TextInput::make('order_no')
                        ->disabled()
                        ->dehydrated(false)
                        ->visible(fn ($record) => $record != null),

                    TextInput::make('buyer')
                        ->disabled()
                        ->formatStateUsing(fn ($state, $record) => $record->buyer->name),

                    TextInput::make('seller')
                        ->disabled()
                        ->formatStateUsing(fn ($state, $record) => $record->seller->name),

                    TextInput::make('ticket_num')
                        ->label('Ticket Number')
                        ->disabled()
                        ->formatStateUsing(fn ($state, $record) => $record->ticket?->ticket_no),
                    TextInput::make('event_name')
                        ->label('Event Name')
                        ->disabled()
                        ->formatStateUsing(fn ($state, $record) => $record->order_meta_data?->event->name->{$lang}),
                    TextInput::make('sector_name')
                        ->label('Sector')
                        ->disabled()
                        ->formatStateUsing(fn ($state, $record) => $record->order_meta_data?->ticket->sector->name),
                    TextInput::make('ticket_rows')
                        ->label('Ticket Rows')
                        ->disabled()
                        ->formatStateUsing(fn ($state, $record) => $record->order_meta_data?->ticket->ticket_rows),
                    TextInput::make('ticket_seats')
                        ->label('Ticket Seats')
                        ->disabled()
                        ->formatStateUsing(fn ($state, $record) => $record->order_meta_data?->ticket->ticket_seats),
                    TextInput::make('quantity')
                        ->numeric()
                        ->disabled(),
                    TextInput::make('currency_code')
                        ->disabled(),
                    TextInput::make('price')
                        ->disabled()
                        ->numeric(),
                    TextInput::make('total_price')
                        ->numeric()
                        ->disabled(),
                    TextInput::make('service_charge_amount')
                        ->disabled()
                        ->numeric(),
                    TextInput::make('tax_amount')
                        ->disabled()
                        ->numeric(),
                    TextInput::make('grand_total')
                        ->disabled()
                        ->numeric(),
                    TextInput::make('purchase_date')
                        ->disabled(),
                    TextInput::make('ticket_downloaded_at')
                        ->disabled(),
                    Textarea::make('description')
                        ->columnSpanFull(),
                ])->columns(2),

                Forms\Components\Section::make('Attendees Details')->schema([

                    Repeater::make('attendees')
                        ->label('')
                        ->relationship('attendees')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('name')
                                        ->required(),
                                    TextInput::make('email')
                                        ->email()
                                        ->required(),
                                    Select::make('gender')
                                        ->options(GenderType::getOptionsWithKeyValuePair())
                                        ->preload()
                                        ->required(),
                                    DatePicker::make('dob')
                                        ->label('Date of Birth')
                                        ->maxDate(now())
                                        ->required(),
                                ]),
                        ])
                        ->itemLabel(static function ($state) {
                            static $i = 0;
                            $i++;

                            return 'Attendee '.$i;
                        })
                        ->defaultItems(0)
                        ->addable(false)
                        ->deletable(false)
                        ->reorderableWithDragAndDrop(false)
                        ->columnSpanFull(),
                ])->collapsible(),

                Forms\Components\Section::make('Order Status')->schema([
                    Select::make('status')
                        ->options(OrderStatus::getOptionsWithKeyValuePair())
                        ->preload()
                        ->reactive()
                        ->default(OrderStatus::PENDING)
                        ->required()
                        ->disabled(fn ($livewire) => $livewire->record?->status === OrderStatus::COMPLETED
                        ),
                    Select::make('penalty_user_type')
                        ->label('Select Penalty User Type')
                        ->live()
                        ->options(UserType::getOptionsWithKeyValuePair(
                            [
                                UserType::BROKER->value,
                                UserType::CUSTOMER->value,
                            ]
                        ))
                        ->required(fn ($get, $livewire) => $get('status') !== $livewire->record?->status->value
                            && in_array($get('status'), [
                                OrderStatus::CANCELED->value,
                            ])
                        )
                        ->visible(fn ($get, $livewire) => $get('status') !== $livewire->record?->status->value
                            && in_array($get('status'), [
                                OrderStatus::CANCELED->value,
                            ])
                        )
                        ->afterStateUpdated(function ($state, callable $set, $livewire) {
                            if ($state === UserType::CUSTOMER->value) {
                                $set('penalty_user_id', $livewire->record?->buyer_id);
                                $set('penalty_amount', round($livewire->record?->service_charge_amount + $livewire->record?->tax_amount, 2));
                            } elseif ($state === UserType::BROKER->value) {
                                $set('penalty_user_id', $livewire->record?->seller_id);
                                $set('penalty_amount', null);
                            } else {
                                $set('penalty_user_id', null);
                                $set('penalty_amount', null);
                            }
                        }),
                    TextInput::make('penalty_amount')
                        ->label('Penalty Amount')
                        ->numeric()
                        ->visible(fn ($get, $livewire) => $get('status') !== $livewire->record?->status->value
                            && in_array($get('status'), [
                                OrderStatus::CANCELED->value,
                            ])
                        )
                        ->required(fn ($get) => $get('penalty_user_type') === UserType::BROKER->value
                        )
                        ->readonly(fn ($get) => $get('penalty_user_type') === UserType::CUSTOMER->value
                        ),
                    Hidden::make('penalty_user_id'),
                    Textarea::make('reason')
                        ->label('Reason')
                        ->required(fn ($get, $livewire) => $get('status') !== $livewire->record?->status->value
                            && in_array($get('status'), [
                                OrderStatus::CANCELED->value,
                                OrderStatus::UNDER_REVIEW->value,
                                OrderStatus::ON_DISPUTE->value,
                            ])
                        )
                        ->visible(fn ($get, $livewire) => $get('status') !== $livewire->record?->status->value
                            && in_array($get('status'), [
                                OrderStatus::CANCELED->value,
                                OrderStatus::UNDER_REVIEW->value,
                                OrderStatus::ON_DISPUTE->value,
                            ])
                        )
                        ->columnSpanFull(),
                ])->columns(2),

                Forms\Components\Section::make('Tickets & Additional Document')->schema([
                    SpatieMediaLibraryFileUpload::make('tickets')
                        ->collection('tickets')
                        ->multiple()
                        ->maxFiles(fn ($livewire) => $livewire->record?->quantity ?? 1)
                        ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'])
                        ->maxSize(5120)
                        ->columnSpanFull()
                        ->required(fn ($livewire) => ($livewire->record?->getMedia('tickets')->isEmpty() ?? true)
                        )
                        ->visible(fn ($livewire) => $livewire->record?->status !== OrderStatus::COMPLETED
                        )
                        ->disk('admin'),

                    SpatieMediaLibraryFileUpload::make('additional_doc')
                        ->label('Additional Document')
                        ->collection('additional_doc')
                        ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'])
                        ->maxSize(5120)
                        ->columnSpanFull()
                        ->disk('admin')
                        ->visible(fn ($livewire) => $livewire->record?->status !== OrderStatus::COMPLETED
                        ),

                    Forms\Components\Placeholder::make('download_tickets')
                        ->label('Download Tickets')
                        ->content(fn ($record) => $record->getMedia('tickets')->isNotEmpty()
                                ? new HtmlString(
                                    collect($record->getMedia('tickets'))->map(function ($media) {
                                        return '<a href="'.$media->getFullUrl().'" target="_blank" class="text-primary underline">'
                                            .e($media->file_name).
                                            '</a>';
                                    })->implode('<br>')
                                )
                                : 'No files uploaded'
                        )
                        ->columnSpanFull(),

                    Forms\Components\Placeholder::make('download_additiona_doc')
                        ->label('Download Additional Document')
                        ->content(fn ($record) => $record->getMedia('additional_doc')->isNotEmpty()
                                ? new HtmlString(
                                    collect($record->getMedia('additional_doc'))->map(function ($media) {
                                        return '<a href="'.$media->getFullUrl().'" target="_blank" class="text-primary underline">'
                                            .e($media->file_name).
                                            '</a>';
                                    })->implode('<br>')
                                )
                                : 'No files uploaded'
                        )
                        ->columnSpanFull(),
                ])->visible(fn ($get, $livewire) => in_array(
                    $get('status') ?? $livewire->record?->status->value,
                    [
                        OrderStatus::SHIPPED->value,
                        OrderStatus::ON_DISPUTE->value,
                        OrderStatus::COMPLETED->value,
                    ],
                    true
                )),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_no')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('buyer.name')
                    ->numeric()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('seller.name')
                    ->numeric()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('ticket.ticket_no')
                    ->numeric()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('ticket.price')
                    ->label('Price')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_price')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(function (OrderStatus $state) {
                        return $state->getLabel();
                    })
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state->getBadgeColour()),
                Tables\Columns\TextColumn::make('purchase_date')
                    ->date()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(OrderStatus::getOptionsWithKeyValuePair())
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => ! in_array($record->status, [OrderStatus::COMPLETED, OrderStatus::CANCELED, OrderStatus::EXPIRED])),
                Tables\Actions\ViewAction::make()
                    ->visible(fn ($record) => in_array($record->status, [OrderStatus::COMPLETED, OrderStatus::CANCELED, OrderStatus::EXPIRED])),
                ReassignOrderAction::make(),
                Tables\Actions\Action::make('reassigned')
                    ->label('Reassigned')
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->tooltip(fn ($record) => $record->parent_id
                            ? 'Reassigned from Order #'.$record->parent->order_no
                            : ($record->child
                                ? 'Reassigned to Order #'.$record->child->order_no
                                : null)
                    )
                    ->visible(fn ($record) => filled($record->parent_id) || filled($record->child)
                    ),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            StatusHistoriesRelationManager::class,
            TransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOrders::route('/'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'view' => Pages\ViewOrder::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['child:id,order_no,parent_id', 'parent:id,order_no,parent_id'])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
