<?php

namespace App\Filament\Resources;

use AmidEsfahani\FilamentTinyEditor\TinyEditor;
use App\Enums\EmailTemplateType;
use App\Filament\Resources\EmailTemplateResource\Pages;
use App\Models\EmailTemplate;
use App\Models\Language;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class EmailTemplateResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = EmailTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-inbox-stack';

    protected static ?string $navigationGroup = 'Content Management';

    protected static ?string $modelLabel = 'Email & Notification Template';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)->orderBy('locale', 'ASC')->get();

        return $form->schema([
            TextInput::make('template_key')
                ->required()
                ->unique(ignoreRecord: true)
                ->maxLength(50)
                ->disabled(fn ($record) => $record !== null),

            Select::make('template_type')
                ->label('Select Template Type')
                ->options(EmailTemplateType::getOptionsWithKeyValuePair())
                ->required()
                ->disabled(fn ($record) => $record !== null),

            Textarea::make('template_purpose')
                ->required()
                ->maxLength(200)
                ->columnSpanFull(),

            // Wrapping Tabs inside a Section for Full Width
            Section::make('Contents')
                ->schema([
                    Tabs::make('content_tabs')
                        ->columnSpanFull()
                        ->tabs(
                            collect($languages)->map(function ($language, $key) {
                                return Tabs\Tab::make($language->name)
                                    ->schema([
                                        Hidden::make("translations.$key.locale")
                                            ->default($language->locale),
                                        TextInput::make("translations.$key.subject")
                                            ->label('Subject')
                                            ->required()
                                            ->maxLength(255)
                                            ->rules(function ($get, $record) use ($language) {
                                                return [
                                                    Rule::unique(self::getEmailTemplateTransTable(), 'subject')
                                                        ->where('locale', $language->locale)
                                                        ->ignore($record?->id, 'email_template_id'),
                                                ];
                                            }),

                                        TinyEditor::make("translations.$key.body")
                                            ->label('Body')
                                            ->toolbarMode('wrap')
                                            ->required(),
                                    ]);
                            })->toArray()
                        ),
                ])
                ->columnSpanFull(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('subject')
                    ->label('Subject')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('template_type')
                    ->label(label: 'Template Type')
                    ->formatStateUsing(function (EmailTemplateType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (EmailTemplateType $state) => $state->getBadgeColour())
                    ->searchable()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations->sortBy('locale');

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {
                        $translations = $data['translations'];
                        unset($data['translations']);

                        // Update the main record
                        $record->update($data);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['email_template_id'] = $record->id;

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert(
                            $translations,
                            ['email_template_id', 'locale'],
                            ['subject', 'body']
                        );

                        return $record;
                    })
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false)
                    ->slideOver(),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageEmailTemplates::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getEmailTemplateTable().'.*', self::getEmailTemplateTransTable().'.subject')
            ->leftJoin(self::getEmailTemplateTransTable(), self::getEmailTemplateTable().'.id', '=', self::getEmailTemplateTransTable().'.email_template_id')
            ->where(self::getEmailTemplateTransTable().'.locale', app()->getLocale())
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
