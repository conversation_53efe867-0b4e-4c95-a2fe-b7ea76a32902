<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SeasonResource\Pages;
use App\Models\Language;
use App\Models\Season;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class SeasonResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = Season::class;

    protected static ?string $navigationIcon = 'heroicon-o-cloud';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)
            ->orderBy('locale', 'ASC')->get();

        return $form->schema([

            Section::make('Contents')
                ->schema([
                    Tabs::make('content_tabs')
                        ->columnSpanFull() // Ensure full width
                        ->tabs(
                            collect($languages)->map(function ($language, $key) {
                                return Tabs\Tab::make($language->name)
                                    ->schema([
                                        Hidden::make("translations.$key.locale")
                                            ->default($language->locale),
                                        TextInput::make("translations.$key.name")
                                            ->label('Name')
                                            ->required()
                                            ->maxLength(255)
                                            ->rules(function ($get, $record) use ($language) {
                                                return [
                                                    Rule::unique(self::getSeasonTransTable(), 'name')
                                                        ->where('locale', $language->locale)
                                                        ->ignore($record?->id, 'season_id'),
                                                ];
                                            }),

                                        TextInput::make("slugs.$language->locale")
                                            ->label('Slug')
                                            ->required()
                                            ->maxLength(255)
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Set $set, $state) use ($language) {
                                                $set("slugs.{$language->locale}", Str::slug($state));
                                            })
                                            ->rules(function ($get, $record) use ($language) {
                                                return [
                                                    Rule::unique(self::getSlugTable(), 'slug')
                                                        ->where(function ($query) use ($language, $record) {
                                                            $query->where('locale', $language->locale);

                                                            if ($record) {
                                                                $query->where(function ($q) use ($record) {
                                                                    $q->where('sluggable_id', '!=', $record->id)
                                                                        ->orWhere('sluggable_type', '!=', get_class($record));
                                                                });
                                                            }
                                                        }),
                                                ];
                                            }),
                                    ]);
                            })->toArray()
                        ),
                ])
                ->columnSpanFull(),
            Toggle::make('is_published')
                ->required()
                ->disabled(fn ($record) => $record && $record->is_published && $record->leagues_count > 0)
                ->helperText(function ($record) {
                    return $record && $record->is_published && $record->leagues_count > 0
                        ? 'This Season cannot be deactivated while it has active leagues'
                        : null;
                })
                ->inline(false),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('slug')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_published')
                    ->disabled(fn ($record) => $record->is_published && $record->leagues_count > 0)
                    ->tooltip(function ($record) {
                        return $record->is_published && $record->leagues_count > 0
                            ? 'This Season cannot be deactivated while it has active leagues'
                            : null;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations->sortBy('locale');
                        $data['slugs'] = $record->slugs->pluck('slug', 'locale')->toArray();

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {
                        $slugs = $data['slugs'];
                        $translations = $data['translations'];

                        unset($data['slugs'], $data['translations']);

                        // Update the main season record
                        $record->update($data);

                        $record->setSlugs($slugs);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['season_id'] = $record->id;
                            $translation['updated_at'] = now();

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert($translations, ['season_id', 'locale'], ['name', 'updated_at']);

                        return $record;
                    })
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->leagues_count === 0),
                Tables\Actions\Action::make('info')
                    ->label('Restricted')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->visible(fn ($record) => $record->leagues_count > 0)
                    ->tooltip('This Season cannot be deleted while it has active leagues'),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->checkIfRecordIsSelectableUsing(
                fn ($record): bool => $record->leagues_count === 0,
            )
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSeasons::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getSeasonTable().'.*', self::getSeasonTransTable().'.name', self::getSlugTable().'.slug')
            ->leftJoin(self::getSeasonTransTable(), function ($join) {
                $join->on(self::getSeasonTable().'.id', '=', self::getSeasonTransTable().'.season_id')
                    ->where(self::getSeasonTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getSlugTable(), function ($join) {
                $join->on(self::getSeasonTable().'.id', '=', self::getSlugTable().'.sluggable_id')
                    ->where(self::getSlugTable().'.sluggable_type', '=', Season::class)
                    ->where(self::getSlugTable().'.locale', app()->getLocale());
            })
            ->withCount(['leagues' => function ($query) {
                $query->where('is_published', 1);
            }])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
