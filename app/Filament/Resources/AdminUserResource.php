<?php

namespace App\Filament\Resources;

use App\Enums\UserType;
use App\Filament\Resources\AdminUserResource\Pages;
use App\Models\User;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Spatie\Permission\Models\Role;

class AdminUserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'User Mangement';

    protected static ?string $modelLabel = 'Admin User';

    public static function canAccess(): bool
    {
        return auth()->user()->can('view_any_admin::user');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Full Name')
                    ->required()
                    ->maxLength(255),
                TextInput::make('user_name')
                    ->label('Username')
                    ->unique(ignoreRecord: true)
                    ->required()
                    ->maxLength(255),
                TextInput::make('email')
                    ->label('Email Address')
                    ->email()
                    ->unique(ignoreRecord: true)
                    ->required(),
                Hidden::make('user_type')
                    ->default(UserType::ADMIN->value),
                Select::make('roles')
                    ->label('Role')
                    ->options(Role::where('name', '!=', UserType::SUPERADMIN->getLabel())->pluck('name', 'name'))
                    ->preload()
                    ->required()
                    ->multiple(false),
                TextInput::make('password')
                    ->label('Password')
                    ->password()
                    ->confirmed()
                    ->rule(Password::min(8)->mixedCase()->numbers()->symbols())
                    ->dehydrateStateUsing(fn ($state) => ! empty($state) ? Hash::make($state) : null)
                    ->required(fn ($record) => $record === null),

                TextInput::make('password_confirmation')
                    ->label('Confirm Password')
                    ->password()
                    ->dehydrated(false)
                    ->required(fn ($record) => $record === null),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(label: 'Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user_name')
                    ->label(label: 'User Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(label: 'Email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->label(label: 'Role')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();
                        $data['roles'] = $record->roles->pluck('name');

                        return $data;
                    })
                    ->mutateFormDataUsing(function (array $data): array {
                        if ($data['password'] === null) {
                            unset($data['password']);
                        }

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {
                        $roles = $data['roles'];
                        unset($data['roles']);

                        // Update the main user record
                        $record->update($data);

                        // Save role data
                        if (! empty($roles)) {
                            $record->syncRoles($roles);
                        }

                        return $record;
                    })
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false)
                    ->slideOver(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->defaultSort('created_at', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageAdminUsers::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_type', UserType::ADMIN->value)
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::query()
            ->where('created_at', '>=', now()->subDay())
            ->where('user_type', UserType::ADMIN->value)
            ->count();
    }
}
