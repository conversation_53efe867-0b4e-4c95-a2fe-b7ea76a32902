<?php

namespace App\Filament\Resources\TicketResource\Pages;

use App\Filament\Resources\TicketResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageTickets extends ManageRecords
{
    protected static string $resource = TicketResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $translations = $data['translations'];
                    $restrictions = $data['restrictions'];
                    unset($data['translations'], $data['restrictions']);

                    // Save ticket and translations
                    $ticket = static::getModel()::create($data);
                    $ticket->translations()->createMany($translations);

                    if (! empty($restrictions)) {
                        $ticket->restrictions()->sync($restrictions);
                    }

                    return $ticket;
                })
                ->closeModalByClickingAway(false)
                ->closeModalByEscaping(false)
                ->slideOver(),
        ];
    }
}
