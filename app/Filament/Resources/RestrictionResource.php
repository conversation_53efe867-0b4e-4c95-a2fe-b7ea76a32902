<?php

namespace App\Filament\Resources;

use App\Enums\RestrictionType;
use App\Filament\Resources\RestrictionResource\Pages;
use App\Models\Language;
use App\Models\Restriction;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class RestrictionResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = Restriction::class;

    protected static ?string $navigationIcon = 'heroicon-o-no-symbol';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)->get();

        return $form->schema([
            Select::make('type')
                ->label('Select Restriction Type')
                ->required()
                ->options(RestrictionType::getOptionsWithKeyValuePair()),
            Toggle::make('is_active')
                ->required()
                ->default(true)
                ->disabled(fn ($record) => $record && $record->is_active && ($record->events_count > 0 || $record->tickets_count > 0))
                ->helperText(function ($record) {
                    return $record && $record->is_active && ($record->events_count > 0 || $record->tickets_count > 0)
                        ? 'This Restriction cannot be deactivated while it is associated with Events or Tickets'
                        : null;
                })
                ->inline(false),

            // Wrapping Tabs inside a Section for Full Width
            Section::make('Contents')
                ->schema([
                    Tabs::make('content_tabs')
                        ->columnSpanFull() // Ensure full width
                        ->tabs(
                            collect($languages)->map(function ($language, $key) {
                                return Tabs\Tab::make($language->name)
                                    ->schema([
                                        Hidden::make("translations.$key.locale")
                                            ->default($language->locale),
                                        TextInput::make("translations.$key.name")
                                            ->label('Name')
                                            ->required()
                                            ->maxLength(255)
                                            ->rules(function ($get, $record) use ($language) {
                                                return [
                                                    Rule::unique(self::getRestrictionTransTable(), 'name')
                                                        ->where('locale', $language->locale)
                                                        ->ignore($record?->id, 'restriction_id'),
                                                ];
                                            }),
                                    ]);
                            })->toArray()
                        ),
                ])
                ->columnSpanFull(), // Ensure full width for the section
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(function (RestrictionType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (RestrictionType $state) => $state->getBadgeColour())
                    ->sortable()
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->disabled(fn ($record) => $record->is_active && ($record->events_count > 0 || $record->tickets_count > 0))
                    ->tooltip(function ($record) {
                        return $record->is_active && ($record->events_count > 0 || $record->tickets_count > 0)
                            ? 'This Restriction cannot be deactivated while it is associated with Events or Tickets'
                            : null;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations;

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {
                        $translations = $data['translations'];
                        unset($data['translations']);

                        // Update the main season record
                        $record->update($data);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['restriction_id'] = $record->id;
                            $translation['updated_at'] = now();

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert(
                            $translations,
                            ['restriction_id', 'locale'],
                            ['name', 'updated_at']
                        );

                        return $record;
                    })
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->events_count === 0 && $record->tickets_count === 0),

                Tables\Actions\Action::make('info')
                    ->label('Restricted')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->visible(fn ($record) => ($record->events_count > 0 || $record->tickets_count > 0))
                    ->tooltip('This Restriction cannot be deleted while it is associated with Events or Tickets'),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->checkIfRecordIsSelectableUsing(
                fn ($record): bool => $record->events_count === 0 && $record->tickets_count === 0,
            )
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRestrictions::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getRestrictionTable().'.*', self::getRestrictionTransTable().'.name')
            ->leftJoin(self::getRestrictionTransTable(), self::getRestrictionTable().'.id', '=', self::getRestrictionTransTable().'.restriction_id')
            ->where(self::getRestrictionTransTable().'.locale', app()->getLocale())
            ->withCount('events')
            ->withCount('tickets')
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
