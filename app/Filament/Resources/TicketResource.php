<?php

namespace App\Filament\Resources;

use App\Enums\CurrencyType;
use App\Enums\RestrictionType;
use App\Enums\TicketQuantitySplitType;
use App\Enums\TicketType;
use App\Filament\Resources\TicketResource\Pages;
use App\Models\Language;
use App\Models\Restriction;
use App\Models\StadiumSector;
use App\Models\Ticket;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class TicketResource extends Resource
{
    use NavigationBadgeTrait, TableNameTrait;

    protected static ?string $model = Ticket::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {

        $languages = Language::select('name', 'locale')->where('is_active', 1)->get();

        return $form
            ->schema([
                TextInput::make('ticket_no')
                    ->disabled()
                    ->dehydrated(false)
                    ->visible(fn ($record) => $record !== null),
                Select::make('event_id')
                    ->relationship(
                        name: 'event',
                        titleAttribute: self::getEventTransTable().'.name',
                        modifyQueryUsing: fn ($query) => $query
                            ->leftJoin(self::getEventTransTable(), self::getEventTable().'.id', '=', self::getEventTransTable().'.event_id')
                            ->where('is_published', 1)
                            ->where(self::getEventTransTable().'.locale', app()->getLocale())
                            ->select([self::getEventTable().'.id', self::getEventTransTable().'.name'])
                    )
                    ->preload()
                    ->searchable()
                    ->live()
                    ->disabled(fn ($record) => $record !== null)
                    ->required(),
                Select::make('seller_id')
                    ->relationship('seller', 'name')
                    ->preload()
                    ->searchable()
                    ->disabled(fn ($record) => $record !== null)
                    ->required(),
                Section::make('Description')
                    ->schema([
                        Tabs::make('description_tabs')
                            ->columnSpanFull()
                            ->tabs(
                                collect($languages)->map(function ($language, $key) {
                                    return Tabs\Tab::make($language->name)
                                        ->schema([
                                            Hidden::make("translations.$key.id")
                                                ->visible(fn ($record) => $record !== null),
                                            Hidden::make("translations.$key.locale")
                                                ->default($language->locale),
                                            Textarea::make("translations.$key.description")
                                                ->label('Description')
                                                ->required()
                                                ->rules(function ($get, $record) use ($language) {
                                                    return [
                                                        Rule::unique(self::getTicketTransTable(), 'description')
                                                            ->where('locale', $language->locale)
                                                            ->ignore($record?->id, 'ticket_id'),
                                                    ];
                                                })
                                                ->columnSpanFull(),
                                        ]);
                                })->toArray()
                            ),
                    ]),
                TextInput::make('price')
                    ->required()
                    ->numeric()
                    ->inputMode('decimal')
                    ->minValue(0)
                    ->maxValue(config('services.ticketgol.max_price_limit')),
                TextInput::make('face_value_price')
                    ->label('Face Value Price')
                    ->required()
                    ->numeric()
                    ->inputMode('decimal')
                    ->minValue(0)
                    ->maxValue(config('services.ticketgol.max_price_limit')),
                TextInput::make('quantity')
                    ->required()
                    ->numeric()
                    ->minValue(fn ($record) => $record?->reservations_sum_quantity ?? 1)
                    ->maxValue(config('services.ticketgol.max_quantity_per_ticket'))
                    ->helperText(function ($record) {
                        return $record && $record->reservations_sum_quantity
                            ? 'There are currently '.$record->reservations_sum_quantity.' tickets reserved. You cannot reduce the quantity below this.'
                            : null;
                    }),
                Toggle::make('is_active')
                    ->inline(false)
                    ->required()
                    ->disabled(fn ($record) => $record && $record->is_active && ($record->orders_count > 0 || $record->reservations_count > 0))
                    ->helperText(function ($record) {
                        return $record && $record->is_active && $record->orders_count > 0
                            ? 'This Ticket cannot be deactivated while it has some orders or active reservations'
                            : null;
                    }),
                Select::make('currency_code')
                    ->required()
                    ->options(CurrencyType::getOptionsWithKeyValuePair())
                    ->default(CurrencyType::EUR->value)
                    ->disabled()
                    ->dehydrated(true)
                    ->searchable(),
                Select::make('sector_id')
                    ->options(function (Get $get) {
                        $eventId = $get('event_id');

                        if (! $eventId) {
                            return [];
                        }

                        return StadiumSector::query()
                            ->with('parent:id,name')
                            ->join('event_stadium_sectors', 'stadium_sectors.id', '=', 'event_stadium_sectors.stadium_sector_id')
                            ->where('event_stadium_sectors.event_id', $eventId)
                            ->select(['stadium_sectors.id', 'stadium_sectors.name', 'stadium_sectors.parent_id'])
                            ->get()
                            ->mapWithKeys(function ($sector) {
                                $label = $sector->parent
                                    ? "{$sector->parent->name} - {$sector->name}"
                                    : $sector->name;

                                return [$sector->id => $label];
                            });
                    })
                    ->searchable()
                    ->preload()
                    ->reactive()
                    ->disabled(fn (Get $get, $record) => ! filled($get('event_id')) ||
                        ($record && $record->orders_count > 0)
                    )
                    ->required(),
                TextInput::make('ticket_rows')
                    ->maxLength(100)
                    ->helperText('Add comma separated rows numbers'),
                TextInput::make('ticket_seats')
                    ->maxLength(200)
                    ->helperText('Add comma separated seats numbers'),

                Section::make('Ticketing')
                    ->schema([
                        Radio::make('quantity_split_type')
                            ->label('Do you want to sell all your tickets together?')
                            ->required()
                            ->options(TicketQuantitySplitType::getOptionsWithKeyValuePair())
                            ->default(TicketQuantitySplitType::ANY->value)
                            ->columnSpanFull(),
                        Radio::make('ticket_type')
                            ->required()
                            ->options(TicketType::getOptionsWithKeyValuePair())
                            ->columnSpanFull(),
                    ]),
                Section::make('Restrictions')
                    ->schema([
                        CheckboxList::make('restrictions')
                            ->relationship('restrictions', self::getRestrictionTransTable().'.name')
                            ->options(fn (Get $get) => Restriction::where('type', RestrictionType::TICKET->value)
                                ->leftJoin(self::getRestrictionTransTable(), self::getRestrictionTable().'.id', '=', self::getRestrictionTransTable().'.restriction_id')
                                ->where('is_active', 1)
                                ->where(self::getRestrictionTransTable().'.locale', app()->getLocale())
                                ->pluck(self::getRestrictionTransTable().'.name', self::getRestrictionTransTable().'.restriction_id'))
                            ->searchable()
                            ->columns(2)
                            ->gridDirection('row')
                            ->extraAttributes([
                                'style' => 'max-height: 300px !important; overflow-y: scroll !important;',
                            ])
                            ->bulkToggleable()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('ticket_no')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('seller.name')
                    ->label('Seller')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('event_name')
                    ->label('Event')
                    ->sortable()
                    ->searchable(self::getEventTransTable().'.name'),
                Tables\Columns\TextColumn::make('price')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('ticket_type')
                    ->formatStateUsing(function (TicketType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (TicketType $state) => $state->getBadgeColour())
                    ->searchable(),
                Tables\Columns\TextColumn::make('currency_code')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('sector.name')
                    ->label('Sector')
                    ->formatStateUsing(function ($state, $record) {
                        if (! $record->sector) {
                            return '-';
                        }

                        $parentName = $record->sector->parent?->name;
                        $childName = $record->sector->name;

                        return $parentName ? "{$parentName} - {$childName}" : $childName;
                    })
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->disabled(fn ($record) => $record->is_active && ($record->orders_count > 0 || $record->reservations_count > 0))
                    ->tooltip(function ($record) {
                        return $record->is_active && $record->orders_count > 0
                            ? 'This Ticket cannot be deactivated while it has some orders or active reservations'
                            : null;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->using(function (Model $record, array $data): Model {
                        $translations = $data['translations'];
                        unset($data['translations']);

                        // Update the main season record
                        $record->update($data);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['ticket_id'] = $record->id;
                            $translation['updated_at'] = now();

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert(
                            $translations,
                            ['ticket_id', 'locale'],
                            ['description', 'updated_at']
                        );

                        return $record;
                    })

                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations;

                        return $data;
                    })
                    ->slideOver()
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false),

                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->orders_count === 0 && $record->reservations_count === 0),
                Tables\Actions\Action::make('info')
                    ->label('Restricted')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->visible(fn ($record) => ($record->orders_count > 0 || $record->reservations_count > 0))
                    ->tooltip('This Ticket cannot be deleted while it has some orders or active reservations'),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->defaultSort('created_at', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->checkIfRecordIsSelectableUsing(
                fn ($record): bool => $record->orders_count === 0,
            );
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTickets::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $ticketTable = self::getTicketTable();
        $eventTable = self::getEventTable();
        $eventTransTable = self::getEventTransTable();

        return parent::getEloquentQuery()
            ->select([
                "{$ticketTable}.*",
                "{$eventTransTable}.name as event_name",
            ])
            ->leftJoin($eventTable, "{$ticketTable}.event_id", '=', "{$eventTable}.id")
            ->leftJoin($eventTransTable, "{$eventTable}.id", '=', "{$eventTransTable}.event_id")
            ->where("{$eventTransTable}.locale", app()->getLocale())
            ->with(['sector:id,name,parent_id', 'sector.parent:id,name'])
            ->withCount(['orders'])
            ->withCount(['reservations' => function ($query) {
                $query->reserved();
            }])
            ->withSum(['reservations' => function ($query) {
                $query->reserved();
            }], 'quantity')
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
