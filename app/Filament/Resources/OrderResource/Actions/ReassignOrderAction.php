<?php

namespace App\Filament\Resources\OrderResource\Actions;

use App\Enums\OrderStatus;
use App\Enums\OrderTransactionStatus;
use App\Enums\OrderTransactionType;
use App\Enums\UserType;
use App\Enums\WalletTransactionType;
use App\Jobs\SendOrderReassignEmailJob;
use App\Jobs\UpdateOrderMetaDataJob;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\User;
use App\Services\UserWalletService;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\DB;

class ReassignOrderAction extends Action
{
    public static function make(?string $name = 'reassignOrder'): static
    {
        return parent::make($name)
            ->label('Reassign')
            ->color('danger')
            ->icon('heroicon-o-arrow-path')
            ->slideOver()
            ->form(function ($record) {
                $lang = app()->getLocale();

                return [
                    Forms\Components\Grid::make(2)->schema([
                        Select::make('new_seller_id')
                            ->options(function () use ($record) {
                                return User::whereNotIn('id', [$record->buyer_id, $record->seller_id])
                                    ->pluck('name', 'id');
                            })
                            ->preload()
                            ->reactive()
                            ->searchable()
                            ->required(),
                        Select::make('new_ticket_id')
                            ->options(function (Get $get) use ($record) {
                                $seller_id = $get('new_seller_id');

                                return Ticket::where('is_active', true)
                                    ->where('event_id', $record->ticket->event_id)
                                    ->where('seller_id', $seller_id)
                                    ->leftJoin('ticket_reservations as tr', function ($join) {
                                        $join->on('tr.ticket_id', '=', 'tickets.id')
                                            ->whereIn('tr.status', ['active', 'processing']);
                                    })
                                    ->select('tickets.id', 'tickets.ticket_no', DB::raw('(tickets.quantity - COALESCE(SUM(tr.quantity), 0)) as available_quantity'))
                                    ->groupBy('tickets.id', 'tickets.ticket_no', 'tickets.quantity')
                                    ->having('available_quantity', '>=', $record->quantity)
                                    ->pluck('ticket_no', 'id');
                            })
                            ->preload()
                            ->searchable()
                            ->live()
                            ->required()
                            ->afterStateUpdated(function ($get, $set) use ($record) {
                                $ticket = Ticket::find($get('new_ticket_id'));
                                $set('new_price', $ticket?->price);
                                $totalPrice = round($ticket->price * $record->quantity, 2);
                                $serviceChargeAmount = round($totalPrice * config('services.ticketgol.service_charge_rate'), 2);
                                $taxAmount = round($serviceChargeAmount * config('services.ticketgol.tax_rate'), 2);
                                $grandTotal = round($totalPrice + $serviceChargeAmount + $taxAmount, 2);
                                $set('new_total_price', number_format($totalPrice, 2));
                                $set('new_service_charge_amount', number_format($serviceChargeAmount, 2));
                                $set('new_tax_amount', number_format($taxAmount, 2));
                                $set('new_grand_total', number_format($grandTotal, 2));
                            })
                            ->disabled(fn ($get) => ! $get('new_seller_id')),
                        TextInput::make('event_name')
                            ->label('Event Name')
                            ->disabled()
                            ->formatStateUsing(fn ($state, $record) => $record->order_meta_data?->event->name->{$lang}),
                        TextInput::make('quantity')
                            ->numeric()
                            ->formatStateUsing(fn ($state, $record) => $record->quantity)
                            ->disabled(),
                        TextInput::make('currency_code')
                            ->formatStateUsing(fn ($state, $record) => $record->currency_code)
                            ->disabled(),
                        TextInput::make('old_price')
                            ->disabled()
                            ->formatStateUsing(fn ($state, $record) => $record->price),
                        TextInput::make('old_total_price')
                            ->disabled()
                            ->formatStateUsing(fn ($state, $record) => $record->total_price),
                        TextInput::make('old_service_charge_amount')
                            ->disabled()
                            ->formatStateUsing(fn ($state, $record) => $record->service_charge_amount),
                        TextInput::make('old_tax_amount')
                            ->disabled()
                            ->formatStateUsing(fn ($state, $record) => $record->tax_amount),
                        TextInput::make('old_grand_total')
                            ->label('Old Grand Total')
                            ->disabled()
                            ->formatStateUsing(fn ($state, $record) => $record->grand_total),
                        TextInput::make('new_price')
                            ->disabled()
                            ->numeric(),
                        TextInput::make('new_total_price')
                            ->numeric()
                            ->disabled(),
                        TextInput::make('new_service_charge_amount')
                            ->disabled()
                            ->numeric(),
                        TextInput::make('new_tax_amount')
                            ->disabled()
                            ->numeric(),
                        TextInput::make('new_grand_total')
                            ->disabled()
                            ->numeric(),
                        TextInput::make('penalty_amount')
                            ->required()
                            ->numeric(),
                        Textarea::make('reason')
                            ->required()
                            ->columnSpanFull(),
                    ]),
                ];
            })
            ->action(function (array $data, $record, $livewire) {
                DB::beginTransaction();
                try {
                    $oldStatus = $record->status;
                    $record->update([
                        'penalty_amount' => $data['penalty_amount'],
                        'penalty_user_id' => $record->seller_id,
                        'penalty_user_type' => UserType::BROKER->value,
                        'status' => OrderStatus::CANCELED,
                    ]);

                    if ($data['penalty_amount'] > 0) {
                        $userWalletService = app(UserWalletService::class);

                        $userWalletService->addOrderWalletTransaction(
                            $record->seller_id,
                            $record->id,
                            $data['penalty_amount'],
                            WalletTransactionType::ORDER_PENALTY
                        );
                    }

                    if ($oldStatus !== OrderStatus::CANCELED) {
                        if ($record->ticket->sold_quantity >= $record->quantity) {
                            $record->ticket->decrement('sold_quantity', $record->quantity);
                            $record->ticket->increment('quantity', $record->quantity);
                        }

                        $record->statusHistories()->create([
                            'from_status' => $oldStatus,
                            'to_status' => OrderStatus::CANCELED,
                            'user_id' => auth()->id(),
                            'reason' => $data['reason'],
                        ]);
                    }

                    $newTicket = Ticket::findOrFail($data['new_ticket_id']);

                    $totalPrice = round($newTicket->price * $record->quantity, 2);
                    $serviceChargeAmount = round($totalPrice * config('services.ticketgol.service_charge_rate'), 2);
                    $taxAmount = round($serviceChargeAmount * config('services.ticketgol.tax_rate'), 2);
                    $grandTotal = round($totalPrice + $serviceChargeAmount + $taxAmount, 2);

                    $newOrder = Order::create([
                        'buyer_id' => $record->buyer_id,
                        'seller_id' => $newTicket->seller_id,
                        'ticket_id' => $newTicket->id,
                        'parent_id' => $record->id,
                        'quantity' => $record->quantity,
                        'currency_code' => $record->currency_code,
                        'price' => $newTicket->price,
                        'total_price' => $totalPrice,
                        'service_charge_amount' => $serviceChargeAmount,
                        'tax_amount' => $taxAmount,
                        'grand_total' => $grandTotal,
                        'status' => OrderStatus::CONFIRMED,
                        'purchase_date' => now(),
                        'created_by' => auth()->id(),
                    ]);

                    $record->attendees->each(function ($attendee) use ($newOrder) {
                        $attendeeData = $attendee->toArray();
                        unset($attendeeData['id']);
                        $newOrder->attendees()->create($attendeeData);
                    });

                    if ($newOrder->ticket->quantity >= $newOrder->quantity) {
                        $newOrder->ticket->decrement('quantity', $newOrder->quantity);
                        $newOrder->ticket->increment('sold_quantity', $newOrder->quantity);
                    }

                    $newOrder->statusHistories()->create([
                        'from_status' => OrderStatus::PENDING,
                        'to_status' => OrderStatus::CONFIRMED,
                        'user_id' => auth()->id(),
                        'reason' => 'Order has been reassigned',
                    ]);

                    $newOrder->transactions()->create([
                        'transaction_type' => OrderTransactionType::PURCHASE,
                        'currency_code' => $newOrder->currency_code,
                        'total_amount' => $newOrder->grand_total,
                        'paid_at' => now(),
                        'status' => OrderTransactionStatus::COMPLETED,
                    ]);

                    UpdateOrderMetaDataJob::dispatch($newOrder->id);
                    SendOrderReassignEmailJob::dispatch($record->id, $newOrder->id);

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                }

                \Filament\Notifications\Notification::make()
                    ->success()
                    ->title('Order reassigned successfully')
                    ->body('New order #'.$newOrder->order_no.' has been created.')
                    ->send();

                $livewire->dispatch('$refresh');
            })
            ->visible(fn ($record) => (! in_array($record->status, [OrderStatus::COMPLETED, OrderStatus::CANCELED, OrderStatus::EXPIRED]) && ! $record->parent_id && ! $record->child && ! $record->tixstock_id));
    }
}
