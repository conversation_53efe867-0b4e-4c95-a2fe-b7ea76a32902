<?php

namespace App\Filament\Resources\OrderResource\RelationManagers;

use App\Enums\OrderTransactionStatus;
use App\Enums\OrderTransactionType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    protected static bool $isLazy = false;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('transaction_type')
                    ->disabled(),
                Forms\Components\TextInput::make('currency_code')
                    ->disabled(),
                Forms\Components\TextInput::make('total_amount')
                    ->disabled(),
                Forms\Components\TextInput::make('payment_intent_id')
                    ->disabled(),
                Forms\Components\TextInput::make('stripe_refund_id')
                    ->disabled(),
                Forms\Components\TextInput::make('payment_method_type')
                    ->disabled(),
                Forms\Components\TextInput::make('card_brand')
                    ->disabled(),
                Forms\Components\TextInput::make('card_last_four')
                    ->disabled(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('transaction_type')
            ->columns([
                Tables\Columns\TextColumn::make('transaction_type')
                    ->formatStateUsing(function (OrderTransactionType $state) {
                        return $state->getLabel();
                    })
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state->getBadgeColour()),
                Tables\Columns\TextColumn::make('currency_code')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('paid_at')
                    ->dateTime()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('refunded_at')
                    ->dateTime()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(function (OrderTransactionStatus $state) {
                        return $state->getLabel();
                    })
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state->getBadgeColour()),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Transaction Details'),
            ]);
    }
}
