<?php

namespace App\Filament\Resources\OrderResource\RelationManagers;

use App\Enums\OrderStatus;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class StatusHistoriesRelationManager extends RelationManager
{
    protected static string $relationship = 'statusHistories';

    protected static bool $isLazy = false;

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('to_status')
            ->columns([

                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable(),
                Tables\Columns\TextColumn::make('from_status')
                    ->formatStateUsing(function (OrderStatus $state) {
                        return $state->getLabel();
                    })
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state->getBadgeColour()),
                Tables\Columns\TextColumn::make('to_status')
                    ->formatStateUsing(function (OrderStatus $state) {
                        return $state->getLabel();
                    })
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state->getBadgeColour()),
                Tables\Columns\TextColumn::make('reason')
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= 40) {
                            return null;
                        }

                        return $state;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->orderByDesc('id'));
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->orderByDesc('id');
    }
}
