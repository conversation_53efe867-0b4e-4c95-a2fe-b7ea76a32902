<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Enums\OrderStatus;
use App\Enums\OrderTransactionType;
use App\Enums\UserType;
use App\Enums\WalletTransactionType;
use App\Filament\Resources\OrderResource;
use App\Jobs\SendOrderStatusUpdateEmailJob;
use App\Repositories\OrderTransactionRepository;
use App\Repositories\SupportRequestRepository;
use App\Services\UserWalletService;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Stripe\StripeClient;

class EditOrder extends EditRecord
{
    protected static string $resource = OrderResource::class;

    protected $orderTransactionRepository;

    protected $supportRequestRepository;

    protected $stripe;

    public function __construct()
    {
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->supportRequestRepository = app(SupportRequestRepository::class);
        $this->userWalletService = app(UserWalletService::class);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    public function getHeading(): string
    {
        return "Edit Order #{$this->record->order_no}";
    }

    /**
     * Handle record update and save translations
     */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {

        if ($data['status'] === OrderStatus::CANCELED->value && $record->parent_id) {
            Notification::make()
                ->title('Status change not allowed')
                ->body('You cannot cancel a reassign order.')
                ->danger()
                ->persistent()
                ->send();

            // Prevent saving the update
            $this->halt();
        }

        $oldStatus = $record->status->value;

        $reason = $data['reason'] ?? null;

        unset($data['reason']);

        // Update the main order record
        $record->update($data);

        if ($oldStatus !== $record->status->value) {
            $record->statusHistories()->create([
                'from_status' => $oldStatus,
                'to_status' => $record->status->value,
                'user_id' => auth()->user()->id,
                'reason' => $reason,
            ]);

            if ($record->status === OrderStatus::CANCELED && $record->purchaseTransaction->payment_intent_id) {
                $refundData = [
                    'payment_intent' => $record->purchaseTransaction->payment_intent_id,
                    'metadata' => [
                        'order_id' => $record->id,
                    ],
                ];

                if ($record->penalty_user_type === UserType::CUSTOMER->value) {
                    $refundData['amount'] = $record->total_price * config('services.ticketgol.stripe_cent_unit');
                }
                $refund = $this->stripe->refunds->create($refundData);

                if ($refund->status === 'pending' || $refund->status === 'succeeded') {
                    $this->orderTransactionRepository->createRefundTransaction(
                        $record,
                        OrderTransactionType::REFUND,
                        $refund
                    );

                    if ($record->ticket->sold_quantity >= $record->quantity) {
                        $record->ticket->decrement('sold_quantity', $record->quantity);
                        $record->ticket->increment('quantity', $record->quantity);
                    }
                }

                if ($record->penalty_user_type === UserType::BROKER->value) {
                    $this->userWalletService->addOrderWalletTransaction(
                        $record->seller_id,
                        $record->id,
                        $data['penalty_amount'],
                        WalletTransactionType::ORDER_PENALTY
                    );
                }
            }

            SendOrderStatusUpdateEmailJob::dispatch($record->id, 'admin');
        }

        $this->redirect(request()->header('Referer'));

        return $record;
    }
}
