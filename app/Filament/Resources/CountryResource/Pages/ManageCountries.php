<?php

namespace App\Filament\Resources\CountryResource\Pages;

use App\Filament\Resources\CountryResource;
use Filament\Resources\Pages\ManageRecords;

class ManageCountries extends ManageRecords
{
    protected static string $resource = CountryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()
            //     ->mutateFormDataUsing(function ($data) {
            //         $data['slug'] = Str::slug($data['slug']);

            //         return $data;
            //     }),
        ];
    }
}
