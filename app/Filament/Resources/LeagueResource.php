<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LeagueResource\Pages;
use App\Models\Language;
use App\Models\League;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class LeagueResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = League::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)
            ->orderBy('locale', 'ASC')->get();

        return $form
            ->schema([
                Select::make('season_id')
                    ->label('Season')
                    ->required()
                    ->relationship('season', self::getSeasonTransTable().'.name', function ($query) {
                        return $query->where('is_published', true)
                            ->leftJoin(self::getSeasonTransTable(), self::getSeasonTable().'.id', '=', self::getSeasonTransTable().'.season_id')
                            ->where(self::getSeasonTransTable().'.locale', app()->getLocale());
                    })
                    ->preload()
                    ->searchable(),
                Select::make('country_id')
                    ->label('Country')
                    ->required()
                    ->relationship('country', self::getCountryTransTable().'.name', function ($query) {
                        return $query->leftJoin(self::getCountryTransTable(), self::getCountryTable().'.id', '=', self::getCountryTransTable().'.country_id')
                            ->where(self::getCountryTransTable().'.locale', app()->getLocale());
                    })
                    ->preload()
                    ->searchable(),
                Toggle::make('is_published')
                    ->required()
                    ->disabled(fn ($record) => $record && $record->is_published && $record->events_count > 0)
                    ->helperText(function ($record) {
                        return $record && $record->is_published && $record->events_count > 0
                            ? 'This League cannot be deactivated while it has active events'
                            : null;
                    })
                    ->inline(false),
                TextInput::make('tixstock_id')
                    ->maxLength(50)
                    ->disabled(fn ($record) => $record && $record->tixstock_id),

                // Wrapping Tabs inside a Section for Full Width
                Section::make('Contents')
                    ->schema([
                        Tabs::make('content_tabs')
                            ->columnSpanFull() // Ensure full width
                            ->tabs(
                                collect($languages)->map(function ($language, $key) {
                                    return Tabs\Tab::make($language->name)
                                        ->schema([
                                            Hidden::make("translations.$key.locale")
                                                ->default($language->locale),
                                            TextInput::make("translations.$key.name")
                                                ->label('Name')
                                                ->required()
                                                ->maxLength(255)
                                                ->rules(function ($get, $record) use ($language) {
                                                    return [
                                                        Rule::unique(self::getLeagueTransTable(), 'name')
                                                            ->where('locale', $language->locale)
                                                            ->ignore($record?->id, 'league_id'),
                                                    ];
                                                }),

                                            TextInput::make("slugs.$language->locale")
                                                ->label('Slug')
                                                ->required()
                                                ->maxLength(255)
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(function (Set $set, $state) use ($language) {
                                                    $set("slugs.{$language->locale}", Str::slug($state));
                                                })
                                                ->rules(function ($get, $record) use ($language) {
                                                    return [
                                                        Rule::unique(self::getSlugTable(), 'slug')
                                                            ->where(function ($query) use ($language, $record) {
                                                                $query->where('locale', $language->locale);

                                                                if ($record) {
                                                                    $query->where(function ($q) use ($record) {
                                                                        $q->where('sluggable_id', '!=', $record->id)
                                                                            ->orWhere('sluggable_type', '!=', get_class($record));
                                                                    });
                                                                }
                                                            }),
                                                    ];
                                                }),
                                            Textarea::make("translations.$key.description")
                                                ->label('Description')
                                                ->maxLength(255)
                                                ->required(),
                                            TextInput::make("translations.$key.meta_title")
                                                ->label('Meta Title')
                                                ->maxLength(255)
                                                ->required(),
                                            TextInput::make("translations.$key.meta_keywords")
                                                ->label('Meta Keywords')
                                                ->maxLength(255)
                                                ->required(),
                                            Textarea::make("translations.$key.meta_description")
                                                ->label('Meta Description')
                                                ->maxLength(255)
                                                ->required(),
                                        ]);
                                })->toArray()
                            ),
                    ])
                    ->columnSpanFull(),

                SpatieMediaLibraryFileUpload::make('image')
                    ->columnSpanFull()
                    ->disk('admin')
                    ->customProperties(fn (Get $get): array => [
                        'alt' => $get('image_alt'),
                    ]),
                TextInput::make('image_alt')
                    ->label('Image Alt Text')
                    ->maxLength(100)
                    ->columnSpanFull(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('league_name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(self::getLeagueTransTable().'.name'),
                Tables\Columns\TextColumn::make('season_name')
                    ->label('Season Name')
                    ->sortable()
                    ->searchable(self::getSeasonTransTable().'.name'),
                Tables\Columns\TextColumn::make('country_name')
                    ->sortable()
                    ->searchable(self::getCountryTransTable().'.name'),
                Tables\Columns\TextColumn::make('slug')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_published')
                    ->disabled(fn ($record) => $record->is_published && $record->events_count > 0)
                    ->tooltip(function ($record) {
                        return $record->is_published && $record->events_count > 0
                            ? 'This League cannot be deactivated while it has active events'
                            : null;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations->sortBy('locale');
                        $data['image_alt'] = optional($record->getFirstMedia())->getCustomProperty('alt');

                        $data['slugs'] = $record->slugs->pluck('slug', 'locale')->toArray();

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {
                        $translations = $data['translations'];
                        $image_alt = $data['image_alt'];
                        $slugs = $data['slugs'];

                        unset($data['translations'], $data['slugs'], $data['image_alt']);

                        // Update the main League record
                        $record->update($data);

                        $record->setSlugs($slugs);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['league_id'] = $record->id;
                            $translation['updated_at'] = now();

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert(
                            $translations,
                            ['league_id', 'locale'],
                            ['name', 'description', 'meta_title', 'meta_description', 'meta_keywords', 'updated_at']
                        );

                        $media = $record->getFirstMedia();
                        if ($media) {
                            $media->setCustomProperty('alt', $image_alt ?? '');
                            $media->save();
                        }

                        return $record;
                    }),

                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->events_count === 0),
                Tables\Actions\Action::make('info')
                    ->label('Restricted')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->visible(fn ($record) => $record->events_count > 0)
                    ->tooltip('This League cannot be deleted while it has active events'),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->checkIfRecordIsSelectableUsing(
                fn ($record): bool => $record->events_count === 0,
            )
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageLeagues::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getLeagueTable().'.*', self::getLeagueTransTable().'.name as league_name', self::getSeasonTransTable().'.name as season_name', self::getCountryTransTable().'.name as country_name', self::getSlugTable().'.slug')

            ->leftJoin(self::getLeagueTransTable(), function ($join) {
                $join->on(self::getLeagueTable().'.id', '=', self::getLeagueTransTable().'.league_id')
                    ->where(self::getLeagueTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getSeasonTransTable(), function ($join) {
                $join->on(self::getLeagueTable().'.season_id', '=', self::getSeasonTransTable().'.season_id')
                    ->where(self::getSeasonTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getCountryTransTable(), function ($join) {
                $join->on(self::getLeagueTable().'.country_id', '=', self::getCountryTransTable().'.country_id')
                    ->where(self::getCountryTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getSlugTable(), function ($join) {
                $join->on(self::getLeagueTable().'.id', '=', self::getSlugTable().'.sluggable_id')
                    ->where(self::getSlugTable().'.sluggable_type', '=', League::class)
                    ->where(self::getSlugTable().'.locale', app()->getLocale());
            })
            ->withCount(['events' => function ($query) {
                $query->where('is_published', 1);
            }])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
