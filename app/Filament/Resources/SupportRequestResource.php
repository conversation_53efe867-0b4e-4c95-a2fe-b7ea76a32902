<?php

namespace App\Filament\Resources;

use App\Enums\MediaLibrary;
use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestStatus;
use App\Enums\SupportRequestType;
use App\Filament\Resources\SupportRequestResource\Pages;
use App\Filament\Resources\SupportRequestResource\RelationManagers\MessagesRelationManager;
use App\Models\SupportRequest;
use App\Models\User;
use App\Traits\NavigationBadgeTrait;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SupportRequestResource extends Resource
{
    use NavigationBadgeTrait;

    protected static ?string $model = SupportRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('user_id')
                    ->required()
                    ->label('User')
                    ->disabled(fn (?SupportRequest $record): bool => $record !== null)
                    ->relationship('user', 'name')
                    ->live()
                    ->afterStateUpdated(
                        fn ($state, callable $set) => $state
                            ? $set('name', User::find($state)?->name)
                            : $set('name', null)
                    )
                    ->afterStateUpdated(
                        fn ($state, callable $set) => $state
                            ? $set('email', User::find($state)?->email)
                            : $set('email', null)
                    ),

                TextInput::make('subject')
                    ->disabled(fn (?SupportRequest $record): bool => $record !== null)
                    ->label('Subject')
                    ->required(),

                Select::make('request_type')
                    ->options(SupportRequestType::getOptionsWithKeyValuePair())
                    ->label('Request Type')
                    ->disabled(fn (?SupportRequest $record): bool => $record !== null)
                    ->required(),

                Select::make('status')
                    ->options(SupportRequestStatus::getOptionsWithKeyValuePair())
                    ->label('Status')
                    ->default(SupportRequestStatus::PENDING)
                    ->required(),

                Select::make('priority')
                    ->options(SupportRequestPriority::getOptionsWithKeyValuePair())
                    ->label('Priority')
                    ->default(SupportRequestPriority::LOW)
                    ->required(),

                Textarea::make('initial_message')
                    ->label('Initial Message')
                    ->required()
                    ->hidden(fn (?SupportRequest $record): bool => $record !== null)
                    ->maxLength(255),

                FileUpload::make('documents')
                    ->label('Documents')
                    ->disk(MediaLibrary::ADMIN_DISK->value)
                    ->multiple()
                    ->required()
                    ->hidden(fn (?SupportRequest $record): bool => $record !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                BadgeableColumn::make('sr_no')
                    ->label('Request No.')
                    ->suffixBadges(function ($record) {
                        return [
                            Badge::make($record->messages_count)
                                ->label($record->messages_count)
                                ->color('success'),
                        ];
                    })
                    ->separator(' ')
                    ->sortable()
                    ->searchable(),
                TextColumn::make(name: 'user.name')
                    ->label('User')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('subject')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('request_type')
                    ->formatStateUsing(function (SupportRequestType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (SupportRequestType $state) => $state->getBadgeColour())
                    ->searchable(),
                TextColumn::make('status')
                    ->formatStateUsing(function (SupportRequestStatus $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (SupportRequestStatus $state) => $state->getBadgeColour())
                    ->searchable(),
                TextColumn::make('priority')
                    ->formatStateUsing(function (SupportRequestPriority $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (SupportRequestPriority $state) => $state->getBadgeColor())
                    ->searchable(),
                TextColumn::make('created_at')->label('Created At')->sortable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options(SupportRequestStatus::getOptionsWithKeyValuePair()),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            MessagesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSupportRequests::route('/'),
            'create' => Pages\CreateSupportRequest::route('/create'),
            'edit' => Pages\EditSupportRequest::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->orderByRaw("CASE
                WHEN priority = 'high' THEN 1
                WHEN priority = 'medium' THEN 2
                WHEN priority = 'low' THEN 3
                ELSE 4
            END")
            ->orderByDesc('created_at')
            ->withCount('messages')
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
