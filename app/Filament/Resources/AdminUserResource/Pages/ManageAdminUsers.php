<?php

namespace App\Filament\Resources\AdminUserResource\Pages;

use App\Filament\Resources\AdminUserResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageAdminUsers extends ManageRecords
{
    protected static string $resource = AdminUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $roles = $data['roles'];
                    unset($data['roles']);

                    // Save User Data
                    $user = static::getModel()::create($data);

                    // Save role data
                    if (! empty($roles)) {
                        $user->syncRoles($roles);
                    }

                    return $user;
                })
                ->closeModalByClickingAway(false)
                ->closeModalByEscaping(false)
                ->slideOver(),
        ];
    }
}
