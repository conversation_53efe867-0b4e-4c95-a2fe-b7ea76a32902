<?php

namespace App\Filament\Resources\EventResource\Pages;

use App\Filament\Resources\EventResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageEvents extends ManageRecords
{
    protected static string $resource = EventResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $slugs = $data['slugs'];
                    $translations = $data['translations'];
                    $sectors = $data['stadiumSectors'];
                    unset($data['slugs'], $data['translations'], $data['stadiumSectors'], $data['image_alt']);

                    // Save event and translations
                    $event = static::getModel()::create($data);

                    $event->setSlugs($slugs);
                    $event->translations()->createMany($translations);
                    $event->stadiumSectors()->sync($sectors);

                    return $event;
                })
                ->closeModalByClickingAway(false)
                ->closeModalByEscaping(false)
                ->slideOver(),
        ];
    }
}
