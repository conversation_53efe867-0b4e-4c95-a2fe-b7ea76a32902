<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Filament\Resources\CustomerResource;
use App\Jobs\SendEmailJob;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\URL;

class ManageCustomers extends ManageRecords
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->after(function ($record) {

                    $verificationUrl = URL::temporarySignedRoute(
                        'verification.verify',
                        now()->addMinutes(60),
                        ['id' => $record->id, 'hash' => sha1($record->email)]
                    );
                    $meta = [
                        'name' => $record->name,
                        'locale' => LanguageCode::ENGLISH->value,
                        'verificationLink' => $verificationUrl,
                    ];
                    // Send registration email notification
                    SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_EMAIL_VERIFICATION->value, $meta, [$record->id]);
                })
                ->closeModalByClickingAway(false)
                ->closeModalByEscaping(false)
                ->slideOver(),
        ];
    }
}
