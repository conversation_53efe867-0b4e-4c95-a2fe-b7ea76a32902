<?php

namespace App\Filament\Resources\CustomerResource\RelationManagers;

use App\Enums\PayoutBankAccountType;
use App\Enums\PayoutMethodType;
use App\Models\Country;
use App\Rules\ValidatePayoutMethodBankField;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PayoutMethodsRelationManager extends RelationManager
{
    protected static string $relationship = 'payoutMethods';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('payout_method_type')
                    ->label('Select Payout Method Type')
                    ->options(PayoutMethodType::getOptionsWithKeyValuePair())
                    ->required()
                    ->reactive(),
                Select::make('country_code')
                    ->label('Select Country')
                    ->options(
                        Country::select('id', 'shortcode')->with(['translation:country_id,name'])
                            ->where('is_published', 1)
                            ->get()
                            ->pluck('translation.name', 'shortcode')->toArray()
                    )
                    ->reactive(),
                Select::make('account_type')
                    ->label('Select Account Type')
                    ->options(PayoutBankAccountType::getOptionsWithKeyValuePair())
                    ->required(),
                TextInput::make('bank_name')
                    ->label('Bank Name')
                    ->required()
                    ->maxLength(100),
                TextInput::make('branch_name')
                    ->label('Branch Name')
                    ->maxLength(100),
                TextInput::make('account_holder_name')
                    ->required()
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(50),
                TextInput::make('account_number')
                    ->required()
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(20),
                TextInput::make('swift_code')
                    ->required()
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(11),
                TextInput::make('iban')
                    ->label('IBAN')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->validationAttribute('IBAN')
                    ->maxLength(32),
                TextInput::make('ifsc_code')
                    ->label('IFSC Code')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->validationAttribute('IFSC Code')
                    ->maxLength(11),
                TextInput::make('routing_number')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(9),
                TextInput::make('sort_code')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(6),
                TextInput::make('bsb')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(6),
                TextInput::make('bank_code')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(5),
                TextInput::make('branch_code')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(6),
                TextInput::make('institution_number')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(3),
                TextInput::make('transit_number')
                    ->rules(function ($get) {
                        $countryCode = $get('country_code');
                        $payoutMethodType = $get('payout_method_type');

                        return [
                            new ValidatePayoutMethodBankField($countryCode, $payoutMethodType),
                        ];
                    })
                    ->maxLength(5),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('payout_method_type')
                    ->label(label: 'Payout Method Type')
                    ->formatStateUsing(function (PayoutMethodType $state) {
                        return $state->getLabel();
                    })
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('country_code')
                    ->label(label: 'Country')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('account_type')
                    ->label(label: 'Account Type')
                    ->formatStateUsing(function (PayoutBankAccountType $state) {
                        return $state->getLabel();
                    })
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('bank_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('account_holder_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('account_number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_default')
                    ->boolean(),
            ])

            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalHeading('Edit Payout Method'),
                Tables\Actions\DeleteAction::make()
                    ->modalHeading('Delete Payout Method'),
            ]);
    }
}
