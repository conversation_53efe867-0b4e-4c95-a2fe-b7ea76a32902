<?php

namespace App\Filament\Resources\CustomerResource\RelationManagers;

use App\Enums\WalletEntryType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Models\UserWallet;
use App\Models\UserWalletTransaction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class WalletTransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'walletTransactions';

    public static string $model = UserWalletTransaction::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('transaction_no')
                    ->required()
                    ->maxLength(255)
                    ->visible(fn ($state, $record) => $record),
                Select::make('transaction_type')
                    ->options(function ($get, $record) {
                        if (! $record) {
                            return WalletTransactionType::getOptionsWithKeyValuePair([WalletTransactionType::ADJUSTMENT->value]);
                        }

                        return WalletTransactionType::getOptionsWithKeyValuePair();
                    })
                    ->required(),
                Select::make('entry_type')
                    ->options(WalletEntryType::getOptionsWithKeyValuePair())
                    ->required(),
                TextInput::make('order_no')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record && $record->order?->order_no)
                    ->visible(fn ($state, $record) => $record && $record->order),
                TextInput::make('withdrawal_no')
                    ->disabled()
                    ->formatStateUsing(fn ($state, $record) => $record && $record->withdrawal?->withdraw_no)
                    ->visible(fn ($state, $record) => $record && $record->withdrawal),
                TextInput::make('currency_code')
                    ->required()
                    ->default('EUR')
                    ->readOnly(),
                TextInput::make('total_amount')
                    ->required()
                    ->inputMode('decimal')
                    ->numeric(),
                TextInput::make('withdrawn_amount')
                    ->required()
                    ->inputMode('decimal')
                    ->numeric()
                    ->visible(fn ($state, $record) => $record),
                TextInput::make('remained_amount')
                    ->required()
                    ->inputMode('decimal')
                    ->numeric()
                    ->visible(fn ($state, $record) => $record),
                TextInput::make('balance_after')
                    ->required()
                    ->inputMode('decimal')
                    ->numeric()
                    ->visible(fn ($state, $record) => $record),
                Textarea::make('note')
                    ->required(fn ($state, $record) => ! $record),
                Select::make('status')
                    ->options(function ($get, $record) {
                        if (! $record) {
                            return WalletTransactionStatus::getOptionsWithKeyValuePair([WalletTransactionStatus::PENDING->value]);
                        }

                        return WalletTransactionStatus::getOptionsWithKeyValuePair();
                    })
                    ->required(),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('transaction_no')
            ->columns([
                Tables\Columns\TextColumn::make('transaction_no')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_type')
                    ->label('Type')
                    ->formatStateUsing(function (WalletTransactionType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (WalletTransactionType $state) => $state->getBadgeColor())
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('entry_type')
                    ->label('Credit / Debit')
                    ->formatStateUsing(function (WalletEntryType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (WalletEntryType $state) => $state->getBadgeColor())
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency_code')
                    ->label('Currency')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Amount')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('withdrawn_amount')
                    ->searchable()
                    ->label('Withdrawn')
                    ->sortable(),
                Tables\Columns\TextColumn::make('remained_amount')
                    ->label('Remaining')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(function (WalletTransactionStatus $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (WalletTransactionStatus $state) => $state->getBadgeColor())
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('balance-display')
                    ->label('')
                    ->view('filament.relation-managers.user-balance-header')
                    ->viewData([
                        'balance' => $this->getOwnerRecord()?->wallet->balance ?? 0,
                    ])
                    ->visible(fn () => true),
                Tables\Actions\CreateAction::make()
                    ->slideOver()
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false)
                    ->using(function (array $data): Model {

                        $userWallet = $this->getOwnerRecord()->wallet;

                        if (! $userWallet) {
                            $userWallet = UserWallet::create([
                                'user_id' => $this->getOwnerRecord()->id,
                                'balance' => 0,
                                'currency_code' => $data['currency_code'],
                            ]);
                        }

                        $isCredit = $data['entry_type'] === WalletEntryType::CREDIT->value;

                        $newBalance = $isCredit ? $userWallet->balance + $data['total_amount'] : $userWallet->balance - $data['total_amount'];

                        $data['withdrawn_amount'] = $isCredit ? 0 : $data['total_amount'];
                        $data['remained_amount'] = $isCredit ? $data['total_amount'] : 0;
                        $data['balance_after'] = $newBalance;

                        $walletTransaction = $this->getRelationship()->create($data);

                        $userWallet->update(['balance' => $newBalance]);

                        return $walletTransaction;
                    }),
            ])
            ->defaultSort('id', 'desc')
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }
}
