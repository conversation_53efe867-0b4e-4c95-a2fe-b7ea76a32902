<?php

namespace App\Filament\Resources;

use App\Enums\GenderType;
use App\Enums\UserType;
use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers\PayoutMethodsRelationManager;
use App\Filament\Resources\CustomerResource\RelationManagers\WalletTransactionsRelationManager;
use App\Models\User;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class CustomerResource extends Resource
{
    use TableNameTrait;

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?string $modelLabel = 'Customer';

    protected static ?string $navigationGroup = 'User Mangement';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        TextInput::make('name')
                            ->label('Full Name')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('user_name')
                            ->label('Username')
                            ->unique(ignoreRecord: true)
                            ->required()
                            ->maxLength(255),
                        TextInput::make('email')
                            ->label('Email Address')
                            ->email()
                            ->unique(ignoreRecord: true)
                            ->required(),
                        Select::make('user_type')
                            ->label('Select User Type')
                            ->options(UserType::getOptionsWithKeyValuePair(
                                [
                                    UserType::BROKER->value,
                                    UserType::CUSTOMER->value,
                                ]
                            ))
                            ->reactive()
                            ->afterStateUpdated(fn ($state, $set) => $set('user_type', $state))
                            ->required(),
                        TextInput::make('password')
                            ->label('Password')
                            ->password()
                            ->confirmed()
                            ->rule(Password::min(8)->mixedCase()->numbers()->symbols())
                            ->dehydrateStateUsing(fn ($state) => ! empty($state) ? Hash::make($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn ($record) => $record === null), // Only required on create

                        TextInput::make('password_confirmation')
                            ->label('Confirm Password')
                            ->password()
                            ->dehydrated(false)
                            ->required(fn ($record) => $record === null),
                    ])
                    ->columns(2),

                Section::make('Additional Information')
                    ->relationship('userDetail')
                    ->schema([
                        TextInput::make('surname')
                            ->maxLength(100),
                        TextInput::make('phone')
                            ->tel()
                            ->maxLength(15),
                        Select::make('gender')
                            ->label('Select your Gender')
                            ->options(GenderType::getOptionsWithKeyValuePair())
                            ->required(),
                        TextInput::make('address')
                            ->maxLength(255),
                        TextInput::make('city')
                            ->maxLength(100),
                        Select::make('country_id')
                            ->label('Country')
                            ->required()
                            ->relationship('country', self::getCountryTransTable().'.name', function ($query) {
                                return $query->leftJoin(self::getCountryTransTable(), self::getCountryTable().'.id', '=', self::getCountryTransTable().'.country_id')
                                    ->where(self::getCountryTransTable().'.locale', app()->getLocale());
                            })
                            ->preload()
                            ->searchable(),
                        TextInput::make('zip')
                            ->maxLength(10),

                        TextInput::make('company')
                            ->label('Company Name')
                            ->maxLength(100)
                            ->visible(fn ($get) => $get('../user_type') === UserType::BROKER->value)
                            ->required(fn ($get) => $get('../user_type') === UserType::BROKER->value),
                        TextInput::make('government_id')
                            ->label('Government ID')
                            ->maxLength(100)
                            ->visible(fn ($get) => $get('../user_type') === UserType::BROKER->value)
                            ->required(fn ($get) => $get('../user_type') === UserType::BROKER->value),
                        Textarea::make('description')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(label: 'Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(label: 'Email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user_type')
                    ->label(label: 'User Type')
                    ->formatStateUsing(function (UserType $state) {
                        return $state->getLabel();
                    })
                    ->badge()
                    ->color(fn (UserType $state) => $state->getBadgeColour())
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('userDetail.phone')
                    ->label(label: 'Phone')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('userDetail.gender')
                    ->label(label: 'Gender')
                    ->formatStateUsing(fn (GenderType $state): string => $state->getLabel())
                    ->searchable(),
                Tables\Columns\TextColumn::make('userDetail.surname')
                    ->label(label: 'Surname')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('userDetail.city')
                    ->label(label: 'City')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->defaultSort('created_at', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            PayoutMethodsRelationManager::class,
            WalletTransactionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCustomers::route('/'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereIn('user_type', [UserType::BROKER->value, UserType::CUSTOMER->value])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::query()
            ->where('created_at', '>=', now()->subDay())
            ->whereIn('user_type', [UserType::BROKER->value, UserType::CUSTOMER->value])
            ->count();
    }
}
