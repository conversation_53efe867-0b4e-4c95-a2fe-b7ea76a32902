<?php

namespace App\Filament\Resources\RestrictionResource\Pages;

use App\Filament\Resources\RestrictionResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageRestrictions extends ManageRecords
{
    protected static string $resource = RestrictionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $translations = $data['translations'];
                    unset($data['translations']);

                    // Save restriction and translations
                    $restriction = static::getModel()::create($data);
                    $restriction->translations()->createMany($translations);

                    return $restriction;
                })
                ->closeModalByEscaping(false)
                ->closeModalByClickingAway(false),
        ];
    }
}
