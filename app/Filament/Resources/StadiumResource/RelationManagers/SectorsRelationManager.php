<?php

namespace App\Filament\Resources\StadiumResource\RelationManagers;

use App\Models\StadiumSector;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rule;

class SectorsRelationManager extends RelationManager
{
    protected static string $relationship = 'sectors';

    protected static ?string $badge = null;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->rules(function (callable $get, $record) {
                        return [
                            Rule::unique('stadium_sectors', 'name')
                                ->where(function ($query) use ($get) {
                                    return $query
                                        ->where('stadium_id', $this->ownerRecord->id)
                                        ->where('parent_id', $get('parent_id') ?: null);
                                })
                                ->ignore($record?->id),
                        ];
                    }),
                Forms\Components\Select::make('parent_id')
                    ->relationship(
                        name: 'parent',
                        titleAttribute: 'name',
                        modifyQueryUsing: function ($query, $get, $record) {

                            $query->where('stadium_id', $this->ownerRecord->id);

                            if ($record) {
                                $query
                                    ->where('id', '<>', $record->id)
                                    ->whereNull('parent_id');
                            } else {
                                $query->whereNull('parent_id');
                            }
                        }
                    )
                    ->preload()
                    ->searchable()
                    ->nullable()
                    ->helperText('Select parent sector from the same stadium (max 1 level).'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('Parent Sector')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('Parent Sector')
                    ->options(function () {
                        return StadiumSector::whereNull('parent_id')
                            ->where('stadium_id', $this->ownerRecord->id)
                            ->pluck('name', 'id');
                    })
                    ->searchable(),
                Tables\Filters\Filter::make('only_parents')
                    ->label('Show Only Parent Sectors')
                    ->query(fn ($query) => $query->whereNull('parent_id')->where('stadium_id', $this->ownerRecord->id)),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => ! $record->events()->exists() &&
                        ! $record->children()->whereHas('events')->exists()
                    ),
                Tables\Actions\Action::make('info')
                    ->label('Restricted')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->visible(fn ($record) => $record->events()->exists() || $record->children()->whereHas('events')->exists())
                    ->tooltip('This Sector cannot be deleted while it has active events'),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->checkIfRecordIsSelectableUsing(
                fn ($record): bool => ! $record->events()->exists() && ! $record->children()->whereHas('events')->exists(),
            )
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]));
    }

    public static function getBadge(Model $ownerRecord, string $pageClass): ?string
    {
        return $ownerRecord->sectors()->count(); // Count of related sectors
    }
}
