<?php

namespace App\Filament\Resources\LeagueResource\Pages;

use App\Filament\Resources\LeagueResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageLeagues extends ManageRecords
{
    protected static string $resource = LeagueResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $translations = $data['translations'];
                    $slugs = $data['slugs'];

                    unset($data['translations'], $data['slugs'], $data['image_alt']);

                    // Save league and translations
                    $league = static::getModel()::create($data);

                    $league->setSlugs($slugs);

                    $league->translations()->createMany($translations);

                    return $league;
                }),
        ];
    }
}
