<?php

namespace App\Filament\Resources\ClubResource\Pages;

use App\Filament\Resources\ClubResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageClubs extends ManageRecords
{
    protected static string $resource = ClubResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {

                    $slugs = $data['slugs'];
                    $translations = $data['translations'];

                    unset($data['slugs'], $data['translations'], $data['image_alt']);

                    // Save club and translations
                    $club = static::getModel()::create($data);

                    $club->setSlugs($slugs);

                    $club->translations()->createMany($translations);

                    return $club;
                })
                ->slideOver(),
        ];
    }
}
