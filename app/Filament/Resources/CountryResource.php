<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CountryResource\Pages;
use App\Models\Country;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CountryResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = Country::class;

    protected static ?string $navigationIcon = 'heroicon-o-flag';

    protected static ?string $navigationGroup = 'Masters';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('shortcode')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone_code')
                    ->label('Phone Code')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('currency_code')
                    ->label('Currency Code')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('currency_symbol')
                    ->label('Currency Symbol')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('slug')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_published'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->defaultSort('name')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCountries::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getCountryTable().'.*', self::getCountryTransTable().'.name')
            ->leftJoin(self::getCountryTransTable(), self::getCountryTable().'.id', '=', self::getCountryTransTable().'.country_id')
            ->where(self::getCountryTransTable().'.locale', app()->getLocale())
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
