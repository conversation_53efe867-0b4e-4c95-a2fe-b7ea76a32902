<?php

namespace App\Filament\Resources\SeasonResource\Pages;

use App\Filament\Resources\SeasonResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;

class ManageSeasons extends ManageRecords
{
    protected static string $resource = SeasonResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): Model {
                    $translations = $data['translations'];
                    $slugs = $data['slugs'];

                    unset($data['slugs'], $data['translations']);

                    // Save season and translations
                    $season = static::getModel()::create($data);

                    $season->setSlugs($slugs);

                    $season->translations()->createMany($translations);

                    return $season;
                })
                ->closeModalByEscaping(false)
                ->closeModalByClickingAway(false),
        ];
    }
}
