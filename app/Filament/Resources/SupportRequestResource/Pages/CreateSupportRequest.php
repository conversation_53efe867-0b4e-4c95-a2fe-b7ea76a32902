<?php

namespace App\Filament\Resources\SupportRequestResource\Pages;

use App\Enums\MediaLibrary;
use App\Filament\Resources\SupportRequestResource;
use App\Models\SupportRequestMessage;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class CreateSupportRequest extends CreateRecord
{
    protected static string $resource = SupportRequestResource::class;

    protected function afterCreate(): void
    {
        $record = $this->getRecord();

        // Create the initial message if provided
        if ($this->data['initial_message'] ?? null) {
            $message = new SupportRequestMessage([
                'support_request_id' => $record->getKey(),
                'user_id' => $this->data['user_id'],
                'message' => $this->data['initial_message'],
            ]);

            $message->save();

            // Handle document uploads if any
            if (! empty($this->data['documents'])) {
                $documents = $this->data['documents'];
                if (is_array($documents)) {
                    foreach ($documents as $document) {
                        // Get the temporary file path from the Filament FileUpload component
                        $filePath = Storage::disk(MediaLibrary::ADMIN_DISK->value)->path($document);

                        // Add the file to media library if it exists
                        if (file_exists($filePath)) {
                            $message->addMedia($filePath)
                                ->toMediaCollection(MediaLibrary::SUPPORT_REQUEST_DOCUMENTS->value);
                        }
                    }
                }
            }
        }
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        unset($data['initial_message']);
        unset($data['user_id_for_message']);
        unset($data['documents']);

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
    }
}
