<?php

namespace App\Filament\Resources\SupportRequestResource\Pages;

use App\Enums\SupportRequestStatus;
use App\Filament\Resources\SupportRequestResource;
use App\Jobs\SendSupportRequestClosedEmailJob;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSupportRequest extends EditRecord
{
    protected static string $resource = SupportRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        $record = $this->getRecord();
        $originalStatus = $record->getOriginal('status');
        $newStatus = $record->status;

        // Check if status was changed to CLOSED
        if ($originalStatus !== SupportRequestStatus::CLOSED && $newStatus === SupportRequestStatus::CLOSED) {
            // Dispatch the closure email job
            SendSupportRequestClosedEmailJob::dispatch($record->id);
        }
    }
}
