<?php

namespace App\Filament\Resources\SupportRequestResource\RelationManagers;

use App\Enums\MediaLibrary;
use App\Enums\UserType;
use App\Jobs\SendSupportRequestReplyEmailJob;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class MessagesRelationManager extends RelationManager
{
    protected static string $relationship = 'messages';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('user_id')
                    ->default(Auth::id()),
                Forms\Components\Textarea::make('message')
                    ->required()
                    ->maxLength(255),
                Forms\Components\SpatieMediaLibraryFileUpload::make('documents')
                    ->label('Documents')
                    ->collection(MediaLibrary::SUPPORT_REQUEST_DOCUMENTS->value)
                    ->multiple()
                    ->disk(MediaLibrary::ADMIN_DISK->value),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('message')
            ->columns([
                Tables\Columns\ImageColumn::make('user.avatar')
                    ->label('')
                    ->circular(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable(),
                Tables\Columns\TextColumn::make('message')
                    ->limit(100)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= 100) {
                            return null;
                        }

                        return $state;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->slideOver()
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false)
                    ->after(function ($record) {
                        // Check if the current user is an admin
                        $currentUser = Auth::user();
                        if ($currentUser && in_array($currentUser->user_type, [UserType::ADMIN, UserType::SUPERADMIN])) {
                            // Dispatch email notification job for admin reply
                            SendSupportRequestReplyEmailJob::dispatch($record->id, 'admin');
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('View Message Details')
                    ->form([]) // Add this line to disable the default form
                    ->modalContent(fn ($record) => view('filament.custom.message-view', [
                        'message' => $record->message,
                        'media' => $record->getMedia(MediaLibrary::SUPPORT_REQUEST_DOCUMENTS->value),
                        'created_at' => $record->created_at,
                        'user' => $record->user,
                    ])),
                Tables\Actions\EditAction::make()
                    ->slideOver()
                    ->closeModalByEscaping(false)
                    ->closeModalByClickingAway(false)
                    ->visible(fn ($record) => $record->user_id === Auth::id()),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->orderByDesc('id'));
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->orderByDesc('id');
    }
}
