<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClubResource\Pages;
use App\Models\Club;
use App\Models\Language;
use App\Traits\NavigationBadgeTrait;
use App\Traits\TableNameTrait;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ClubResource extends Resource
{
    use NavigationBadgeTrait;
    use TableNameTrait;

    protected static ?string $model = Club::class;

    protected static ?string $navigationIcon = 'heroicon-o-home-modern';

    protected static ?string $navigationGroup = 'Masters';

    public static function form(Form $form): Form
    {
        $languages = Language::select('name', 'locale')->where('is_active', 1)->orderBy('locale', 'ASC')->get();

        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        Select::make('country_id')
                            ->label('Country')
                            ->required()
                            ->relationship('country', self::getCountryTransTable().'.name', function ($query) {
                                return $query->leftJoin(self::getCountryTransTable(), self::getCountryTable().'.id', '=', self::getCountryTransTable().'.country_id')
                                    ->where(self::getCountryTransTable().'.locale', app()->getLocale());
                            })
                            ->live()
                            ->preload()
                            ->searchable(),
                        Select::make('stadium_id')
                            ->disabled(fn (Get $get): bool => ! filled($get('country_id')))
                            ->relationship(
                                name: 'stadium',
                                titleAttribute: self::getStadiumTransTable().'.name',
                                modifyQueryUsing: function ($query, Get $get) {
                                    $country_id = $get('country_id');

                                    return $query->leftJoin(self::getStadiumTransTable(), self::getStadiumTable().'.id', '=', self::getStadiumTransTable().'.stadium_id')
                                        ->where('country_id', $country_id)
                                        ->where(self::getStadiumTransTable().'.locale', app()->getLocale());
                                }
                            )
                            ->preload()
                            ->reactive()
                            ->searchable()
                            ->required(),
                        Toggle::make('is_active')
                            ->required()
                            ->disabled(fn ($record) => $record && $record->is_active && ($record->home_events_count > 0 || $record->guest_events_count > 0))
                            ->helperText(function ($record) {
                                return $record && $record->is_active && ($record->home_events_count > 0 || $record->guest_events_count > 0)
                                    ? 'This Club cannot be deactivated while it has active events'
                                    : null;
                            })
                            ->inline(false),
                        TextInput::make('tixstock_id')
                            ->maxLength(50)
                            ->disabled(fn ($record) => $record && $record->tixstock_id),
                    ]),

                Section::make('Contents')
                    ->schema([
                        Tabs::make('content_tabs')
                            ->columnSpanFull() // Ensure full width
                            ->tabs(
                                collect($languages)->map(function ($language, $key) {
                                    return Tabs\Tab::make($language->name)
                                        ->schema([
                                            Hidden::make("translations.$key.locale")
                                                ->default($language->locale),
                                            TextInput::make("translations.$key.name")
                                                ->label('Name')
                                                ->required()
                                                ->maxLength(255)
                                                ->rules(function ($get, $record) use ($language) {
                                                    return [
                                                        Rule::unique(self::getClubTransTable(), 'name')
                                                            ->where('locale', $language->locale)
                                                            ->ignore($record?->id, 'club_id'),
                                                    ];
                                                }),

                                            TextInput::make("slugs.$language->locale")
                                                ->label('Slug')
                                                ->required()
                                                ->maxLength(255)
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(function (Set $set, $state) use ($language) {
                                                    $set("slugs.{$language->locale}", Str::slug($state));
                                                })
                                                ->rules(function ($get, $record) use ($language) {
                                                    return [
                                                        Rule::unique(self::getSlugTable(), 'slug')
                                                            ->where(function ($query) use ($language, $record) {
                                                                $query->where('locale', $language->locale);

                                                                if ($record) {
                                                                    $query->where(function ($q) use ($record) {
                                                                        $q->where('sluggable_id', '!=', $record->id)
                                                                            ->orWhere('sluggable_type', '!=', get_class($record));
                                                                    });
                                                                }
                                                            }),
                                                    ];
                                                }),

                                            Textarea::make("translations.$key.description")
                                                ->label('Description')
                                                ->rows(5)
                                                ->maxLength(255)
                                                ->required(),
                                            RichEditor::make("translations.$key.detailed_description")
                                                ->label('Detailed Description')
                                                ->required()
                                                ->helperText('This appears in the main page of the club for the users'),
                                            TextInput::make("translations.$key.meta_title")
                                                ->label('Meta Title')
                                                ->maxLength(255)
                                                ->required(),
                                            TextInput::make("translations.$key.meta_keywords")
                                                ->label('Meta Keywords')
                                                ->maxLength(255)
                                                ->required(),
                                            Textarea::make("translations.$key.meta_description")
                                                ->label('Meta Description')
                                                ->maxLength(255)
                                                ->required(),
                                        ]);
                                })->toArray()
                            ),
                    ])
                    ->columnSpanFull(), // Ensure full width for the section

                SpatieMediaLibraryFileUpload::make('image')
                    ->required()
                    ->columnSpanFull()
                    ->disk('admin')
                    ->customProperties(fn (Get $get): array => [
                        'alt' => $get('image_alt'),
                    ]),
                TextInput::make('image_alt')
                    ->label('Image Alt Text')
                    ->required()
                    ->maxLength(100)
                    ->columnSpanFull(),

            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('club_name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(self::getClubTransTable().'.name'),
                Tables\Columns\TextColumn::make('club_description')
                    ->label('Description')
                    ->limit(40)
                    ->sortable()
                    ->searchable(self::getClubTransTable().'.description'),
                Tables\Columns\TextColumn::make('country_name')
                    ->sortable()
                    ->searchable(self::getCountryTransTable().'.name'),
                Tables\Columns\TextColumn::make('stadium_name')
                    ->label('Stadium')
                    ->sortable()
                    ->searchable(self::getStadiumTransTable().'.name'),
                Tables\Columns\TextColumn::make('slug')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('tixstock_id')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->disabled(fn ($record) => $record->is_active && ($record->home_events_count > 0 || $record->guest_events_count > 0))
                    ->tooltip(function ($record) {
                        return $record->is_active && ($record->home_events_count > 0 || $record->guest_events_count > 0)
                            ? 'This Club cannot be deactivated while it has active events'
                            : null;
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data, $livewire) {
                        $record = $livewire->getMountedTableActionRecord();

                        // Ensure the form structure includes translations
                        $data['translations'] = $record->translations->sortBy('locale');
                        $data['slugs'] = $record->slugs->pluck('slug', 'locale')->toArray();
                        $data['image_alt'] = optional($record->getFirstMedia())->getCustomProperty('alt');

                        return $data;
                    })
                    ->using(function (Model $record, array $data): Model {

                        $slugs = $data['slugs'];
                        $translations = $data['translations'];
                        $image_alt = $data['image_alt'];
                        unset($data['slugs'], $data['translations'], $data['image_alt']);

                        // Update the main League record
                        $record->update($data);

                        $record->setSlugs($slugs);

                        $translations = collect($translations)->map(function ($translation) use ($record) {
                            $translation['club_id'] = $record->id;
                            $translation['updated_at'] = now();

                            return $translation;
                        })->toArray();

                        $record->translations()->upsert(
                            $translations,
                            ['club_id', 'locale'],
                            ['name', 'description', 'detailed_description', 'meta_title', 'meta_keywords',
                                'meta_description', 'updated_at']
                        );

                        $media = $record->getFirstMedia();
                        if ($media) {
                            $media->setCustomProperty('alt', $image_alt ?? '');
                            $media->save();
                        }

                        return $record;
                    })
                    ->slideOver(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => ($record->home_events_count === 0 && $record->guest_events_count === 0)),
                Tables\Actions\Action::make('info')
                    ->label('Restricted')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->visible(fn ($record) => ($record->home_events_count > 0 || $record->guest_events_count > 0))
                    ->tooltip('This Club cannot be deleted while it has active events'),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->checkIfRecordIsSelectableUsing(
                fn ($record): bool => $record->home_events_count === 0 && $record->guest_events_count === 0,
            )
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageClubs::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->select(self::getClubTable().'.*', self::getClubTransTable().'.name as club_name', self::getClubTransTable().'.description as club_description', self::getStadiumTransTable().'.name as stadium_name', self::getCountryTransTable().'.name as country_name', self::getSlugTable().'.slug')
            ->leftJoin(self::getClubTransTable(), function ($join) {
                $join->on(self::getClubTable().'.id', '=', self::getClubTransTable().'.club_id')
                    ->where(self::getClubTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getStadiumTransTable(), function ($join) {
                $join->on(self::getClubTable().'.stadium_id', '=', self::getStadiumTransTable().'.stadium_id')
                    ->where(self::getStadiumTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getCountryTransTable(), function ($join) {
                $join->on(self::getClubTable().'.country_id', '=', self::getCountryTransTable().'.country_id')
                    ->where(self::getCountryTransTable().'.locale', app()->getLocale());
            })
            ->leftJoin(self::getSlugTable(), function ($join) {
                $join->on(self::getClubTable().'.id', '=', self::getSlugTable().'.sluggable_id')
                    ->where(self::getSlugTable().'.sluggable_type', '=', Club::class)
                    ->where(self::getSlugTable().'.locale', app()->getLocale());
            })
            ->withCount(['homeEvents' => function ($query) {
                $query->where('is_published', 1);
            }])
            ->withCount(['guestEvents' => function ($query) {
                $query->where('is_published', 1);
            }])
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
