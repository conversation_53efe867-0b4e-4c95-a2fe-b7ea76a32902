<?php

namespace App\Filament\Pages;

use Illuminate\Contracts\Support\Htmlable;
use Shu<PERSON>roRoy\FilamentSpatieLaravelHealth\Pages\HealthCheckResults as BaseHealthCheckResults;

class HealthCheckResults extends BaseHealthCheckResults
{
    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';

    public static function getNavigationGroup(): ?string
    {
        return 'Settings';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Application Health';
    }

    public static function getNavigationLabel(): string
    {
        return 'Application Health';
    }
}
