<?php

namespace App\Filament\Pages;

use <PERSON>swan\FilamentLaravelPulse\Widgets\PulseCache;
use <PERSON>s<PERSON>\FilamentLaravelPulse\Widgets\PulseExceptions;
use <PERSON>swan\FilamentLaravelPulse\Widgets\PulseQueues;
use <PERSON>swan\FilamentLaravelPulse\Widgets\PulseServers;
use Dotswan\FilamentLaravelPulse\Widgets\PulseSlowOutGoingRequests;
use <PERSON>swan\FilamentLaravelPulse\Widgets\PulseSlowQueries;
use <PERSON>swan\FilamentLaravelPulse\Widgets\PulseSlowRequests;
use Dotswan\FilamentLaravelPulse\Widgets\PulseUsage;
use Filament\Pages\Page;

class FilamentLaravelPulse extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationGroup = 'Settings';

    protected static ?string $navigationLabel = 'Pulse';

    protected static ?string $title = 'Laravel Pulse';

    protected static string $view = 'filament.pages.pulse';

    protected static ?int $navigationSort = 12;

    public function getColumns(): int|string|array
    {
        return 12;
    }

    public function getWidgets(): array
    {
        return [
            PulseServers::class,
            PulseCache::class,
            PulseExceptions::class,
            PulseUsage::class,
            PulseQueues::class,
            PulseSlowQueries::class,
            PulseSlowRequests::class,
            PulseSlowOutGoingRequests::class,
        ];
    }
}
