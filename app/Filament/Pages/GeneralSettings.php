<?php

namespace App\Filament\Pages;

use App\Enums\SettingType;
use App\Models\GeneralSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class GeneralSettings extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cog';

    protected static string $view = 'filament.pages.general-settings';

    protected static ?string $title = 'General Settings';

    protected static ?string $navigationLabel = 'Settings';

    protected static ?string $navigationGroup = 'Content Management';

    protected static ?int $navigationSort = 11;

    protected static ?string $slug = 'settings';

    public array $settings = [];

    public static function canAccess(): bool
    {
        return auth()->user()->can('page_GeneralSettings');
    }

    public function mount()
    {
        $this->settings = GeneralSetting::pluck('setting_value', 'setting_key')->toArray();
        $this->form->fill(['settings' => $this->settings]);
    }

    public function save()
    {
        $this->validate();

        foreach ($this->settings as $key => $value) {
            GeneralSetting::where('setting_key', $key)->update(['setting_value' => $value]);
        }

        Notification::make()
            ->title('Settings Updated')
            ->success()
            ->send();
    }

    public function form(Form $form): Form
    {
        $settings = GeneralSetting::all();
        $fields = [];

        foreach ($settings as $setting) {
            $defaultValue = $this->settings[$setting->setting_key] ?? '';
            switch ($setting->setting_type) {
                case SettingType::TEXT->value:
                    $fields[] = Forms\Components\TextInput::make("settings.{$setting->setting_key}")
                        ->label($setting->setting_label)
                        ->reactive()
                        ->required();
                    break;
                case SettingType::EMAIL->value:
                    $fields[] = Forms\Components\TextInput::make("settings.{$setting->setting_key}")
                        ->label($setting->setting_label)
                        ->email()
                        ->reactive()
                        ->required();
                    break;
                case SettingType::TOGGLE->value:
                    $fields[] = Forms\Components\Toggle::make("settings.{$setting->setting_key}")
                        ->label($setting->setting_label);
                    break;
                case SettingType::TEXTAREA->value:
                    $fields[] = Forms\Components\Textarea::make("settings.{$setting->setting_key}")
                        ->label($setting->setting_label)
                        ->reactive()
                        ->required();
                    break;
                default:
                    $fields[] = Forms\Components\TextInput::make("settings.{$setting->setting_key}")
                        ->label($setting->setting_label)
                        ->reactive()
                        ->required();
                    break;
            }
        }

        return $form
            ->schema([
                Forms\Components\Grid::make(2)->schema($fields),
            ])
            ->columns(1);
    }
}
