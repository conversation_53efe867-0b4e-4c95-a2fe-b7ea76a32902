<?php

namespace App\Services;

use App\DTO\DashboardFilterDTO;
use App\Enums\DateRangeFilter;
use App\Repositories\OrderRepository;
use App\Repositories\SupportRequestRepository;
use App\Repositories\TicketRepository;
use App\Repositories\UserWithdrawalRepository;
use Illuminate\Support\Facades\Auth;

/**
 * Class DashboardService
 *
 * Service class for managing dashboard page stats and other data.
 */
class DashboardService
{
    protected $orderRepository;

    protected $ticketRepository;

    protected $userWithdrawalRepository;

    protected $supportRequestRepository;

    public function __construct(
        OrderRepository $orderRepository,
        TicketRepository $ticketRepository,
        UserWithdrawalRepository $userWithdrawalRepository,
        SupportRequestRepository $supportRequestRepository,
    ) {
        $this->orderRepository = $orderRepository;
        $this->ticketRepository = $ticketRepository;
        $this->userWithdrawalRepository = $userWithdrawalRepository;
        $this->supportRequestRepository = $supportRequestRepository;
    }

    public function getDashboardStats(DashboardFilterDTO $filtersDTO)
    {
        $user = Auth::user();

        $filterRange = DateRangeFilter::tryFrom($filtersDTO->period)->dateRange();

        $totalOrders = $this->orderRepository->getUserOrderCounts($user->id, $filterRange);
        $totalSalesOrders = $this->orderRepository->getUserSalesOrderCounts($user->id, $filterRange);

        $totalQuantity = $this->ticketRepository->getUserTicketCounts($user->id, 'quantity', $filterRange);
        $soldQuantity = $this->ticketRepository->getUserTicketCounts($user->id, 'sold_quantity', $filterRange);

        $ticketsSold = $this->orderRepository->getUserSalesSoldTicketsCount($user->id, $filterRange);

        $withdrawals = $this->userWithdrawalRepository->getUserWithdrawalCounts($user->id, $filterRange);

        $supportRequests = $this->supportRequestRepository->getSupportRequestCounts($user->id, $filterRange);

        $dateRangeFilters = DateRangeFilter::getOptionsWithKeyValuePair();

        $stats = [
            'total_orders' => $totalOrders,
            'total_sales_orders' => $totalSalesOrders,
            'total_tickets_added' => $totalQuantity + $soldQuantity,
            'total_tickets_sold' => $ticketsSold,
            'current_balance' => $user->wallet?->balance ?? 0,
            'withdrawal_requests' => $withdrawals,
            'support_requests' => $supportRequests,
        ];

        return ['stats' => $stats, 'dateRangeFilters' => $dateRangeFilters];
    }
}
