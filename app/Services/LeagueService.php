<?php

namespace App\Services;

use App\DTO\LeagueFilterDTO;
use App\Http\Resources\League\LeagueCollection;
use App\Http\Resources\League\LeagueDetailResource;
use App\Repositories\LeagueRepository;
use Illuminate\Support\Collection;

/**
 * Class LeagueService
 *
 * Service class for managing League, including retrieval, creation, updating, and deletion.
 */
class LeagueService
{
    /**
     * @var LeagueRepository The repository for interacting with Event data.
     */
    protected $leagueRepository;

    /**
     * LeagueRepository constructor.
     *
     * @param  LeagueRepository  $leagueRepository  The repository for interacting with user data.
     */
    public function __construct(LeagueRepository $leagueRepository)
    {
        $this->leagueRepository = $leagueRepository;
    }

    /**
     * League Options Listing.
     *
     * @return array The League collection.
     */
    public function leagueOptionsList(): Collection
    {
        return $this->leagueRepository->getLeagueOptionsList();
    }

    /**
     * League Listing.
     *
     * @return LeagueCollection The leagues LeagueCollection.
     */
    public function leaguesList(LeagueFilterDTO $filtersDTO): LeagueCollection
    {
        $query = $this->leagueRepository->getLeaguesList($filtersDTO);

        $leagues = $query->paginate(config('services.ticketgol.items_per_page'))->withPath(route('api.leagues.index'));

        return new LeagueCollection($leagues);
    }

    /**
     * League Details by slug.
     *
     * @param  string  $slug  League Slug     * @return LeagueDetailResource The League LeagueDetailResource.
     */
    public function getLeagueDetailBySlug(string $slug): ?LeagueDetailResource
    {
        $league = $this->leagueRepository->getLeagueDetail($slug);

        if ($league) {
            return new LeagueDetailResource($league);
        }

        return $league;
    }
}
