<?php

namespace App\Services;

use App\Repositories\CmsPageRepository;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CmsPageService
 *
 * Service class for managing CmsPages, including retrieval, creation, updating, and deletion.
 */
class CmsPageService
{
    /**
     * @var CmsPageRepository The repository for interacting with CmsPage data.
     */
    protected $cmsPageRepository;

    /**
     * CmsPageRepository constructor.
     *
     * @param  CmsPageRepository  $cmsPageRepository  The repository for interacting with user data.
     */
    public function __construct(CmsPageRepository $cmsPageRepository)
    {
        $this->cmsPageRepository = $cmsPageRepository;
    }

    /**
     * CmsPage Details by slug.
     *
     * @param  string  $slug  CmsPage Slug
     * @return Model The cms page Model.
     */
    public function getCmsPageDetailBySlug(string $slug): ?Model
    {
        return $this->cmsPageRepository->getCmsPageDetail($slug);
    }
}
