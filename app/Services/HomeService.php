<?php

namespace App\Services;

use App\Http\Resources\Event\EventResource;
use App\Repositories\EventRepository;

/**
 * Class HomeService
 *
 * Service class for managing home page events listing.
 */
class HomeService
{
    protected $eventRepository;

    /**
     * HomeService constructor.
     *
     * @param  EventRepository  $eventRepository  The repository for interacting with events data.
     */
    public function __construct(EventRepository $eventRepository)
    {
        $this->eventRepository = $eventRepository;
    }

    /**
     * Events Listing.
     *
     * @return Evetns The events.
     */
    public function getUpcomingEvents()
    {
        $query = $this->eventRepository->getHomePageEvents(['upcoming' => true]);
        $events = $query->limit(8)->get();

        return EventResource::collection($events);
    }

    /**
     * Events Listing.
     *
     * @return Evetns The events.
     */
    public function getFeaturedEvents()
    {
        $query = $this->eventRepository->getHomePageEvents(['featured' => true]);
        $events = $query->paginate(8);

        return EventResource::collection($events);
    }
}
