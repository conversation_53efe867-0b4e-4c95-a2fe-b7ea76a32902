<?php

namespace App\Services;

use App\Contracts\UserDTOInterface;
use App\Enums\EmailTemplateKeys;
use App\Enums\LanguageCode;
use App\Jobs\SendEmailJob;
use App\Repositories\UserDetailRepository;
use App\Repositories\UserRepository;
use App\Repositories\UserWalletRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;

/**
 * Class UserService
 *
 * Service class for managing users, including retrieval, creation, updating, and deletion.
 */
class UserService
{
    /**
     * @var UserRepository The repository for interacting with user data.
     */
    protected $userRepository;

    /**
     * @var UserDetailRepository The repository for interacting with user data.
     */
    protected $userDetailRepository;

    protected $userWalletRepository;

    /**
     * UserRepository constructor.
     *
     * @param  UserRepository  $userRepository  The repository for interacting with user data.
     * @param  UserDetailRepository  $userDetailRepository  The repository for interacting with user detail data.
     * @param  UserWalletRepository  $userWalletRepository  The repository for interacting with user wallet data.
     */
    public function __construct(
        UserRepository $userRepository,
        UserDetailRepository $userDetailRepository,
        UserWalletRepository $userWalletRepository
    ) {
        $this->userRepository = $userRepository;
        $this->userDetailRepository = $userDetailRepository;
        $this->userWalletRepository = $userWalletRepository;
    }

    /**
     * Create a new user.
     *
     * @param  array  $userData  The data for creating the user.
     * @return Model The created user model.
     */
    public function createUser(UserDTOInterface $userDTO): Model
    {
        $user = $this->userRepository->create($userDTO->toArray());

        $this->userDetailRepository->create($userDTO->toUserDetailArray($user->id));

        $this->userWalletRepository->createWallet($user->id);

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );
        $meta = [
            'name' => $user->name,
            'locale' => LanguageCode::ENGLISH->value,
            'verificationLink' => $verificationUrl,
        ];
        // Send verification email notification
        SendEmailJob::dispatch(EmailTemplateKeys::CUSTOMER_EMAIL_VERIFICATION->value, $meta, [$user->id]);

        Auth::login($user);

        return $user;
    }

    /**
     * Update a user.
     *
     * @param  int  $userId  The id for the user.
     * @param  array  $userData  The data for updating the user.
     * @return Model The created user model.
     */
    public function updateUser($userId, UserDTOInterface $userDTO): Model
    {
        $user = $this->userRepository->findById($userId);

        $this->userRepository->update($userId, $userDTO->toArray());
        $this->userDetailRepository->update($user->userDetail->id, $userDTO->toUserDetailArray($user->id));

        return $user;
    }
}
