<?php

namespace App\Services;

use Illuminate\Support\Facades\File;

class TranslationService
{
    public function getTranslations(?string $locale = null): array
    {
        $locale = $locale ?? app()->getLocale();
        $fallbackLocale = config('app.fallback_locale');

        $langPath = base_path("lang/$locale");
        $fallbackPath = base_path("lang/$fallbackLocale");

        $files = collect(File::files($fallbackPath))
            ->map(function ($file) {
                $fileName = pathinfo($file, PATHINFO_FILENAME);
                if (! in_array($fileName, ['auth', 'message', 'pagination', 'passwords'])) {
                    return $fileName;
                }
            })
            ->filter()
            ->values();

        $translations = [];

        foreach ($files as $file) {
            $localeFilePath = "$langPath/{$file}.php";
            $fallbackFilePath = "$fallbackPath/{$file}.php";

            if (File::exists($localeFilePath)) {
                $translations[$file] = trans($file, [], $locale);
            } elseif (File::exists($fallbackFilePath)) {
                $translations[$file] = trans($file, [], $fallbackLocale);
            } else {
                $translations[$file] = [];
            }
        }

        return $translations;
    }

    /**
     * Get translations with locale information
     */
    public function getTranslationsWithLocale(?string $locale = null): array
    {
        $locale = $locale ?? app()->getLocale();
        $translations = $this->getTranslations($locale);

        return [
            'translations' => $translations,
            'locale' => $locale,
        ];
    }
}
