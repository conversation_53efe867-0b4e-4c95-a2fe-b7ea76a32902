<?php

namespace App\Services;

use App\DTO\EventTicketFilterDTO;
use App\DTO\MyTicketFilterDTO;
use App\DTO\TicketStoreDTO;
use App\DTO\TicketUpdateDTO;
use App\Enums\UserType;
use App\Http\Resources\Ticket\EventTicketCollection;
use App\Http\Resources\Ticket\MyTicketCollection;
use App\Http\Resources\Ticket\MyTicketDetailResource;
use App\Repositories\RestrictionRepository;
use App\Repositories\TicketRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * Class TicketService
 *
 * Service class for managing Tickets, including retrieval, creation, updating, and deletion.
 */
class TicketService
{
    /**
     * @var TicketRepository The repository for interacting with Event data.
     */
    protected $ticketRepository;

    protected $restrictionRepository;

    /**
     * TicketRepository constructor.
     *
     * @param  TicketRepository  $ticketRepository  The repository for interacting with user data.
     */
    public function __construct(TicketRepository $ticketRepository, RestrictionRepository $restrictionRepository)
    {
        $this->ticketRepository = $ticketRepository;
        $this->restrictionRepository = $restrictionRepository;
    }

    /**
     * Tickets Listing for the event.
     *
     * @return EventTicketCollection The tickets EventTicketCollection.
     */
    public function eventTicketsList(EventTicketFilterDTO $filtersDTO): EventTicketCollection
    {
        $query = $this->ticketRepository->getEventTicketsList($filtersDTO);
        $tickets = $query->paginate(config('services.ticketgol.tickets_per_page'))
            ->withPath(route('api.tickets.index'));

        return new EventTicketCollection($tickets);
    }

    /**
     * Tickets Listing for logged in user.
     *
     * @return MyTicketCollection The tickets MyTicketCollection.
     */
    public function myTicketsList(MyTicketFilterDTO $filtersDTO): MyTicketCollection
    {
        $query = $this->ticketRepository->getMyTicketsList($filtersDTO);
        $tickets = $query->paginate(config('services.ticketgol.tickets_per_page'));

        return new MyTicketCollection($tickets);
    }

    /**
     * Get ticket common configurations
     *
     * @return Collection
     */
    public function getConfigurations()
    {
        $restrictions = $this->restrictionRepository->getTicketRestrictions();
        $user = auth()->user();
        $ticketCounts = $user->tickets->sum('quantity') + $user->tickets->sum('sold_quantity');
        $limit = config('services.ticketgol.max_ticket_creation_limit');

        $canCreateTicket = true;

        if (! in_array($user->user_type->value, [UserType::CUSTOMER->value, UserType::BROKER->value])) {
            $canCreateTicket = false;
        }

        if ($user->user_type === UserType::CUSTOMER && $ticketCounts >= $limit) {
            $canCreateTicket = false;
        }
        $data = [
            'restrictions' => $restrictions,
            'can_create_ticket' => $canCreateTicket,
            'service_charge_rate' => config('services.ticketgol.service_charge_rate'),
            'tax_rate' => config('services.ticketgol.tax_rate'),
            'max_quantity_per_ticket' => config('services.ticketgol.max_quantity_per_ticket'),
            'max_price_limit' => config('services.ticketgol.max_price_limit'),
        ];

        return $data;
    }

    /**
     * Add ticket
     *
     * @return Collection
     */
    public function addTicket(TicketStoreDTO $ticketData): ?Model
    {
        return $this->ticketRepository->addTicket($ticketData);
    }

    /**
     * Update ticket
     *
     * @return Collection
     */
    public function updateTicket(TicketUpdateDTO $ticketData): ?Model
    {
        return $this->ticketRepository->updateTicket($ticketData);
    }

    /**
     * Get ticket details
     *
     * @return MyTicketDetailResource Ticket detail
     */
    public function showMyTicket($ticketNo): ?MyTicketDetailResource
    {
        $ticket = $this->ticketRepository->getMyTicketDetail($ticketNo);

        if ($ticket) {
            $restrictions = $this->restrictionRepository->getTicketRestrictions();
            $configurations = [
                'restrictions' => $restrictions,
                'service_charge_rate' => config('services.ticketgol.service_charge_rate'),
                'tax_rate' => config('services.ticketgol.tax_rate'),
                'max_quantity_per_ticket' => config('services.ticketgol.max_quantity_per_ticket'),
                'max_price_limit' => config('services.ticketgol.max_price_limit'),
            ];

            $ticket->configurations = $configurations;

            return new MyTicketDetailResource($ticket);
        }

        return $ticket;
    }

    /**
     * Delete ticket
     *
     * @return Collection
     */
    public function deleteMyTicket($ticketNo): string
    {
        $ticket = $this->ticketRepository->getMyTicketDetail($ticketNo);
        if ($ticket) {
            if ($ticket->orders_count == 0 && $ticket->reservations_count == 0) {
                $result = $this->ticketRepository->deleteById($ticket->id);

                return $result ? 'TICKET_DELETED' : 'SOMETHING_WENT_WRONG';
            }

            return 'TICKET_ORDERS_EXISTS';
        }

        return 'TICKET_NOT_FOUND';
    }
}
