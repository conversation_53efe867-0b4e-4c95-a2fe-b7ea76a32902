<?php

namespace App\Services;

use App\DTO\SupportTicketFilterDTO;
use App\DTO\SupportTicketStoreDTO;
use App\Jobs\SendSupportRequestCreationEmailJob;
use App\Jobs\SendSupportRequestReplyEmailJob;
use App\Repositories\SupportRequestRepository;

class SupportRequestService
{
    private $supportRequestRepository;

    public function __construct(SupportRequestRepository $supportRequestRepository)
    {
        $this->supportRequestRepository = $supportRequestRepository;
    }

    public function getSupportRequests($userId, SupportTicketFilterDTO $dto)
    {
        $query = $this->supportRequestRepository->getSupportRequests($userId, $dto);

        return $query->paginate(10);
    }

    public function createSupportRequest(SupportTicketStoreDTO $dto)
    {
        $supportRequest = $this->supportRequestRepository->createSupportRequestWithInitialMessage($dto);

        SendSupportRequestCreationEmailJob::dispatch($supportRequest->id);

        return $supportRequest;
    }

    public function getSupportDetail($id)
    {
        return $this->supportRequestRepository->getSupportDetail($id);
    }

    public function addReply($id, $dto)
    {
        $supportRequestMessage = $this->supportRequestRepository->addReply($id, $dto->message);

        if (! empty($dto->files)) {
            $this->supportRequestRepository->attachFiles($supportRequestMessage, $dto->files);
        }

        // This service method is only used by regular users (frontend)
        // Admin replies happen through Filament admin panel
        SendSupportRequestReplyEmailJob::dispatch($supportRequestMessage->id, 'user');

        return $supportRequestMessage->supportRequest->load(['messages']);
    }

    public function closeTicket($id)
    {
        $this->supportRequestRepository->closeTicket($id);
    }
}
