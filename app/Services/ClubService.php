<?php

namespace App\Services;

use App\DTO\ClubFilterDTO;
use App\Http\Resources\Club\ClubCollection;
use App\Http\Resources\Club\ClubDetailResource;
use App\Repositories\ClubRepository;
use Illuminate\Support\Collection;

/**
 * Class ClubService
 *
 * Service class for managing Club, including retrieval, creation, updating, and deletion.
 */
class ClubService
{
    /**
     * @var ClubRepository The repository for interacting with Event data.
     */
    protected $clubRepository;

    /**
     * ClubRepository constructor.
     *
     * @param  ClubRepository  $clubRepository  The repository for interacting with user data.
     */
    public function __construct(ClubRepository $clubRepository)
    {
        $this->clubRepository = $clubRepository;
    }

    /**
     * Club Options Listing.
     *
     * @return array The Club collection.
     */
    public function clubOptionsList(): Collection
    {
        return $this->clubRepository->getClubOptionsList();
    }

    /**
     * Club Listing.
     *
     * @return ClubCollection The clubs ClubCollection.
     */
    public function clubsList(ClubFilterDTO $filtersDTO): ClubCollection
    {
        $query = $this->clubRepository->getClubsList($filtersDTO);
        $clubs = $query->paginate(config('services.ticketgol.items_per_page'))->withPath(route('api.clubs.index'));

        return new ClubCollection($clubs);
    }

    /**
     * Club Details by slug.
     *
     * @param  string  $slug  Club Slug
     * @return ClubDetailResource The Club ClubDetailResource.
     */
    public function getClubDetailBySlug(string $slug): ?ClubDetailResource
    {
        $club = $this->clubRepository->getClubDetail($slug);
        if ($club) {
            return new ClubDetailResource($club);
        }

        return $club;
    }
}
