<?php

namespace App\Services;

use App\Enums\OrderStatus;
use App\Enums\OrderTransactionStatus;
use App\Enums\TicketReservationStatus;
use App\Jobs\CreateTixStockOrderJob;
use App\Jobs\SendOrderProcessingEmailJob;
use App\Repositories\OrderRepository;
use App\Repositories\OrderStatusHistoryRepository;
use App\Repositories\OrderTransactionRepository;
use App\Repositories\PaymentLogRepository;
use App\Repositories\TicketRepository;
use App\Repositories\TicketReservationRepository;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class StripeWebhookService
{
    private $stripe;

    private $orderRepository;

    private $orderTransactionRepository;

    private $ticketRepository;

    private $paymentLogRepository;

    private $ticketReservationRepository;

    public function __construct()
    {
        $this->stripe = new StripeClient(config('services.stripe.secret'));
        $this->orderRepository = app(OrderRepository::class);
        $this->orderStatusHistoryRepository = app(OrderStatusHistoryRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->ticketRepository = app(TicketRepository::class);
        $this->ticketReservationRepository = app(TicketReservationRepository::class);
        $this->paymentLogRepository = app(PaymentLogRepository::class);
    }

    public function handlePaymentIntentCreated($event)
    {
        $paymentIntent = $event->data->object;
        Log::channel(channel: 'stripe')->info('Payment intent created', ['payment_intent' => $paymentIntent]);

        $order = $this->orderRepository->findById($paymentIntent->metadata->order_id);

        $orderTransaction = $order->purchaseTransaction;

        $paymentMethodType = $paymentIntent->payment_method_types[0] ?? 'unknown';

        $this->orderTransactionRepository->update(
            $orderTransaction->id,
            [
                'payment_method_type' => $paymentMethodType,
                'payment_intent_id' => $paymentIntent->id,
                'currency_code' => strtoupper($paymentIntent->currency),
                'total_amount' => $paymentIntent->amount / config('services.ticketgol.stripe_cent_unit'),
            ]
        );
    }

    public function handleChargeSucceeded($event)
    {
        $charge = $event->data->object;
        Log::channel(channel: 'stripe')->info('Charge succeeded', ['charge' => $charge]);

        $order = $this->orderRepository->findById($charge->metadata->order_id);

        $orderTransaction = $order->purchaseTransaction;

        $this->orderTransactionRepository->update(
            $orderTransaction->id,
            [
                'payment_intent_id' => $charge->payment_intent,
                'stripe_charge_id' => $charge->id,
                'payment_method_type' => $charge->payment_method_details->type,
                'payment_method_id' => $charge->payment_method,
                'card_brand' => $charge->payment_method_details->card->brand ?? null,
                'card_last_four' => $charge->payment_method_details->card->last4 ?? null,
                'currency_code' => strtoupper($charge->currency),
                'total_amount' => $charge->amount / config('services.ticketgol.stripe_cent_unit'),
            ]
        );
    }

    public function handlePaymentIntentSucceeded($event)
    {
        $paymentIntent = $event->data->object;

        Log::channel(channel: 'stripe')->info('Payment intent succeeded', ['payment_intent' => $paymentIntent]);

        $order = $this->orderRepository->findById($paymentIntent->metadata->order_id);
        if ($order->status === OrderStatus::PENDING) {
            $this->confirmOrderAndTransaction($order);
        }
    }

    public function confirmOrderAndTransaction($order)
    {
        if ($order->status === OrderStatus::PENDING) {
            $ticketReservationId = $order->ticket_reservation_id;

            $orderTransaction = $order->purchaseTransaction;

            $autoConfirm = $order->seller->userDetail?->order_auto_confirm;

            $this->orderRepository->update($order->id, [
                'status' => $autoConfirm ? OrderStatus::CONFIRMED : OrderStatus::PROCESSING,
                'purchase_date' => now(),
            ]);

            $orderStatusDTO = [
                'order_id' => $order->id,
                'user_id' => $order->seller_id,
                'status' => $autoConfirm ? OrderStatus::CONFIRMED->value : OrderStatus::PROCESSING->value,
            ];

            $this->orderStatusHistoryRepository->addStatusHistory($orderStatusDTO, $order->status);

            Log::channel('stripe')->info('Order status updated to processing');

            $this->orderTransactionRepository->update($orderTransaction->id, [
                'status' => OrderTransactionStatus::COMPLETED,
                'paid_at' => now(),
            ]);

            Log::channel('stripe')->info('Transaction status updated to completed');

            $quantity = $order->quantity;
            $ticket = $this->ticketRepository->handleTicketQuantity($order->ticket_id, $quantity);

            if (! $ticket) {
                Log::channel('stripe')->info('Not enough tickets available.');
            }

            // Rare Condition: This condition happens on sometimes when there is a delay on stripe webhook and our cron job marked as expired before the webhook
            if ($order->status === OrderStatus::EXPIRED) {
                $this->ticketReservationRepository->update($ticketReservationId, [
                    'status' => TicketReservationStatus::COMPLETED,
                ]);
            } else {
                $this->ticketReservationRepository->updateCounterAndCompleteReservation($ticketReservationId);
            }

            CreateTixStockOrderJob::dispatch($order->id);
            SendOrderProcessingEmailJob::dispatch($order->id);

            $order = $this->orderRepository->findById($order->id);
        }

        return $order;
    }

    public function handleChargeRefunded($event)
    {
        $refund = $event->data->object;
        Log::channel(channel: 'stripe')->info('Charge Refunded', ['charge_refunded' => $refund]);

        if ($refund->paid) {
            $order = $this->orderRepository->findById($refund->metadata->order_id);

            $orderTransaction = $order->refundTransaction;

            $this->orderTransactionRepository->update(
                $orderTransaction->id,
                [
                    'status' => OrderTransactionStatus::REFUNDED,
                    'refunded_at' => now(),
                ]
            );
        }
    }

    public function addPaymentLog($event)
    {
        return $this->paymentLogRepository->createPaymentLog($event);
    }
}
