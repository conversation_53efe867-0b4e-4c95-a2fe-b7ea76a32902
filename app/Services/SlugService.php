<?php

namespace App\Services;

use App\Repositories\SlugRepository;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SlugService
 *
 * Service class for managing Slugs, including retrieval, creation, updating, and deletion.
 */
class SlugService
{
    /**
     * @var SlugRepository The repository for interacting with CmsPage data.
     */
    protected $slugRepository;

    /**
     * SlugRepository constructor.
     *
     * @param  SlugRepository  $slugRepository  The repository for interacting with user data.
     */
    public function __construct(SlugRepository $slugRepository)
    {
        $this->slugRepository = $slugRepository;
    }

    /**
     * Page Details by slug.
     *
     * @param  string  $slug  CmsPage Slug
     * @return Model The cms page Model.
     */
    public function getPageDetailBySlug(string $slug): ?Model
    {
        return $this->slugRepository->getSlugDetail($slug);
    }
}
