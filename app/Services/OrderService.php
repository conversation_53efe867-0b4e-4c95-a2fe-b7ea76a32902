<?php

namespace App\Services;

use App\DTO\MySalesFilterDTO;
use App\DTO\OrderFilterDTO;
use App\DTO\OrderStoreDTO;
use App\Enums\OrderStatus;
use App\Enums\OrderTransactionStatus;
use App\Enums\OrderTransactionType;
use App\Enums\StripePaymentIntentStatus;
use App\Enums\TicketReservationStatus;
use App\Http\Resources\Order\MySalesOrderCollection;
use App\Http\Resources\Order\MySalesOrderDetailResource;
use App\Http\Resources\OrderCollection;
use App\Jobs\UpdateOrderMetaDataJob;
use App\Repositories\OrderRepository;
use App\Repositories\OrderTransactionRepository;
use App\Repositories\TicketReservationRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class OrderService
{
    private $orderRepository;

    private $orderTransactionRepository;

    private $ticketReservationRepository;

    private $stripe;

    public function __construct()
    {
        $this->orderRepository = app(OrderRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->ticketReservationRepository = app(TicketReservationRepository::class);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    // Current implementation
    public function getPaginatedOrdersForUser($userId, OrderFilterDTO $filtersDTO)
    {
        $query = $this->orderRepository->getOrdersForUser($userId, $filtersDTO);
        $orders = $query->paginate(config('services.ticketgol.items_per_page'));

        return new OrderCollection($orders);
    }

    public function getMySalesOrders(MySalesFilterDTO $filtersDTO)
    {
        $sellerId = Auth::id();
        $query = $this->orderRepository->getSalesOrders($sellerId, $filtersDTO);
        $orders = $query->paginate(config('services.ticketgol.items_per_page'));

        return new MySalesOrderCollection($orders);
    }

    public function createOrder(OrderStoreDTO $orderDTO)
    {
        $order = $this->orderRepository->createOrder($orderDTO);

        $orderTransaction = $this->orderTransactionRepository->initiateTransaction($order, $orderDTO, OrderTransactionType::PURCHASE);

        $this->ticketReservationRepository->update($orderDTO->tempTicketReservationId, [
            'status' => TicketReservationStatus::PROCESSING,
        ]);

        UpdateOrderMetaDataJob::dispatch($order->id);

        return [$order, $orderTransaction];
    }

    public function getOrderDetail($orderId)
    {
        return $this->orderRepository->findById($orderId);
    }

    public function getStatusOptions()
    {
        $exclude = [OrderStatus::EXPIRED];

        $options = collect(OrderStatus::cases())
            ->reject(fn ($status) => in_array($status, $exclude))
            ->map(function ($status) {
                return [
                    'label' => $status->getLabel(),
                    'value' => $status->value,
                ];
            });

        $options->unshift([
            'label' => __('common.all') ?? 'All',
            'value' => '',
        ]);

        return $options;
    }

    public function createStripePaymentIntent($order)
    {
        $user = Auth::user();

        return $this->stripe->paymentIntents->create([
            'payment_method_types' => ['card'],
            'amount' => $order->grand_total * config('services.ticketgol.stripe_cent_unit'),
            'currency' => $order->currency_code,
            'metadata' => [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'ticket_reservation_id' => $order->ticket_reservation_id,
                'customer_email' => $user->email,
            ],
        ]);
    }

    public function expireOrderAndTransaction($order)
    {
        $this->orderRepository->update($order->id, [
            'status' => OrderStatus::EXPIRED,
        ]);

        $orderTransaction = $order->purchaseTransaction;

        $this->orderTransactionRepository->update($orderTransaction->id, [
            'status' => OrderTransactionStatus::EXPIRED,
        ]);
        try {
            $paymentIntent = $this->stripe->paymentIntents->retrieve($orderTransaction->payment_intent_id);
            $statuses = StripePaymentIntentStatus::getValues([StripePaymentIntentStatus::SUCCEEDED->value]);

            if (in_array($paymentIntent->status, $statuses)) {
                Log::channel('stripe')->info('Payment Intent cancelled through SDK', ['payment_intent_id' => $orderTransaction->payment_intent_id]);
                $this->stripe->paymentIntents->cancel($paymentIntent->id);
            }
        } catch (\Exception $e) {
            Log::channel('stripe')->error("Failed to cancel payment intent: {$e->getMessage()}");
        }
    }

    public function getOrderDetails(int $userId, int $orderId)
    {
        return $this->orderRepository->getOrderWithDetails($userId, $orderId);
    }

    public function getSalesOrderDetails(string $orderNo)
    {
        $sellerId = Auth::id();
        $order = $this->orderRepository->getSalesOrderWithDetails($orderNo, $sellerId);

        if ($order) {
            return new MySalesOrderDetailResource($order);
        }

        return $order;
    }

    public function markTicketsDownloaded($orderId)
    {
        $order = $this->orderRepository->findById($orderId);
        if ($order) {
            $this->orderRepository->update($order->id, ['ticket_downloaded_at' => now()]);
        }

        return $order;
    }
}
