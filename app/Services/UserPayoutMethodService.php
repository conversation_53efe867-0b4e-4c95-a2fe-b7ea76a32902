<?php

namespace App\Services;

use App\DTO\PayoutMethodStoreDTO;
use App\Enums\PayoutBankAccountType;
use App\Enums\PayoutMethodStatus;
use App\Enums\PayoutMethodType;
use App\Enums\UserType;
use App\Http\Resources\PayoutMethod\MyPayoutMethodCollection;
use App\Repositories\CountryRepository;
use App\Repositories\UserPayoutMethodRepository;
use Illuminate\Support\Facades\Auth;
use Stripe\StripeClient;

class UserPayoutMethodService
{
    private $userPayoutMethodRepository;

    private $countryRepository;

    private $stripe;

    public function __construct()
    {
        $this->userPayoutMethodRepository = app(UserPayoutMethodRepository::class);
        $this->countryRepository = app(CountryRepository::class);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    public function getPayoutMethods()
    {
        $user = Auth::user();

        $payoutMethods = $this->userPayoutMethodRepository->getUserPayoutMethods($user->id);

        return new MyPayoutMethodCollection($payoutMethods);
    }

    public function createPayoutMethod(PayoutMethodStoreDTO $payoutMethodDTO)
    {
        $user = Auth::user();
        $payoutMethodData = $payoutMethodDTO->toArray();
        $payoutMethodData['user_id'] = $user->id;
        $payoutMethodData['status'] = PayoutMethodStatus::ACTIVE;
        $payoutMethodData['is_default'] = $user->payoutMethods->count() === 0 ? 1 : 0;

        $payoutMethod = $this->userPayoutMethodRepository->createUserPayoutMethod($payoutMethodData);

        return $payoutMethod;
    }

    public function markDefaultPayoutMethod($payoutMethodId)
    {
        $user = Auth::user();

        return $this->userPayoutMethodRepository->markPayoutMethodAsDefault($payoutMethodId, $user->id);
    }

    public function deletePayoutMethod($payoutMethodId)
    {
        $payoutMethod = $this->userPayoutMethodRepository->findById($payoutMethodId);
        if ($payoutMethod && $payoutMethod->user_id == Auth::id()) {
            $result = $this->userPayoutMethodRepository->deleteById($payoutMethod->id);

            return $result ? 'PAYOUT_METHOD_DELETED' : 'SOMETHING_WENT_WRONG';
        }

        return 'PAYOUT_METHOD_NOT_FOUND';
    }

    public function getPayoutConfigurations()
    {
        $user = Auth::user();
        $countries = $this->countryRepository->getCountriesShortCodeList();
        $payoutMethodTypes = PayoutMethodType::getOptionsWithKeyValuePair();
        $bankAccountTypes = PayoutBankAccountType::getOptionsWithKeyValuePair();

        $totalPayoutMethods = $user->payoutMethods->count();

        $maxLimit = $user->user_type === UserType::BROKER ? config('services.ticketgol.broker_payout_method_max_limit') : config('services.ticketgol.payout_method_max_limit');

        $data = [
            'countries' => $countries,
            'payout_method_types' => $payoutMethodTypes,
            'bank_account_types' => $bankAccountTypes,
            'canAddPayoutMethod' => $totalPayoutMethods < $maxLimit,
        ];

        return $data;
    }
}
