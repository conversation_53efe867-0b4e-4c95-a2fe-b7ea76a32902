<?php

namespace App\Services;

use App\DTO\CheckoutDTO;
use App\Models\Event;
use App\Repositories\EventRepository;
use App\Repositories\OrderRepository;
use App\Repositories\OrderTransactionRepository;
use App\Repositories\TicketRepository;
use App\Repositories\TicketReservationRepository;
use Illuminate\Support\Facades\Auth;
use Stripe\StripeClient;

class CheckoutService
{
    private const EURO_MINOR_UNITS = 100;

    private $ticketRepository;

    private $orderRepository;

    private $eventRepository;

    private $orderTransactionRepository;

    private $ticketReservationRepository;

    private $stripe;

    public function __construct()
    {
        $this->ticketRepository = app(TicketRepository::class);
        $this->orderRepository = app(OrderRepository::class);
        $this->eventRepository = app(EventRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->ticketReservationRepository = app(TicketReservationRepository::class);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    public function createCheckoutSession(CheckoutDTO $checkoutDTO)
    {
        $ticket = $this->ticketRepository->findById($checkoutDTO->ticketId, ['*'], ['event.translation', 'translation']);
        $event = $this->eventRepository->getEventWithImage($ticket->event_id);

        $order = $this->orderRepository->createOrderWithAttendees($checkoutDTO, $ticket);
        $tempTicketReservation = $this->ticketReservationRepository->findById($checkoutDTO->tempTicketReservationId);

        $checkoutSession = $this->createStripeCheckoutObject($event, $ticket, $order, $checkoutDTO, $tempTicketReservation);
        $orderTransaction = $this->orderTransactionRepository->initiateTransaction($order, $checkoutSession, $tempTicketReservation);

        return $checkoutSession;
    }

    private function createStripeCheckoutObject($event, $ticket, $order, $checkoutDTO, $tempTicketReservation)
    {
        $user = Auth::user();

        $baseUnitAmount = round($ticket->price * $checkoutDTO->quantity, 2);
        $serviceCharge = round($baseUnitAmount * config('services.ticketgol.service_charge_rate'), 2);
        $tax = round($serviceCharge * config('services.ticketgol.tax_rate'), 2);

        return $this->stripe->checkout->sessions->create([
            'payment_method_types' => ['card'],
            'line_items' => [
                [
                    'price_data' => [
                        'currency' => $checkoutDTO->currencyCode,
                        'product_data' => [
                            'name' => $event->translation->name,
                            'images' => [$event->imageUrl ?? Event::DEFAUL_IMAGE_URL],
                            'description' => 'Ticket Quantity: '.$checkoutDTO->quantity,
                        ],
                        'unit_amount' => $ticket->price * self::EURO_MINOR_UNITS,
                    ],
                    'quantity' => $checkoutDTO->quantity,
                ],
                [
                    'price_data' => [
                        'currency' => $checkoutDTO->currencyCode,
                        'product_data' => [
                            'name' => '💰 Platform Service Fee',
                            'description' => config('services.ticketgol.service_charge_rate') * 100 .'% of ticket price',
                            'images' => [asset('img/coin.png')],
                        ],
                        'unit_amount' => $serviceCharge * self::EURO_MINOR_UNITS,
                    ],
                    'quantity' => 1,
                ],
                [
                    'price_data' => [
                        'currency' => $checkoutDTO->currencyCode,
                        'product_data' => [
                            'name' => '📋 Tax',
                            'description' => config('services.ticketgol.tax_rate') * 100 .'% of service fee',
                            'images' => [asset('img/shipping-cost.png')],
                        ],
                        'unit_amount' => $tax * self::EURO_MINOR_UNITS,
                    ],
                    'quantity' => 1,
                ],
            ],
            'mode' => 'payment',
            'success_url' => $checkoutDTO->successUrl.'?session_id={CHECKOUT_SESSION_ID}',
            'cancel_url' => $checkoutDTO->cancelUrl.'?session_id={CHECKOUT_SESSION_ID}',
            'customer_email' => $user->email,
            'metadata' => [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'temp_ticket_reservation_id' => $tempTicketReservation->id,
            ],
            'invoice_creation' => [
                'enabled' => true,
            ],
            'custom_text' => [
                'submit' => [
                    'message' => 'We will send you an email with your tickets also can view your tickets in your profile.',
                ],
            ],
        ]);
    }
}
