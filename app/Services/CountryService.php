<?php

namespace App\Services;

use App\Repositories\CountryRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;

/**
 * Class CountryService
 *
 * Service class for managing users, including retrieval, creation, updating, and deletion.
 */
class CountryService
{
    /**
     * @var CountryRepository The repository for interacting with user data.
     */
    protected $countryRepository;

    /**
     * CountryRepository constructor.
     *
     * @param  CountryRepository  $countryRepository  The repository for interacting with user data.
     */
    public function __construct(CountryRepository $countryRepository)
    {
        $this->countryRepository = $countryRepository;
    }

    /**
     * Get All Countries.
     *
     *
     * @return Collection The countries collection.
     */
    public function getAllCountries(): Collection
    {
        return $this->countryRepository->all(['id', 'shortcode'], ['translation:country_id,locale,name']);
    }

    /**
     * Country Options Listing.
     *
     * @return array The Country collection.
     */
    public function countryOptionsList(): SupportCollection
    {
        return $this->countryRepository->getCountriesOptionsList();
    }
}
