<?php

namespace App\Services;

use App\DTO\StadiumFilterDTO;
use App\Http\Resources\Stadium\StadiumCollection;
use App\Http\Resources\Stadium\StadiumDetailResource;
use App\Repositories\StadiumRepository;
use Illuminate\Support\Collection;

/**
 * Class StadiumService
 *
 * Service class for managing Stadium, including retrieval, creation, updating, and deletion.
 */
class StadiumService
{
    /**
     * @var StadiumRepository The repository for interacting with Event data.
     */
    protected $stadiumRepository;

    /**
     * StadiumRepository constructor.
     *
     * @param  StadiumRepository  $stadiumRepository  The repository for interacting with user data.
     */
    public function __construct(StadiumRepository $stadiumRepository)
    {
        $this->stadiumRepository = $stadiumRepository;
    }

    /**
     * Stadium Options Listing.
     *
     * @return array The Stadium collection.
     */
    public function stadiumOptionsList(): Collection
    {
        return $this->stadiumRepository->getStadiumOptionsList();
    }

    /**
     * Stadium Listing.
     *
     * @return StadiumCollection The stadiums StadiumCollection.
     */
    public function stadiumsList(StadiumFilterDTO $filtersDTO): StadiumCollection
    {
        $query = $this->stadiumRepository->getStadiumsList($filtersDTO);
        $stadiums = $query->paginate(
            config('services.ticketgol.items_per_page')
        )->withPath(route('api.stadiums.index'));

        return new StadiumCollection($stadiums);
    }

    /**
     * Stadium Details by slug.
     *
     * @param  string  $slug  Stadium Slug
     * @return StadiumDetailResource The Stadium StadiumDetailResource.
     */
    public function getStadiumDetailBySlug(string $slug): ?StadiumDetailResource
    {
        $stadium = $this->stadiumRepository->getStadiumDetail($slug);

        if ($stadium) {
            return new StadiumDetailResource($stadium);
        }

        return $stadium;
    }
}
