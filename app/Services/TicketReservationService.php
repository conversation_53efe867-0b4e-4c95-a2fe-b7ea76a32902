<?php

namespace App\Services;

use App\DTO\TicketReservationLockDTO;
use App\Enums\TicketReservationStatus;
use App\Http\Resources\TicketReservation\TicketReservationDetailResource;
use App\Repositories\TicketReservationRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class TicketReservationService
{
    private $ticketReservationRepository;

    private $stripe;

    public function __construct()
    {
        $this->ticketReservationRepository = app(TicketReservationRepository::class);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    public function getAvailableTickets($ticketId)
    {
        return $this->ticketReservationRepository->getReservedTickets($ticketId);
    }

    public function lockTheTransaction(TicketReservationLockDTO $ticketReservationDTO)
    {
        return $this->ticketReservationRepository->reserveTicket($ticketReservationDTO);
    }

    public function getUserActiveReservation()
    {
        $userId = Auth::id();

        return $this->ticketReservationRepository->checkIfUserHasReservationInProgress($userId);
    }

    public function getReservationDetail($reservationId)
    {
        $reservationId = decrypt($reservationId);
        $reservation = $this->ticketReservationRepository->getReservedTicketById($reservationId);

        if ($reservation) {
            $subTotal = $reservation->quantity * $reservation->price;
            $serviceCharge = round($subTotal * config('services.ticketgol.service_charge_rate'), 2);
            $taxRate = round($serviceCharge * config('services.ticketgol.tax_rate'), 2);
            $grandTotal = round($subTotal + $serviceCharge + $taxRate, 2);

            $reservation->subTotal = number_format($subTotal, 2);
            $reservation->serviceCharge = number_format($serviceCharge, 2);
            $reservation->taxRate = number_format($taxRate, 2);
            $reservation->grandTotal = number_format($grandTotal, 2);

            if ($reservation->order) {
                $reservation->order->encryptedOrderId = encrypt($reservation->order->id);
                $paymentIntent = $this->stripe->paymentIntents->retrieve(
                    $reservation->order->purchaseTransaction->payment_intent_id
                );
                $reservation->order->clientSecret = $paymentIntent->client_secret;
            }

            return new TicketReservationDetailResource($reservation);
        }

        return $reservation;
    }

    public function handleActiveReservations()
    {
        $ticketReservations = $this->ticketReservationRepository->getExpiredReservationsByStatus(TicketReservationStatus::ACTIVE);
        Log::channel('stripe')->info('Expired active reservations', ['active_reservations' => $ticketReservations]);

        $this->ticketReservationRepository->updateExpiredRecords($ticketReservations);

        return $ticketReservations;
    }

    public function handleProcessingReservations()
    {
        $ticketReservations = $this->ticketReservationRepository->getExpiredReservationsByStatus(TicketReservationStatus::PROCESSING);
        Log::channel('stripe')->info('Expired processing reservations', ['processing_reservations' => $ticketReservations]);

        $this->ticketReservationRepository->updateExpiredRecords($ticketReservations);

        return $ticketReservations;

    }

    public function isKeyValid($encryptedReservationId)
    {
        $userId = Auth::id();
        $ticketReservationId = $this->ticketReservationRepository->checkIfUserHasReservationInProgress($userId);

        return $ticketReservationId === $encryptedReservationId;
    }
}
