<?php

namespace App\Services;

use App\Repositories\SeasonRepository;
use Illuminate\Support\Collection;

/**
 * Class SeasonService
 *
 * Service class for managing Season, including retrieval, creation, updating, and deletion.
 */
class SeasonService
{
    /**
     * @var SeasonRepository The repository for interacting with Event data.
     */
    protected $seasonRepository;

    /**
     * SeasonRepository constructor.
     *
     * @param  SeasonRepository  $seasonRepository  The repository for interacting with user data.
     */
    public function __construct(SeasonRepository $seasonRepository)
    {
        $this->seasonRepository = $seasonRepository;
    }

    /**
     * Season Dropdown Listing.
     *
     * @return array The Season collection.
     */
    public function seasonOptionsList(): Collection
    {
        return $this->seasonRepository->getSeasonOptionsList();
    }
}
