<?php

namespace App\Services;

use App\DTO\WalletTransactionFilterDTO;
use App\Enums\WalletEntryType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Http\Resources\UserWalletTransaction\UserWalletTransactionCollection;
use App\Http\Resources\UserWalletTransaction\UserWalletTransactionDetailResource;
use App\Repositories\UserWalletRepository;
use App\Repositories\UserWalletTransactionRepository;
use Illuminate\Support\Facades\Auth;

/**
 * Class UserWalletService
 *
 * Service class for managing user wallet and transactions, including retrieval, creation, updating, and deletion.
 */
class UserWalletService
{
    protected $userWalletRepository;

    protected $userWalletTransactionRepository;

    public function __construct(
        UserWalletRepository $userWalletRepository,
        UserWalletTransactionRepository $userWalletTransactionRepository,
    ) {
        $this->userWalletRepository = $userWalletRepository;
        $this->userWalletTransactionRepository = $userWalletTransactionRepository;
    }

    public function getUserWalletDetail($userId)
    {
        $userWallet = $this->userWalletRepository->findUserWallet($userId);

        if (! $userWallet) {
            $userWallet = $this->userWalletRepository->createWallet($userId);
        }

        return $userWallet;
    }

    public function getUserWalletTransactionList(WalletTransactionFilterDTO $filtersDTO): UserWalletTransactionCollection
    {
        $userId = Auth::id();
        $query = $this->userWalletTransactionRepository->getUserWalletTransactions($userId, $filtersDTO);
        $walletTransactions = $query->paginate(config('services.ticketgol.items_per_page'));

        return new UserWalletTransactionCollection($walletTransactions);
    }

    public function getUserWalletTransactionDetail($transactionNo)
    {
        $userId = Auth::id();
        $transaction = $this->userWalletTransactionRepository->getUserWalletTransactionDetail(
            $userId,
            $transactionNo
        );

        if ($transaction) {
            return new UserWalletTransactionDetailResource($transaction);
        }

        return $transaction;
    }

    public function addOrderWalletTransaction($userId, $orderId, $amount, $transactionType)
    {
        $userWallet = $this->getUserWalletDetail($userId);

        $isPenalty = $transactionType === WalletTransactionType::ORDER_PENALTY;

        $newBalance = $isPenalty ? $userWallet->balance - $amount : $userWallet->balance + $amount;

        $userWallet->update(['balance' => $newBalance]);

        $walletTransactionData = [
            'user_id' => $userId,
            'transaction_type' => $transactionType,
            'entry_type' => $isPenalty ? WalletEntryType::DEBIT : WalletEntryType::CREDIT,
            'total_amount' => $amount,
            'withdrawn_amount' => $isPenalty ? $amount : 0,
            'remained_amount' => $isPenalty ? 0 : $amount,
            'currency_code' => $userWallet->currency_code,
            'balance_after' => $newBalance,
            'order_id' => $orderId,
            'status' => WalletTransactionStatus::PENDING,
        ];

        if ($transactionType === WalletTransactionType::ORDER_PENALTY) {
            $walletTransactionData['note'] = 'Order Cancellation Penalty';
        }

        return $this->userWalletTransactionRepository->create($walletTransactionData);
    }

    public function addWithdrawalWalletTransaction($userId, $withdrawalId, $amount)
    {
        $userWallet = $this->getUserWalletDetail($userId);

        $newBalance = $userWallet->balance - $amount;

        $userWallet->update(['balance' => $newBalance]);

        $walletTransactionData = [
            'user_id' => $userId,
            'transaction_type' => WalletTransactionType::WITHDRAWAL,
            'entry_type' => WalletEntryType::DEBIT,
            'total_amount' => $amount,
            'withdrawn_amount' => $amount,
            'remained_amount' => 0,
            'currency_code' => $userWallet->currency_code,
            'balance_after' => $newBalance,
            'withdrawal_id' => $withdrawalId,
            'status' => WalletTransactionStatus::WITHDRAWN,
            'note' => 'Withdrawal Paid',
        ];

        return $this->userWalletTransactionRepository->create($walletTransactionData);
    }

    public function markWalletTransactionToApproved($userId, $amount)
    {
        return $this->userWalletTransactionRepository->approveUserPendingTransactions($userId, $amount);
    }

    public function markWalletTransactionToPaid($userId, $amount)
    {
        $oldTransactions = $this->userWalletTransactionRepository->getUserApprovedTransactions($userId, true);

        $transactions = $this->userWalletTransactionRepository->getUserApprovedTransactions($userId);

        foreach ($transactions as $transaction) {
            $transaction->status = WalletTransactionStatus::WITHDRAWN;

            if ($transaction->entry_type === WalletEntryType::CREDIT) {
                if ($amount >= $transaction->remained_amount) {
                    // Full withdrawal from this transaction
                    $amount -= $transaction->remained_amount;
                    $transaction->remained_amount = 0;
                    $transaction->withdrawn_amount = $transaction->total_amount;
                } else {
                    // Partial withdrawal
                    $transaction->withdrawn_amount = $amount;
                    $transaction->remained_amount -= $amount;
                    $transaction->status = WalletTransactionStatus::PARTIAL_WITHDRAWN;
                    $amount = 0;
                }
            }

            $transaction->save();
        }

        return $oldTransactions;
    }
}
