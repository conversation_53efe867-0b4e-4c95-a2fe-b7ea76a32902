<?php

namespace App\Services\TixStock;

use App\Enums\CurrencyType;
use App\Enums\RestrictionType;
use App\Enums\TicketQuantitySplitType;
use App\Enums\TicketType;
use App\Enums\TixStockQuantitySplitType;
use App\Enums\TixStockTicketType;
use App\Models\Language;
use App\Models\User;
use App\Repositories\EventRepository;
use App\Repositories\RestrictionRepository;
use App\Repositories\StadiumRepository;
use App\Repositories\TicketRepository;
use App\Repositories\TixStockApiLogRepository;
use Illuminate\Support\Str;

/**
 * Class SyncTicketsService
 *
 * Service class for managing tixstock tickets sync, including retrieval, creation, updating, and deletion.
 */
class SyncTicketsService
{
    protected $eventRepository;

    protected $restrictionRepository;

    protected $stadiumRepository;

    protected $ticketRepository;

    protected $tixStockApiLogRepository;

    protected $tixStockApi;

    protected $languages;

    protected $seller;

    protected $ticketsEndpoint = 'tickets/feed';

    protected $ticketsReleaseEndpoint = 'tickets/release/{hold_id}/{quantity_to_release}';

    public function __construct()
    {
        $this->eventRepository = app(EventRepository::class);
        $this->restrictionRepository = app(RestrictionRepository::class);
        $this->stadiumRepository = app(StadiumRepository::class);
        $this->ticketRepository = app(TicketRepository::class);
        $this->tixStockApiLogRepository = app(TixStockApiLogRepository::class);
        $this->tixStockApi = app(TixStockApiService::class);
    }

    public function handle()
    {
        $params = [];
        $params['per_page'] = config('service.tixstock.events_per_page');
        $params['ticket_types'][] = TixStockTicketType::E_TICKET->value;
        $params['ticket_types'][] = TixStockTicketType::MOBILE->value;

        $this->languages = Language::select('locale')->where('is_active', 1)->get();

        $this->seller = User::where('email', config('services.tixstock.user_email'))->first();

        $this->getTicketsList($this->ticketsEndpoint, $params);
    }

    protected function getTicketsList($endpoint, $params)
    {
        $response = $this->tixStockApi->get($endpoint, $params);

        if (! empty($response->data)) {
            $this->saveTickets($response->data);
        }

        if ($response->links && $response->links->next) {
            $params['page'] = $response->meta->current_page + 1;

            return $this->getTicketsList($endpoint, $params);
        }
    }

    protected function saveTickets($tickets)
    {
        foreach ($tickets as $ticketItem) {
            $ticket = $this->ticketRepository->fetchTixStockTicketDetail($ticketItem);
            $event = $this->eventRepository->fetchTixStockEventDetail($ticketItem->event);

            if (! $event) {
                continue;
            }
            $sector = $this->stadiumRepository->getStadiumSector($event->stadium_id, $ticketItem);

            $ticketData = [
                'event_id' => $event->id,
                'ticket_type' => TixStockTicketType::tryFrom($ticketItem->ticket->type)?->toTicketType()
                    ?? TicketType::E_TICKET,
                'quantity_split_type' => TixStockQuantitySplitType::tryFrom($ticketItem->ticket->split_type)?->toSplitType() ?? TicketQuantitySplitType::ANY,
                'currency_code' => CurrencyType::EUR->value,
                'quantity' => $ticketItem->number_of_tickets_for_sale->quantity_available,
                'sold_quantity' => $ticketItem->number_of_tickets_for_sale->quantity_sold,
                'sector_id' => $sector->id,
                'ticket_rows' => $ticketItem->seat_details->row ?? null,
                'ticket_seats' => $ticketItem->seat_details->first_seat ?? null,
                'tixstock_data' => json_encode($ticketItem),
                'is_active' => true,
            ];

            if ($ticketItem->ticket->split_type === TixStockQuantitySplitType::SELL_IN_MULTIPLES->value) {
                $ticketData['sell_in_multiples'] = $ticketItem->number_of_tickets_for_sale->split_quantity ?? null;
            }

            if ($ticket) {
                $oldData = json_decode($ticket->tixstock_data);
                if (isset($oldData->proceed_price->amount) && $ticketItem->proceed_price->amount !== $oldData->proceed_price->amount) {
                    $ticketData['price'] = $this->getConvertedPrice($ticketItem->proceed_price);
                }

                if (isset($oldData->face_value->amount) && $ticketItem->face_value->amount !== $oldData->face_value->amount) {
                    $ticketData['face_value_price'] = $this->getConvertedPrice($ticketItem->face_value);
                }
            } else {
                $ticketData['price'] = $this->getConvertedPrice($ticketItem->proceed_price);
                $ticketData['face_value_price'] = $this->getConvertedPrice($ticketItem->face_value);
            }

            if (! $ticket) {
                $ticketData['seller_id'] = $this->seller->id;
                $ticketData['tixstock_id'] = $ticketItem->id;

                $ticket = $this->ticketRepository->create($ticketData);

                $translations = [];
                foreach ($this->languages as $value) {
                    $translations[] = [
                        'locale' => $value->locale,
                        'description' => 'TixStock Ticket Listing',
                    ];
                }

                $ticket->translations()->createMany($translations);
            } else {
                $ticket->update($ticketData);
            }

            if (! empty($ticketItem->restrictions_benefits->options)) {
                $this->handleTicketRestrictions($ticket, $ticketItem->restrictions_benefits->options);
            }
        }
    }

    protected function getConvertedPrice($ticketPrice)
    {
        $currentRate = $this->tixStockApi->getCurrencyRate($ticketPrice->currency);

        return round($ticketPrice->amount * $currentRate->rates->EUR, 2);
    }

    protected function handleTicketRestrictions($ticket, $tixStockRestrictions)
    {
        $restrictions = [];

        foreach ($tixStockRestrictions as $txRestriction) {
            $restriction = $this->restrictionRepository->getTicketRestriction($txRestriction);

            if (! $restriction) {
                $restrictionData['type'] = RestrictionType::TICKET->value;
                $restrictionData['is_active'] = 1;

                $restriction = $this->restrictionRepository->createRestriction($restrictionData);

                $translations = [];
                foreach ($this->languages as $value) {
                    $translations[] = [
                        'locale' => $value->locale,
                        'name' => $txRestriction,
                    ];
                }

                $restriction->translations()->createMany($translations);
            }

            $restrictions[] = $restriction->id;
        }

        $ticket->restrictions()->sync($restrictions);
    }

    public function updateTicketStatus()
    {
        $tixStockTickets = $this->ticketRepository->getTixStockListings();

        foreach ($tixStockTickets as $ticket) {
            $params = [];
            $params['listing_id'] = $ticket->tixstock_id;

            $response = $this->tixStockApi->get($this->ticketsEndpoint, $params);

            if (empty($response->data)) {
                $ticket->is_active = false;
                $ticket->save();
            }
        }
    }

    public function releaseTicket($reservation)
    {
        $endpoint = Str::replace(
            ['{hold_id}', '{quantity_to_release}'],
            [$reservation->tixstock_hold_id, $reservation->quantity],
            $this->ticketsReleaseEndpoint
        );

        $response = $this->tixStockApi->post($endpoint);

        $this->tixStockApiLogRepository->createApiLog($endpoint, [], $response);
    }
}
