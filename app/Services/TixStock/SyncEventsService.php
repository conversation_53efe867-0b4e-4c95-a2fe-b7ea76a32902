<?php

namespace App\Services\TixStock;

use App\Enums\EmailTemplateKeys;
use App\Enums\EventCategoryType;
use App\Enums\LanguageCode;
use App\Jobs\SendEmailJob;
use App\Models\GeneralSetting;
use App\Models\Language;
use App\Repositories\ClubRepository;
use App\Repositories\CountryRepository;
use App\Repositories\EventRepository;
use App\Repositories\LeagueRepository;
use App\Repositories\StadiumRepository;
use DateTimeZone;
use Illuminate\Support\Str;

/**
 * Class SyncEventsService
 *
 * Service class for managing tixstock events sync, including retrieval, creation, updating, and deletion.
 */
class SyncEventsService
{
    protected $clubRepository;

    protected $countryRepository;

    protected $eventRepository;

    protected $leagueRepository;

    protected $stadiumRepository;

    protected $tixStockApi;

    protected $languages;

    public function __construct()
    {
        $this->clubRepository = app(ClubRepository::class);
        $this->countryRepository = app(CountryRepository::class);
        $this->eventRepository = app(EventRepository::class);
        $this->leagueRepository = app(LeagueRepository::class);
        $this->stadiumRepository = app(StadiumRepository::class);
        $this->tixStockApi = app(TixStockApiService::class);
    }

    public function handle()
    {
        $params = [];
        $params['per_page'] = config('service.tixstock.events_per_page');
        $params['include_listings'] = false;
        $params['category_name'] = config('service.tixstock.event_category');
        $params['has_listing'] = true;
        $endpoint = 'feed';

        $this->languages = Language::select('locale')->where('is_active', 1)->get();

        $this->getEventsList($endpoint, $params);
    }

    protected function getEventsList($endpoint, $params, $totalAdded = 0)
    {
        $response = $this->tixStockApi->get($endpoint, $params);

        $added = $this->saveEvents($response->data);

        $totalAdded += $added;

        if ($response->links && $response->links->next) {
            $params['page'] = $response->meta->current_page + 1;

            return $this->getEventsList($endpoint, $params, $totalAdded);
        }

        if ($totalAdded > 0) {
            $meta = [
                'locale' => LanguageCode::ENGLISH->value,
                'newEventCount' => $totalAdded,
            ];

            $adminEmail = GeneralSetting::getValue('admin_email');

            SendEmailJob::dispatch(EmailTemplateKeys::ADMIN_TIXSTOCK_EVENT_SYNCED_EMAIL->value, $meta, [], [$adminEmail]);
        }
    }

    protected function saveEvents($events)
    {
        $added = 0;

        foreach ($events as $eventItem) {

            $event = $this->eventRepository->fetchTixStockEventDetail($eventItem);

            if (! $event) {
                $country = $this->countryRepository->getCountryByCode($eventItem->venue->country_code);

                $stadium = $this->getStadiumDetail($eventItem->venue, $country);

                $league = $this->getLeagueDetail($eventItem->category, $country);

                $clubs = $this->getClubsDetail($eventItem->performers, $country);

                $eventData['date'] = date('Y-m-d', strtotime($eventItem->datetime));
                $eventData['time'] = date('H:i:s', strtotime($eventItem->datetime));

                $timezones = DateTimeZone::listIdentifiers(DateTimeZone::PER_COUNTRY, $country->shortcode);

                $eventData['timezone'] = $timezones[0] ?? config('app.timezone');

                $eventData['category'] = EventCategoryType::SPORT;
                $eventData['country_id'] = $country->id;
                $eventData['stadium_id'] = $stadium->id;
                $eventData['league_id'] = $league->id;

                $eventData['home_club_id'] = $clubs[0]->id;
                $eventData['guest_club_id'] = $clubs[1]->id;
                $eventData['is_published'] = false;
                $eventData['tixstock_id'] = $eventItem->id;
                $eventData['tixstock_data'] = json_encode($eventItem);

                $event = $this->eventRepository->create($eventData);

                $translations = [];
                $slugs = [];

                foreach ($this->languages as $value) {
                    $translations[] = [
                        'locale' => $value->locale,
                        'name' => $eventItem->name,
                        'description' => $eventItem->name,
                    ];

                    $slugs[$value->locale] = Str::slug($eventItem->name);
                }

                $event->translations()->createMany($translations);

                $event->setSlugs($slugs);

                $sectors = $stadium->sectors->whereNotNull('parent_id')->pluck('id')->toArray();

                if (! empty($sectors)) {
                    $event->stadiumSectors()->sync($sectors);
                }

                $added++;
            }

            if ($event && ! $event->tixstock_id) {
                $event->tixstock_id = $eventItem->id;
                $event->tixstock_data = json_encode($eventItem);
                $event->save();
            }
        }

        return $added;
    }

    protected function getStadiumDetail($venue, $country)
    {
        $stadium = $this->stadiumRepository->fetchTixStockStadiumDetail($venue);

        if (! $stadium) {

            $endpoint = 'venues/feed';

            $params['id'] = $venue->id;
            $params['has_listing'] = true;

            $venueDetail = $this->tixStockApi->get($endpoint, $params);

            $addressLine2 = collect([$venue->address_line_2, $venue->city, $venue->state]);

            $stadiumData['address_line_1'] = $venue->address_line_1;
            $stadiumData['address_line_2'] = $addressLine2->filter()->implode(', ');
            $stadiumData['country_id'] = $country->id;
            $stadiumData['postcode'] = $venue->postcode;
            $stadiumData['tixstock_id'] = $venue->id;
            $stadiumData['is_published'] = false;

            $venueSections = [];

            if (! empty($venueDetail->data[0]->events)) {
                $venueSections = $venueDetail->data[0]->events[0]->venue_details;
            }

            $stadium = $this->stadiumRepository->create($stadiumData);

            $translations = $slugs = [];
            foreach ($this->languages as $value) {
                $translations[] = [
                    'locale' => $value->locale,
                    'name' => $venue->name,
                ];
                $slugs[$value->locale] = Str::slug($venue->name);
            }

            $stadium->translations()->createMany($translations);

            $stadium->setSlugs($slugs);

            foreach ($venueSections as $value) {
                $sector['name'] = $value->name;

                $newSector = $stadium->sectors()->create($sector);

                $childSectors = [];
                foreach ($value->sections as $section) {
                    if ($section != $value->name) {
                        $childSectors[] = [
                            'parent_id' => $newSector->id,
                            'name' => trim(str_replace($value->name, '', $section)),
                        ];
                    }
                }

                if (! empty($childSectors)) {
                    $stadium->sectors()->createMany($childSectors);
                }
            }
        }

        if ($stadium && ! $stadium->tixstock_id) {
            $stadium->tixstock_id = $venue->id;
            $stadium->save();
        }

        return $stadium;
    }

    protected function getLeagueDetail($category, $country)
    {
        $league = $this->leagueRepository->fetchTixStockLeagueDetail($category);

        if (! $league) {
            $leagueData['country_id'] = $country->id;
            $leagueData['tixstock_id'] = $category->id;
            $leagueData['is_published'] = false;

            $league = $this->leagueRepository->create($leagueData);

            $translations = $slugs = [];
            foreach ($this->languages as $value) {
                $translations[] = [
                    'locale' => $value->locale,
                    'name' => $category->name,
                    'description' => $category->name,
                ];

                $slugs[$value->locale] = Str::slug($category->name);
            }
            $league->translations()->createMany($translations);
            $league->setSlugs($slugs);
        }

        if ($league && ! $league->tixstock_id) {
            $league->tixstock_id = $category->id;
            $league->save();
        }

        return $league;
    }

    protected function getClubsDetail($performers, $country)
    {
        $clubs = [];

        foreach ($performers as $performer) {
            $club = $this->clubRepository->fetchTixStockClubDetail($performer);

            if (! $club) {
                $clubData['country_id'] = $country->id;
                $clubData['tixstock_id'] = $performer->id;
                $clubData['is_active'] = false;

                $club = $this->clubRepository->create($clubData);

                $translations = $slugs = [];
                foreach ($this->languages as $value) {
                    $translations[] = [
                        'locale' => $value->locale,
                        'name' => $performer->name,
                        'description' => $performer->name,
                        'detailed_description' => $performer->name,
                    ];

                    $slugs[$value->locale] = Str::slug($performer->name);
                }

                $club->translations()->createMany($translations);
                $club->setSlugs($slugs);
            }

            if ($club && ! $club->tixstock_id) {
                $club->tixstock_id = $performer->id;
                $club->save();
            }

            $clubs[] = $club;
        }

        return $clubs;
    }
}
