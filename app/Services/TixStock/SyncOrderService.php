<?php

namespace App\Services\TixStock;

use App\Enums\TixStockOrderStatus;
use App\Repositories\OrderRepository;
use App\Repositories\TixStockApiLogRepository;
use Carbon\Carbon;
use Illuminate\Support\Str;

/**
 * Class SyncOrderService
 *
 * Service class for managing tixstock order sync, including retrieval, creation, updating, and deletion.
 */
class SyncOrderService
{
    protected $orderRepository;

    protected $tixStockApiLogRepository;

    protected $tixStockApi;

    protected $orderAddEndpoint = 'orders/add/{hold_id}/{quantity_sold}';

    protected $orderUpdateEndpoint = 'orders/update/{external_order_id}';

    public function __construct()
    {
        $this->orderRepository = app(OrderRepository::class);
        $this->tixStockApiLogRepository = app(TixStockApiLogRepository::class);
        $this->tixStockApi = app(TixStockApiService::class);
    }

    public function createOrder($orderId)
    {
        $order = $this->orderRepository->findById($orderId);

        if (! $order->tixstock_id && $order->ticketReservation->tixstock_hold_id) {
            $orderData = $this->prepareOrderData($order);

            $endpoint = Str::replace(
                ['{hold_id}', '{quantity_sold}'],
                [$order->ticketReservation->tixstock_hold_id, $order->quantity],
                $this->orderAddEndpoint
            );

            $response = $this->tixStockApi->post($endpoint, $orderData);

            if (isset($response->data) && ! empty($response->data)) {
                $order->tixstock_id = $response->data->id;
                $order->tixstock_status = $response->data->status;
                $order->tixstock_response = json_encode($response);
                $order->save();
            }

            $this->tixStockApiLogRepository->createApiLog($endpoint, $orderData, $response);
        }
    }

    public function updateOrder($orderId)
    {
        $order = $this->orderRepository->findById($orderId);

        if ($order->tixstock_id && $order->ticketReservation->tixstock_hold_id) {
            $orderData = $this->prepareOrderData($order);

            $endpoint = Str::replace('{external_order_id}', $order->order_no, $this->orderUpdateEndpoint);

            $response = $this->tixStockApi->post($endpoint, $orderData);

            if (isset($response->data) && ! empty($response->data)) {
                $order->tixstock_status = $response->data->status;
                $order->tixstock_response = json_encode($response);
                $order->save();
            }

            $this->tixStockApiLogRepository->createApiLog($endpoint, $orderData, $response);
        }
    }

    protected function prepareOrderData($order)
    {
        $tixStockTicketData = json_decode($order->ticket->tixstock_data);

        $orderData = [
            'order_id' => $order->order_no,
            'order_status' => TixStockOrderStatus::fromOrderStatus($order->status),
            'datetime' => Carbon::now('UTC')->format('Y-m-d\TH:i:sO'),
            'currency' => $tixStockTicketData?->proceed_price->currency,
            'customer' => [
                'id' => (string) $order->buyer->id,
                'first_name' => $order->buyer->name,
                'email_address' => $order->buyer->email,
            ],
        ];

        $orderItems = [];

        foreach ($order->attendees as $attendee) {
            $orderItems[] = [
                'general_admission' => filter_var($tixStockTicketData?->ticket->general_admission, FILTER_VALIDATE_BOOLEAN),
                'price' => $tixStockTicketData?->proceed_price->amount,
                'row' => $tixStockTicketData?->seat_details->row,
                'customer' => [
                    'first_name' => $attendee->name,
                    'email_address' => $attendee->email,
                    'dob' => $attendee->dob,
                ],
            ];
        }

        $orderData['items'] = $orderItems;

        return $orderData;
    }
}
