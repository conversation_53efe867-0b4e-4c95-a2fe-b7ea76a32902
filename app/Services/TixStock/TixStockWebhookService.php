<?php

namespace App\Services\TixStock;

use App\Enums\OrderStatus;
use App\Enums\TixStockOrderStatus;
use App\Jobs\SendOrderStatusUpdateEmailJob;
use App\Jobs\UpdateTixStockOrderJob;
use App\Repositories\EventRepository;
use App\Repositories\OrderRepository;
use App\Repositories\OrderTransactionRepository;
use App\Repositories\TicketRepository;
use App\Repositories\TixStockApiLogRepository;
use Illuminate\Support\Str;

/**
 * Class TixStockWebhookService
 *
 * Service class for managing tixstock webhook
 */
class TixStockWebhookService
{
    protected $eventRepository;

    protected $ticketRepository;

    protected $orderRepository;

    protected $orderTransactionRepository;

    protected $tixStockApiLogRepository;

    protected $tixStockApi;

    protected $ticketsEndpoint = 'tickets/feed';

    public function __construct()
    {
        $this->eventRepository = app(EventRepository::class);
        $this->ticketRepository = app(TicketRepository::class);
        $this->orderRepository = app(OrderRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->tixStockApiLogRepository = app(TixStockApiLogRepository::class);
        $this->tixStockApi = app(TixStockApiService::class);
    }

    public function handleHoldAndReleaseTicket($event)
    {
        $params['listing_id'] = $event->data->id;

        $response = $this->tixStockApi->get($this->ticketsEndpoint, $params);

        if (! empty($response->data)) {

            $ticketItem = $response->data[0];

            $event = $this->eventRepository->fetchTixStockEventDetail($ticketItem->event);
            $ticket = $this->ticketRepository->fetchTixStockTicketDetail($ticketItem);

            if ($event && $ticket) {
                $ticketData['quantity'] = $ticketItem->number_of_tickets_for_sale->quantity_available;
                $ticketData['sold_quantity'] = $ticketItem->number_of_tickets_for_sale->quantity_sold;

                $ticket->update($ticketData);
            }
        }
    }

    public function handleOrderUpdate($event)
    {
        $order = $this->orderRepository->getTixStockOrderDetail($event->data->id);

        if ($order) {
            $orderStatus = TixStockOrderStatus::from($event->data->status)->toOrderStatus();

            if ($order->status !== $orderStatus) {
                $updateData = ['status' => $orderStatus, 'tixstock_data' => json_encode($event)];
                $this->orderRepository->update($order->id, $updateData);
            }
        }
    }

    public function handleOrderTicketsFile($event)
    {
        $order = $this->orderRepository->getOrderDetailByNumber($event->data->order_id);

        if ($order) {
            $order->addMediaFromBase64($event->data->file)
                ->usingName(Str::random('10'))
                ->usingFileName(Str::uuid().'.pdf')
                ->toMediaCollection('tickets', 'admin');

            if (! empty($event->data->additional_file)) {
                $order->addMediaFromUrl($event->data->additional_file)
                    ->usingFileName(Str::uuid().'.pdf')
                    ->toMediaCollection('additional_doc', 'admin');
            }

            if ($order->getMedia('tickets')->count() === $order->attendees->count()) {
                $updateData = ['status' => OrderStatus::SHIPPED];
                $this->orderRepository->update($order->id, $updateData);

                UpdateTixStockOrderJob::dispatch($order->id);
                SendOrderStatusUpdateEmailJob::dispatch($order->id, 'seller');
            }
        }
    }

    public function addWebhookLog($event, $response = [])
    {
        $eventType = $event->meta->type;

        return $this->tixStockApiLogRepository->createWebhookLog($eventType, $event, $response);
    }
}
