<?php

namespace App\Services\TixStock;

use App\Enums\CurrencyType;
use Illuminate\Support\Facades\Http;

/**
 * Class TixStockApiService
 *
 * Service class for managing tixstock api, including retrieval, creation, updating, and deletion.
 */
class TixStockApiService
{
    protected $baseUrl;

    protected $bearerToken;

    protected $currencyApiUrl;

    public function __construct()
    {
        $this->baseUrl = config('services.tixstock.base_url');
        $this->bearerToken = config('services.tixstock.bearer_token');
        $this->currencyApiUrl = config('services.tixstock.currency_api_url');
    }

    public function get(string $endpoint, array $params = [])
    {
        return Http::withToken($this->bearerToken)
            ->get($this->baseUrl.$endpoint, $params)
            ->object();
    }

    public function post(string $endpoint, array $data = [])
    {
        return Http::withToken($this->bearerToken)
            ->post($this->baseUrl.$endpoint, $data)
            ->object();
    }

    public function getCurrencyRate($fromCurrency)
    {
        $url = $this->currencyApiUrl.'latest?from='.$fromCurrency.'&to='.CurrencyType::EUR->value;

        return Http::get($url)->object();
    }
}
