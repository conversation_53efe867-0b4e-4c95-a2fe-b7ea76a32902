<?php

namespace App\Services\TixStock;

use Illuminate\Support\Facades\Http;

/**
 * Class TixStockApiService
 *
 * Service class for managing tixstock api, including retrieval, creation, updating, and deletion.
 */
class TixStockApiService
{
    protected $baseUrl;

    protected $bearerToken;

    public function __construct()
    {
        $this->baseUrl = config('services.tixstock.base_url');
        $this->bearerToken = config('services.tixstock.bearer_token');
    }

    public function get(string $endpoint, array $params = [])
    {
        return Http::withToken($this->bearerToken)
            ->get($this->baseUrl.$endpoint, $params)
            ->object();
    }

    public function post(string $endpoint, array $data = [])
    {
        return Http::withToken($this->bearerToken)
            ->post($this->baseUrl.$endpoint, $data)
            ->object();
    }
}
