<?php

namespace App\Services;

use App\DTO\EventFilterDTO;
use App\DTO\SellTicketsFilterDTO;
use App\Enums\TicketQuantitySplitType;
use App\Enums\TicketReservationStatus;
use App\Http\Resources\Event\EventCollection;
use App\Http\Resources\Event\EventDetailResource;
use App\Repositories\EventRepository;

/**
 * Class EventService
 *
 * Service class for managing Events, including retrieval, creation, updating, and deletion.
 */
class EventService
{
    /**
     * @var EventRepository The repository for interacting with Event data.
     */
    protected $eventRepository;

    /**
     * EventRepository constructor.
     *
     * @param  EventRepository  $eventRepository  The repository for interacting with user data.
     */
    public function __construct(EventRepository $eventRepository)
    {
        $this->eventRepository = $eventRepository;
    }

    /**
     * Events Listing.
     *
     * @return EventCollection The events EventCollection.
     */
    public function eventsList(EventFilterDTO $filtersDTO): EventCollection
    {
        $query = $this->eventRepository->getEventsList($filtersDTO);

        $events = $query->paginate(config('services.ticketgol.events_per_page'))
            ->withPath(route('api.events.index'));

        return new EventCollection($events);
    }

    /**
     * Events Listing for sell tickets.
     *
     * @return EventCollection The events EventCollection.
     */
    public function eventsListForSellTickets(SellTicketsFilterDTO $filtersDTO): EventCollection
    {
        $query = $this->eventRepository->getEventsListForSellTickets($filtersDTO);

        $events = $query->paginate(config('services.ticketgol.events_per_page'));

        return new EventCollection($events);
    }

    /**
     * Event Details by slug.
     *
     * @param  string  $slug  Event Slug
     * @return EventDetailResource The events EventDetailResource.
     */
    public function getEventDetailBySlug(string $slug): ?EventDetailResource
    {
        $event = $this->eventRepository->getEventDetail($slug);
        if ($event) {
            $event->category_label = $event->category->getLabel();
            $event->max_quantity = $event->tickets()
                ->leftJoin('ticket_reservations as r', function ($join) {
                    $join->on('r.ticket_id', '=', 'tickets.id')
                        ->whereIn('status', [TicketReservationStatus::ACTIVE, TicketReservationStatus::PROCESSING]);
                })
                ->whereNull('tickets.deleted_at')
                ->selectRaw('tickets.id, tickets.quantity - COALESCE(SUM(r.quantity), 0) as available_quantity')
                ->groupBy('tickets.id', 'tickets.quantity')
                ->pluck('available_quantity')
                ->max();

            $event->quantitySplitEnums = TicketQuantitySplitType::keyValue();
            $event->tempReservationMinutes = config('services.ticketgol.temp_reservation_minutes');

            $event->min_price = $event->min_price ?? 0;
            $event->max_price = $event->max_price ?? 10; // Set max price to 10 avoid range slider collassion

            return new EventDetailResource($event);
        }

        return $event;
    }
}
