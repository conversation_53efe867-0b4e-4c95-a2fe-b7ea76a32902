<?php

namespace App\Services;

use App\DTO\SearchFilterDTO;
use App\Repositories\ClubRepository;
use App\Repositories\EventRepository;
use App\Repositories\LeagueRepository;
use App\Repositories\StadiumRepository;

/**
 * Class SearchService
 *
 * Service class for managing home page events listing.
 */
class SearchService
{
    protected $eventRepository;

    protected $clubRepository;

    protected $leagueRepository;

    protected $stadiumRepository;

    /**
     * SearchService constructor.
     *
     * @param  ClubRepository  $clubRepository  The repository for interacting with clubs data.
     * @param  EventRepository  $eventRepository  The repository for interacting with events data.
     * @param  LeagueRepository  $leagueRepository  The repository for interacting with leagues data.
     * @param  StadiumRepository  $stadiumRepository  The repository for interacting with staidums data.
     */
    public function __construct(
        ClubRepository $clubRepository,
        EventRepository $eventRepository,
        LeagueRepository $leagueRepository,
        StadiumRepository $stadiumRepository,
    ) {
        $this->clubRepository = $clubRepository;
        $this->eventRepository = $eventRepository;
        $this->leagueRepository = $leagueRepository;
        $this->stadiumRepository = $stadiumRepository;
    }

    public function getSearchResults(SearchFilterDTO $searchDTO)
    {
        $page = $searchDTO->page;
        $perPage = config('services.ticketgol.searches_per_page');

        $events = $this->eventRepository->searchEvents($searchDTO)->get();
        $clubs = $this->clubRepository->searchClubs($searchDTO)->get();
        $leagues = $this->leagueRepository->searchLeagues($searchDTO)->get();
        $stadiums = $this->stadiumRepository->searchStadiums($searchDTO)->get();

        $results = $events->concat($clubs)->concat($leagues)->concat($stadiums)->sortBy('name')->values();

        $total = $results->count();
        $paginated = $results->forPage($page, $perPage)->values();

        return [
            'data' => $paginated,
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
            ],
        ];
    }

    public function getSearchSuggestions(SearchFilterDTO $searchDTO)
    {
        $perPage = config('services.ticketgol.searches_per_page');

        $events = $this->eventRepository->searchEvents($searchDTO)->limit($perPage)->get();
        $clubs = $this->clubRepository->searchClubs($searchDTO)->limit($perPage)->get();
        $leagues = $this->leagueRepository->searchLeagues($searchDTO)->limit($perPage)->get();
        $stadiums = $this->stadiumRepository->searchStadiums($searchDTO)->limit($perPage)->get();

        $results = $events->concat($clubs)->concat($leagues)->concat($stadiums);

        return [
            'data' => $results->take($perPage)->values(),
            'total' => $results->count(),
        ];
    }
}
