<?php

namespace App\Services;

use App\DTO\WithdrawalFilterDTO;
use App\DTO\WithdrawalStoreDTO;
use App\Enums\WithdrawalStatus;
use App\Http\Resources\UserWithdrawal\UserWithdrawalCollection;
use App\Jobs\SendNewWithdrawalRequestEmailJob;
use App\Repositories\UserPayoutMethodRepository;
use App\Repositories\UserWalletRepository;
use App\Repositories\UserWithdrawalRepository;
use App\Traits\CommonTrait;
use Illuminate\Support\Facades\Auth;

/**
 * Class UserWithdrawalService
 *
 * Service class for managing user withdrawal requests, including retrieval, creation, updating, and deletion.
 */
class UserWithdrawalService
{
    use CommonTrait;

    protected $userWithdrawalRepository;

    protected $userWalletRepository;

    protected $userPayoutMethodRepository;

    /**
     * UserWithdrawalRepository constructor.
     *
     * @param  UserWithdrawalRepository  $userWithdrawalRepository  The repository for interacting with user data.
     */
    public function __construct(
        UserWithdrawalRepository $userWithdrawalRepository,
        UserWalletRepository $userWalletRepository,
        UserPayoutMethodRepository $userPayoutMethodRepository,
    ) {
        $this->userWithdrawalRepository = $userWithdrawalRepository;
        $this->userWalletRepository = $userWalletRepository;
        $this->userPayoutMethodRepository = $userPayoutMethodRepository;
    }

    public function getUserWithdrawalList(WithdrawalFilterDTO $filtersDTO)
    {
        $user = Auth::user();

        $query = $this->userWithdrawalRepository->getUserWithdrawals($user->id, $filtersDTO);
        $withdrawals = $query->paginate(config('services.ticketgol.items_per_page'));

        return new UserWithdrawalCollection($withdrawals);
    }

    public function getWithdrawalConfigurations()
    {
        $user = Auth::user();

        $wallet = $this->userWalletRepository->findUserWallet($user->id);
        if (! $wallet) {
            $wallet = $this->userWalletRepository->createWallet($user->id);
        }

        $userPayoutMethods = $this->userPayoutMethodRepository->getUserPayoutMethods($user->id);
        $payoutMethodOptions = $userPayoutMethods->map(function ($method) {
            return [
                'label' => $method->bank_name.' - '.$this->maskAccountNumber($method->account_number),
                'value' => $method->id,
            ];
        })->values();

        return [
            'payoutMethodOptions' => $payoutMethodOptions,
            'balance' => $wallet->balance,
            'currency_code' => $wallet->currency_code,
            'min_withdrawal_limit' => config('services.ticketgol.min_withdrawal_limit'),
            'max_withdrawal_limit' => config('services.ticketgol.max_withdrawal_limit'),
        ];
    }

    public function addWithdrawalRequest(WithdrawalStoreDTO $withdrawalData)
    {
        $data = $withdrawalData->toArray();
        $data['status'] = WithdrawalStatus::PENDING;
        $withdrawal = $this->userWithdrawalRepository->createWithdrawalRequest($data);

        SendNewWithdrawalRequestEmailJob::dispatch($withdrawal->id);

        return $withdrawal;
    }

    public function checkUserCanWithdraw()
    {
        $user = Auth::user();

        if ($user->wallet && $user->wallet->balance < config('services.ticketgol.min_withdrawal_limit')) {
            return [
                'can_withdraw' => false,
                'withdraw_message' => __('message.WITHDRAWAL_INSUFFICIENT_AMOUNT'),
            ];
        }

        $checkWithdrawal = $this->userWithdrawalRepository->getUserPendingWithdrawals($user->id);

        if ($checkWithdrawal) {
            return [
                'can_withdraw' => false,
                'withdraw_message' => __('message.WITHDRAWAL_REQUEST_PENDING'),
            ];
        }

        return [
            'can_withdraw' => true,
            'withdraw_message' => __('message.SUCCESS'),
        ];
    }

    public function deleteWithdrawalRequest($withdrawalId)
    {
        $withdrawal = $this->userWithdrawalRepository->findById($withdrawalId);
        if ($withdrawal && $withdrawal->user_id == Auth::id()) {
            $result = $this->userWithdrawalRepository->deleteById($withdrawal->id);

            return $result ? 'WITHDRAWAL_REQUEST_DELETED' : 'SOMETHING_WENT_WRONG';
        }

        return 'WITHDRAWAL_REQUEST_NOT_FOUND';
    }
}
