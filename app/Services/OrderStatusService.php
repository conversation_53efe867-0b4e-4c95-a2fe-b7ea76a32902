<?php

namespace App\Services;

use App\DTO\OrderOpenDisputeDTO;
use App\DTO\OrderStatusCheckDTO;
use App\DTO\OrderStatusUpdateDTO;
use App\DTO\OrderTicketsUploadDTO;
use App\Enums\OrderStatus;
use App\Enums\OrderTransactionStatus;
use App\Enums\OrderTransactionType;
use App\Enums\StripePaymentIntentStatus;
use App\Enums\UserType;
use App\Enums\WalletTransactionType;
use App\Jobs\SendOrderOpenDisputeEmailJob;
use App\Jobs\SendOrderStatusUpdateEmailJob;
use App\Jobs\SendSupportRequestCreationEmailJob;
use App\Repositories\OrderRepository;
use App\Repositories\OrderStatusHistoryRepository;
use App\Repositories\OrderTransactionRepository;
use App\Repositories\SupportRequestRepository;
use Stripe\StripeClient;

class OrderStatusService
{
    private $orderRepository;

    private $orderStatusHistoryRepository;

    private $orderTransactionRepository;

    private $supportRequestRepository;

    private $stripe;

    public function __construct()
    {
        $this->orderRepository = app(OrderRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
        $this->orderStatusHistoryRepository = app(OrderStatusHistoryRepository::class);
        $this->supportRequestRepository = app(SupportRequestRepository::class);
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    public function checkOrderStatus(OrderStatusCheckDTO $orderStatusDTO)
    {
        $orderId = decrypt($orderStatusDTO->orderId);
        $order = $this->orderRepository->findById($orderId);

        if ($order) {

            if ($orderStatusDTO->checkPaymentStatus && $order->status === OrderStatus::PENDING) {
                $paymentIntent = $this->stripe->paymentIntents->retrieve($order->purchaseTransaction->payment_intent_id);
                if ($paymentIntent->status === StripePaymentIntentStatus::SUCCEEDED->value) {
                    $stripeWebhookService = app(StripeWebhookService::class);
                    $order = $stripeWebhookService->confirmOrderAndTransaction($order);
                }
            }

            $orderTransaction = $order->purchaseTransaction;

            return [
                'order_id' => $order->id,
                'status' => $order->status,
                'is_completed' => ($order->status === OrderStatus::PROCESSING || $order->status === OrderStatus::CONFIRMED)
                 && $orderTransaction->status === OrderTransactionStatus::COMPLETED,
            ];
        }

        return $order;
    }

    public function updateOrderStatus(OrderStatusUpdateDTO $orderStatusDTO)
    {
        $order = $this->orderRepository->findById($orderStatusDTO->orderId);

        if ($order) {
            $updateData = ['status' => $orderStatusDTO->status];

            // Commenting for now as no direct cancellation done by Seller
            // if ($orderStatusDTO->status === OrderStatus::CANCELED->value) {
            //     $updateData = $this->handleCancelledOrder($order, $orderStatusDTO, $updateData);
            // }

            $this->orderRepository->update($order->id, $updateData);
            $this->orderStatusHistoryRepository->addStatusHistory($orderStatusDTO->toArray(), $order->status);

            if ($orderStatusDTO->status === OrderStatus::UNDER_REVIEW->value) {
                $this->handleUnderReviewOrder($order, $orderStatusDTO);
            }

            if ($orderStatusDTO->status === OrderStatus::ON_DISPUTE->value) {
                $this->handleOnDisputeOrder($order, $orderStatusDTO);
            }

            if ($orderStatusDTO->status === OrderStatus::SHIPPED->value) {
                $this->handleOrderTicketsAndDoc($order, $orderStatusDTO);
            }

            SendOrderStatusUpdateEmailJob::dispatch($order->id);
        }

        return $order;
    }

    public function openOrderDispute(OrderOpenDisputeDTO $openDisputeDTO)
    {
        $order = $this->orderRepository->findById($openDisputeDTO->orderId);

        if ($order) {
            $updateData = ['status' => $openDisputeDTO->status];

            $this->orderRepository->update($order->id, $updateData);
            $this->orderStatusHistoryRepository->addStatusHistory($openDisputeDTO->toArray(), $order->status);

            $requestData = [
                'user_id' => $openDisputeDTO->userId,
                'order_id' => $order->id,
                'subject' => 'Order #'.$order->order_no.' Marked as In Dispute by Buyer',
                'message' => $openDisputeDTO->reason,
            ];

            $supportRequest = $this->supportRequestRepository->createOrderSupportRequest($requestData);

            SendOrderOpenDisputeEmailJob::dispatch($order->id, $openDisputeDTO->toArray());
            SendSupportRequestCreationEmailJob::dispatch($supportRequest->id);
        }

        return $order;
    }

    public function reUploadOrderTickets(OrderTicketsUploadDTO $ticketsUploadDTO)
    {
        $order = $this->orderRepository->findById($ticketsUploadDTO->orderId);

        if ($order) {
            if ($ticketsUploadDTO->tickets) {
                $order->clearMediaCollection('tickets');
            }
            if ($ticketsUploadDTO->additionalDoc) {
                $order->clearMediaCollection('additional_doc');
            }
            $this->handleOrderTicketsAndDoc($order, $ticketsUploadDTO);
        }

        return $order;
    }

    public function completePastShippedOrders()
    {
        $orders = $this->orderRepository->getPastShippedOrders();

        foreach ($orders as $order) {
            $updateData = ['status' => OrderStatus::COMPLETED];

            $this->orderRepository->update($order->id, $updateData);

            $statusData = [
                'order_id' => $order->id,
                'status' => OrderStatus::COMPLETED,
            ];

            $this->orderStatusHistoryRepository->addStatusHistory($statusData, $order->status);

            $userWalletService = app(UserWalletService::class);

            $userWalletService->addOrderWalletTransaction(
                $order->seller_id,
                $order->id,
                $order->total_price,
                WalletTransactionType::ORDER_EARNING
            );

            SendOrderStatusUpdateEmailJob::dispatch($order->id);
        }

        return $orders;
    }

    protected function handleCancelledOrder($order, $orderStatusDTO, $updateData)
    {
        if ($order->purchaseTransaction->payment_intent_id) {
            $refund = $this->stripe->refunds->create([
                'payment_intent' => $order->purchaseTransaction->payment_intent_id,
                'metadata' => [
                    'order_id' => $order->id,
                ],
            ]);

            if ($refund->status === 'pending' || $refund->status === 'succeeded') {
                $this->orderTransactionRepository->createRefundTransaction(
                    $order,
                    OrderTransactionType::REFUND,
                    $refund
                );

                $paymentIntent = $this->stripe->paymentIntents->retrieve(
                    $order->purchaseTransaction->payment_intent_id,
                    [
                        'expand' => ['latest_charge.balance_transaction'],
                    ]
                );

                $originalTransaction = $paymentIntent->latest_charge->balance_transaction;

                $refundTransaction = $this->stripe->balanceTransactions->retrieve($refund->balance_transaction);

                $originalNetAmount = $originalTransaction->net / config('services.ticketgol.stripe_cent_unit');
                $refundNetAmount = $refundTransaction->net / config('services.ticketgol.stripe_cent_unit');

                // Calculate final loss based on actual + refund transaction net amount in stripe
                $finalLossAmount = abs($originalNetAmount + $refundNetAmount);

                if ($refundTransaction->exchange_rate) {
                    $finalLossAmount = round($finalLossAmount / $refundTransaction->exchange_rate, 2);
                }

                $updateData['penalty_amount'] = $finalLossAmount;
                $updateData['penalty_user_type'] = UserType::BROKER->value;
                $updateData['penalty_user_id'] = $orderStatusDTO->userId;

                $userWalletService = app(UserWalletService::class);

                $userWalletService->addOrderWalletTransaction(
                    $orderStatusDTO->userId,
                    $order->id,
                    $finalLossAmount,
                    WalletTransactionType::ORDER_PENALTY
                );

                if ($order->ticket->sold_quantity >= $order->quantity) {
                    $order->ticket->decrement('sold_quantity', $order->quantity);
                    $order->ticket->increment('quantity', $order->quantity);
                }
            }
        }

        return $updateData;
    }

    protected function handleUnderReviewOrder($order, $orderStatusDTO)
    {
        $requestData = [
            'user_id' => $orderStatusDTO->userId,
            'order_id' => $order->id,
            'subject' => 'Order #'.$order->order_no.' Marked as Under Review',
            'message' => $orderStatusDTO->reason,
        ];

        $supportRequest = $this->supportRequestRepository->createOrderSupportRequest($requestData);

        SendSupportRequestCreationEmailJob::dispatch($supportRequest->id);
    }

    protected function handleOnDisputeOrder($order, $orderStatusDTO)
    {
        $requestData = [
            'user_id' => $orderStatusDTO->userId,
            'order_id' => $order->id,
            'subject' => 'Order #'.$order->order_no.' Marked as In Dispute by Seller',
            'message' => $orderStatusDTO->reason,
        ];

        $supportRequest = $this->supportRequestRepository->createOrderSupportRequest($requestData);

        SendSupportRequestCreationEmailJob::dispatch($supportRequest->id);
    }

    protected function handleOrderTicketsAndDoc($order, $uploadDTO)
    {
        foreach ($uploadDTO->tickets as $file) {
            $order->addMedia($file)->toMediaCollection('tickets', 'admin');
        }

        if ($uploadDTO->additionalDoc) {
            $order->addMedia($uploadDTO->additionalDoc)->toMediaCollection('additional_doc', 'admin');
        }
    }
}
