<?php

namespace App\Contracts;

/**
 * Interface DTOInterface
 *
 * Base interface for all Data Transfer Objects in the application.
 * DTOs are used to transfer data between subsystems of the application.
 */
interface DTOInterface
{
    /**
     * Convert the DTO to an array representation.
     *
     * @return array The array representation of the DTO.
     */
    public function toArray(): array;
}
