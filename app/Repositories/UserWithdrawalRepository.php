<?php

namespace App\Repositories;

use App\DTO\WithdrawalFilterDTO;
use App\Enums\WithdrawalStatus;
use App\Models\UserWithdrawal;

/**
 * Class UserWithdrawalRepository
 *
 * Repository class for interacting with the `UserWithdrawal` model.
 */
class UserWithdrawalRepository extends BaseRepository
{
    /**
     * UserWithdrawalRepository constructor.
     *
     * @param  UserWithdrawal  $model  The underlying model for the repository.
     */
    public function __construct(UserWithdrawal $model)
    {
        $this->model = $model;
    }

    public function createWithdrawalRequest($withdrawalData)
    {
        return $this->model->create($withdrawalData);
    }

    public function getUserWithdrawals($userId, WithdrawalFilterDTO $filtersDTO)
    {
        return $this->model->select('id', 'user_id', 'withdraw_no', 'amount', 'payout_method_id', 'status', 'note', 'payment_reference_number', 'approved_at', 'paid_at', 'created_at')
            ->with([
                'payoutMethod:id,bank_name,account_number',
            ])
            ->where('user_id', $userId)
            ->when(! empty($filtersDTO->search), function ($query) use ($filtersDTO) {
                $query->where(function ($q) use ($filtersDTO) {
                    $q->where('withdraw_no', 'like', '%'.$filtersDTO->search.'%')
                        ->orWhereHas('payoutMethod', function ($q) use ($filtersDTO) {
                            $q->where('bank_name', 'like', '%'.$filtersDTO->search.'%')
                                ->orWhere('account_number', 'like', '%'.$filtersDTO->search.'%');
                        });
                });
            })
            ->when(! empty($filtersDTO->dateFrom), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '>=', $filtersDTO->dateFrom);
            })
            ->when(! empty($filtersDTO->dateTo), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '<=', $filtersDTO->dateTo);
            })
            ->when(! empty($filtersDTO->status), function ($query) use ($filtersDTO) {
                $query->where('status', $filtersDTO->status);
            })
            ->when(! empty($filtersDTO->sort), function ($query) use ($filtersDTO) {
                $sort = $filtersDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('created_at', 'desc');
            });
    }

    public function getUserPendingWithdrawals($userId)
    {
        return $this->model->where('user_id', $userId)
            ->whereIn('status', [WithdrawalStatus::PENDING->value, WithdrawalStatus::APPROVED->value])
            ->latest()
            ->first();
    }

    public function getUserWithdrawalCounts($userId, $filterRange)
    {
        return $this->model->where('user_id', $userId)
            ->when(! empty($filterRange), function ($query) use ($filterRange) {
                $query->whereBetween('created_at', $filterRange);
            })
            ->count();
    }
}
