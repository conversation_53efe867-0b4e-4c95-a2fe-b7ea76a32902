<?php

namespace App\Repositories;

use App\Models\TixstockApiLog;

class TixStockApiLogRepository extends BaseRepository
{
    public function __construct(TixstockApiLog $model)
    {
        $this->model = $model;
    }

    public function createApiLog($endpoint, $requestData, $responseData = [])
    {
        return $this->model->create([
            'event_type' => 'api',
            'endpoint' => $endpoint,
            'request_data' => json_encode($requestData),
            'response_data' => json_encode($responseData),
        ]);
    }

    public function createWebhookLog($webhookType, $requestData, $responseData = [])
    {
        return $this->model->create([
            'event_type' => 'webhook',
            'webhook_type' => $webhookType,
            'request_data' => json_encode($requestData),
            'response_data' => json_encode($responseData),
        ]);
    }
}
