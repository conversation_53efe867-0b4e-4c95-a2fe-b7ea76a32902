<?php

namespace App\Repositories;

use App\Models\UserPayoutMethod;

class UserPayoutMethodRepository extends BaseRepository
{
    public function __construct(UserPayoutMethod $model)
    {
        $this->model = $model;
    }

    public function getUserPayoutMethods($userId)
    {
        return $this->model->select('id', 'payout_method_type', 'user_id', 'bank_name', 'account_number', 'is_default')
            ->where('user_id', $userId)
            ->get();
    }

    public function createUserPayoutMethod($payoutMethodStoreDTO)
    {
        return $this->model->create($payoutMethodStoreDTO);
    }

    public function markPayoutMethodAsDefault($payoutMethodId, $userId)
    {
        $payoutMethod = $this->findById($payoutMethodId);

        if ($payoutMethod && $payoutMethod->user_id == $userId) {
            $this->model->where('user_id', $userId)->update(['is_default' => false]);

            return $payoutMethod->update(['is_default' => true]);
        }

        return false;
    }
}
