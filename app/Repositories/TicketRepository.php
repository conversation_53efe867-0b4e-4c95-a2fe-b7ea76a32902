<?php

namespace App\Repositories;

use App\DTO\EventTicketFilterDTO;
use App\DTO\MyTicketFilterDTO;
use App\DTO\TicketStoreDTO;
use App\DTO\TicketUpdateDTO;
use App\Enums\TicketQuantitySplitType;
use App\Models\Language;
use App\Models\Ticket;
use App\Models\TicketReservation;
use App\Traits\TableNameTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TicketRepository extends BaseRepository
{
    use TableNameTrait;

    /**
     * TicketRepository constructor.
     *
     * @param  Ticket  $model  The underlying model for the repository.
     */
    public function __construct(Ticket $model)
    {
        $this->model = $model;
    }

    /**
     * Get Event Tickets Listing
     *
     * @return Builder Return event tickets list with Pagination
     */
    public function getEventTicketsList(EventTicketFilterDTO $filterDTO): Builder
    {
        $reservationSubquery = TicketReservation::select('ticket_id', DB::raw('SUM(quantity) as total_reserved_qty'))
            ->reserved()
            ->groupBy('ticket_id');

        return $this->model->select(self::getTicketTable().'.id', 'price', 'quantity', 'ticket_type', 'quantity_split_type', 'sell_in_multiples', 'sector_id', DB::raw('('.self::getTicketTable().'.quantity - COALESCE(r.total_reserved_qty, 0)) as remain_qty'))
            ->with(['sector:id,name,parent_id', 'sector.parent:id,name'])
            ->leftJoinSub($reservationSubquery, 'r', self::getTicketTable().'.id', '=', 'r.ticket_id')
            ->where('is_active', 1)
            ->when(! empty($filterDTO->eventId), function ($query) use ($filterDTO) {
                $query->where('event_id', $filterDTO->eventId);
            })
            ->when(! empty($filterDTO->sector), function ($query) use ($filterDTO) {
                $query->where('sector_id', $filterDTO->sector);
            })
            ->when(! empty($filterDTO->quantity), function ($query) use ($filterDTO) {
                $q = $filterDTO->quantity;

                $query->where(function ($sub) use ($q) {
                    $remainQty = DB::raw('('.self::getTicketTable().'.quantity - COALESCE(r.total_reserved_qty, 0))');

                    $sub->where(function ($q1) use ($remainQty, $q) {
                        $q1->where('quantity_split_type', TicketQuantitySplitType::ANY->value)
                            ->where($remainQty, '>=', $q);
                    })->orWhere(function ($q2) use ($q) {
                        $q2->where('quantity_split_type', TicketQuantitySplitType::SINGLE->value)
                            ->whereRaw('? = 1', [$q]);
                    })->orWhere(function ($q3) use ($remainQty, $q) {
                        $q3->where('quantity_split_type', TicketQuantitySplitType::AVOID_ONE->value)
                            ->where($remainQty, '>=', $q)
                            ->whereRaw('(('.self::getTicketTable().'.quantity - COALESCE(r.total_reserved_qty, 0)) - ?) != 1', [$q]);
                    })->orWhere(function ($q4) use ($remainQty, $q) {
                        $q4->where('quantity_split_type', TicketQuantitySplitType::AVOID_ONE_THREE->value)
                            ->where($remainQty, '>=', $q)
                            ->whereRaw('(('.self::getTicketTable().'.quantity - COALESCE(r.total_reserved_qty, 0)) - ?) NOT IN (1, 3)', [$q]);
                    })->orWhere(function ($q5) use ($remainQty, $q) {
                        $q5->where('quantity_split_type', TicketQuantitySplitType::AVOID_ODD->value)
                            ->where($remainQty, '>=', $q)
                            ->whereRaw('MOD((('.self::getTicketTable().'.quantity - COALESCE(r.total_reserved_qty, 0)) - ?), 2) = 0', [$q]);
                    })
                        ->orWhere(function ($q6) use ($remainQty, $q) {
                            $q6->where('quantity_split_type', TicketQuantitySplitType::ALL_TOGETHER->value)
                                ->where($remainQty, $q);
                        })
                        ->orWhere(function ($q7) use ($remainQty, $q) {
                            $q7->where('quantity_split_type', TicketQuantitySplitType::IN_MULTIPLE->value)
                                ->whereNotNull('sell_in_multiples')
                                ->whereRaw('sell_in_multiples > 0')
                                ->where($remainQty, '>=', $q)
                                ->whereRaw('MOD(('.self::getTicketTable().'.quantity - COALESCE(r.total_reserved_qty, 0)), sell_in_multiples) = 0')
                                ->whereRaw('MOD(?, sell_in_multiples) = 0', [$q]);
                        });
                });
            })
            ->when(! empty($filterDTO->ticketType), function ($query) use ($filterDTO) {
                $query->where('ticket_type', '=', $filterDTO->ticketType);
            })
            ->when(! empty($filterDTO->priceRange), function ($query) use ($filterDTO) {
                $query->where('price', '>=', $filterDTO->priceRange[0])
                    ->where('price', '<=', $filterDTO->priceRange[1]);
            })->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('price', 'asc');
            })
            ->having('remain_qty', '>', 0);
    }

    /**
     * Get Tickets Listing for logged in user
     *
     * @return Builder Return tickets list with Pagination
     */
    public function getMyTicketsList(MyTicketFilterDTO $filterDTO): Builder
    {
        return $this->model->select('id', 'ticket_no', 'price', 'quantity', 'event_id', 'sector_id', 'ticket_type', 'sold_quantity', 'created_at')
            ->with([
                'event:id,date',
                'event.translation:event_id,name',
                'event.media',
                'sector:id,name,parent_id',
                'sector.parent:id,name',
            ])
            ->withCount('orders')
            ->withCount(['reservations' => function ($query) {
                $query->reserved();
            }])
            ->withSum(['reservations' => function ($query) {
                $query->reserved();
            }], 'quantity')
            ->where('seller_id', Auth::id())
            ->when(! empty($filterDTO->search), function ($query) use ($filterDTO) {
                $search = strtolower($filterDTO->search);

                $query->where(function ($q) use ($search) {
                    $q->where(DB::raw('LOWER(ticket_no)'), 'like', "%{$search}%")
                        ->orWhereHas('event.translation', function ($q) use ($search) {
                            $q->where(DB::raw('LOWER(name)'), 'like', "%{$search}%");
                        })
                        ->orWhereHas('sector', function ($q) use ($search) {
                            $q->where(DB::raw('LOWER(name)'), 'like', "%{$search}%")
                                ->orWhereHas('parent', function ($q2) use ($search) {
                                    $q2->where('name', 'like', "%{$search}%");
                                });
                        });
                });
            })
            ->when(! empty($filterDTO->ticketType), function ($query) use ($filterDTO) {
                $query->where('ticket_type', '=', $filterDTO->ticketType);
            })
            ->when(! empty($filterDTO->dateFrom), function ($query) use ($filterDTO) {
                $query->whereDate('created_at', '>=', $filterDTO->dateFrom);
            })
            ->when(! empty($filterDTO->dateTo), function ($query) use ($filterDTO) {
                $query->whereDate('created_at', '<=', $filterDTO->dateTo);
            })
            ->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('created_at', 'desc');
            });
    }

    /**
     * Add ticket listing
     *
     * @return Model Return ticket object after creation
     */
    public function addTicket(TicketStoreDTO $ticketData): ?Model
    {
        $languages = Language::select('locale')->where('is_active', 1)->get();

        $ticket = $this->model->create([
            'event_id' => $ticketData->eventId,
            'seller_id' => Auth::id(),
            'price' => $ticketData->price,
            'quantity' => $ticketData->quantity,
            'currency_code' => $ticketData->currencyCode,
            'ticket_type' => $ticketData->ticketType,
            'sector_id' => $ticketData->sectorId,
            'ticket_rows' => $ticketData->ticketRows,
            'ticket_seats' => $ticketData->ticketSeats,
            'face_value_price' => $ticketData->faceValuePrice,
            'quantity_split_type' => $ticketData->quantitySplitType,
            'sell_in_multiples' => $ticketData->sellInMultiples ?? null,
            'is_active' => 1,
        ]);

        $translations = [];
        foreach ($languages as $value) {
            $translations[] = [
                'locale' => $value->locale,
                'description' => $ticketData->description,
            ];
        }

        $ticket->translations()->createMany($translations);

        if (! empty($ticketData->restrictions)) {
            $ticket->restrictions()->sync($ticketData->restrictions);
        }

        return $ticket;
    }

    /**
     * Update ticket listing
     *
     * @return Model Return ticket object after update
     */
    public function updateTicket(TicketUpdateDTO $ticketData): ?Model
    {
        $ticket = $this->findById($ticketData->ticketId);

        $languages = Language::select('locale')->where('is_active', 1)->get();

        $ticket->update([
            'price' => $ticketData->price,
            'quantity' => $ticketData->quantity,
            'currency_code' => $ticketData->currencyCode,
            'ticket_type' => $ticketData->ticketType,
            'ticket_rows' => $ticketData->ticketRows,
            'ticket_seats' => $ticketData->ticketSeats,
            'face_value_price' => $ticketData->faceValuePrice,
            'quantity_split_type' => $ticketData->quantitySplitType,
            'sell_in_multiples' => $ticketData->sellInMultiples ?? null,
        ]);

        $translations = [];
        foreach ($languages as $value) {
            $translations[] = [
                'locale' => $value->locale,
                'description' => $ticketData->description,
                'ticket_id' => $ticket->id,
                'updated_at' => now(),
            ];
        }

        $ticket->translations()->upsert(
            $translations,
            ['ticket_id', 'locale'],
            ['description', 'updated_at']
        );

        if (! empty($ticketData->restrictions)) {
            $ticket->restrictions()->sync($ticketData->restrictions);
        }

        return $ticket;
    }

    public function handleTicketQuantity($ticketId, $quantity)
    {
        $ticket = $this->findById($ticketId);
        if ($ticket->quantity >= $quantity) {
            $ticket->decrement('quantity', $quantity);
            $ticket->increment('sold_quantity', $quantity);

            return true;
        }

        return false;
    }

    public function getMyTicketDetail($ticketNo): ?Model
    {
        return $this->model
            ->with([
                'translation:ticket_id,description',
                'event:id',
                'event.translation:id,event_id,name',
                'sector:id,name,parent_id',
                'sector.parent:id,name',
                'restrictions:id',
                'restrictions.translation:restriction_id,locale,name',
            ])
            ->withSum(['reservations' => function ($query) {
                $query->reserved();
            }], 'quantity')
            ->where('seller_id', Auth::id())
            ->where('ticket_no', $ticketNo)
            ->first();
    }

    public function fetchTixStockTicketDetail($txTicket)
    {
        return $this->model
            ->where(function ($query) use ($txTicket) {
                $query->whereHas('event', function ($q) use ($txTicket) {
                    $q->where('tixstock_id', $txTicket->event_id);
                })->where('tixstock_id', $txTicket->id);
            })
            ->first();
    }

    public function getTixStockTicket($tixstockId)
    {
        return $this->model->where('tixstock_id', $tixstockId)->first();
    }

    public function getTixStockListings()
    {
        return $this->model->whereNotNull('tixstock_id')->where('is_active', 1)->get();
    }

    public function getUserTicketCounts($userId, $field, $filterRange)
    {
        return $this->model->where('seller_id', $userId)
            ->when(! empty($filterRange), function ($query) use ($filterRange) {
                $query->whereBetween('created_at', $filterRange);
            })
            ->sum($field);
    }
}
