<?php

namespace App\Repositories;

use App\Enums\CurrencyType;
use App\Models\UserWallet;

/**
 * Class UserWalletRepository
 *
 * Repository class for interacting with the `UserWallet` model.
 */
class UserWalletRepository extends BaseRepository
{
    /**
     * UserWalletRepository constructor.
     *
     * @param  UserWallet  $model  The underlying model for the repository.
     */
    public function __construct(UserWallet $model)
    {
        $this->model = $model;
    }

    public function createWallet($userId)
    {
        return $this->model->create([
            'user_id' => $userId,
            'balance' => 0,
            'currency_code' => CurrencyType::EUR->value,
        ]);
    }

    public function findUserWallet($userId)
    {
        return $this->model->where('user_id', $userId)->first();
    }
}
