<?php

namespace App\Repositories;

use App\DTO\LeagueFilterDTO;
use App\DTO\SearchFilterDTO;
use App\Models\League;
use App\Traits\TableNameTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Class LeagueRepository
 *
 * Repository class for interacting with the `League` model.
 */
class LeagueRepository extends BaseRepository
{
    use TableNameTrait;

    /**
     * LeagueRepository constructor.
     *
     * @param  League  $model  The underlying model for the repository.
     */
    public function __construct(League $model)
    {
        $this->model = $model;
    }

    public function getLeagueOptionsList(): Collection
    {
        return $this->model->select('id')->with(['translation:league_id,name'])
            ->where('is_published', 1)
            ->get()
            ->pluck('translation.name', 'id');
    }

    /**
     * Get Leagues Listing
     *
     * @return Builder Return Leagues list query
     */
    public function getLeaguesList(LeagueFilterDTO $filterDTO): Builder
    {
        return $this->model->with(['media'])
            ->select(self::getLeagueTable().'.id', 'lt.name as league_name')
            ->leftJoin(self::getLeagueTransTable().' as lt', function ($join) {
                $join->on(self::getLeagueTable().'.id', '=', 'lt.league_id')
                    ->where('lt.locale', app()->getLocale());
            })
            ->where('is_published', 1)
            ->when(! empty($filterDTO->search), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->where('lt.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('lt.description', 'LIKE', "%{$filterDTO->search}%");
                });
            })
            ->when(! empty($filterDTO->countries), function ($query) use ($filterDTO) {
                $query->whereIn(self::getLeagueTable().'.country_id', $filterDTO->countries);
            })
            ->when(! empty($filterDTO->seasons), function ($query) use ($filterDTO) {
                $query->whereIn(self::getLeagueTable().'.season_id', $filterDTO->seasons);
            })
            ->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy(self::getLeagueTable().'.id', 'desc');
            });
    }

    /**
     * Search Leagues
     *
     * @return Builder Return Leagues with search filter
     */
    public function searchLeagues(SearchFilterDTO $searchDTO): Builder
    {
        return $this->model->select(self::getLeagueTable().'.id', 'lt.name')
            ->leftJoin(self::getLeagueTransTable().' as lt', function ($join) {
                $join->on(self::getLeagueTable().'.id', '=', 'lt.league_id')
                    ->where('lt.locale', app()->getLocale());
            })
            ->where('is_published', 1)
            ->where('lt.name', 'LIKE', "%{$searchDTO->search}%")
            ->addSelect(DB::raw("'league' as type"))
            ->orderBy(self::getLeagueTable().'.id', 'desc');
    }

    /**
     * Get League Detail
     *
     * @param  string  $slug
     * @return Model Return League detail object
     */
    public function getLeagueDetail($slug): ?Model
    {
        $relations = [
            'translation:id,league_id,locale,name,description,meta_title,meta_description,meta_keywords',
            'country:id,shortcode',
            'country.translation:country_id,locale,name',
            'media',
        ];

        return $this->model->select('id', 'country_id')
            ->with($relations)
            ->whereSlug($slug)
            ->where('is_published', 1)
            ->first();
    }

    public function fetchTixStockLeagueDetail($category)
    {
        return $this->model
            ->where(function ($query) use ($category) {
                $query->whereHas('translations', function ($q) use ($category) {
                    $q->where('locale', 'en')
                        ->where('name', 'LIKE', "%{$category->name}%");
                })->orWhere('tixstock_id', $category->id);
            })
            ->first();
    }
}
