<?php

namespace App\Repositories;

use App\Enums\OrderTransactionStatus;
use App\Models\OrderTransaction;

class OrderTransactionRepository extends BaseRepository
{
    public function __construct(OrderTransaction $model)
    {
        $this->model = $model;
    }

    public function initiateTransaction($order, $orderDTO, $transactionType)
    {
        return $this->model->create([
            'status' => OrderTransactionStatus::PENDING,
            'order_id' => $order->id,
            'currency_code' => $orderDTO->currencyCode,
            'transaction_type' => $transactionType,
        ]);
    }

    public function createRefundTransaction($order, $transactionType, $refund)
    {
        return $this->model->create([
            'status' => OrderTransactionStatus::PENDING,
            'order_id' => $order->id,
            'total_amount' => $refund->amount / config('services.ticketgol.stripe_cent_unit'),
            'currency_code' => $order->currency_code,
            'transaction_type' => $transactionType,
            'stripe_refund_id' => $refund->id,
            'payment_intent_id' => $refund->payment_intent,
            'stripe_charge_id' => $refund->charge,
        ]);
    }
}
