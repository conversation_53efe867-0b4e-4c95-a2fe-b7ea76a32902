<?php

namespace App\Repositories;

use App\DTO\MySalesFilterDTO;
use App\DTO\OrderFilterDTO;
use App\Enums\OrderStatus;
use App\Models\Order;
use Illuminate\Support\Facades\Auth;

class OrderRepository extends BaseRepository
{
    public function __construct(Order $model)
    {
        $this->model = $model;
    }

    public function createOrderWithAttendees($checkoutDTO, $ticket)
    {
        $user = Auth::user();

        $order = $this->model->create([
            'buyer_id' => $user->id,
            'ticket_id' => $ticket->id,
            'quantity' => $checkoutDTO->quantity,
            'total_price' => $checkoutDTO->quantity * $ticket->price,
            'status' => OrderStatus::PENDING,
            'created_by' => $user->id,
            'purchase_date' => now(),
        ]);

        $order->attendees()->createMany($checkoutDTO->attendees);

        return $order;
    }

    public function createOrder($orderDTO)
    {
        $user = Auth::user();

        $order = $this->model->create([
            'buyer_id' => $user->id,
            'ticket_id' => $orderDTO->ticketId,
            'seller_id' => $orderDTO->sellerId,
            'ticket_reservation_id' => $orderDTO->tempTicketReservationId,
            'quantity' => $orderDTO->quantity,
            'currency_code' => $orderDTO->currencyCode,
            'price' => $orderDTO->price,
            'total_price' => $orderDTO->totalPrice,
            'service_charge_amount' => $orderDTO->serviceChargeAmount,
            'tax_amount' => $orderDTO->taxAmount,
            'grand_total' => $orderDTO->grandTotal,
            'status' => OrderStatus::PENDING,
            'created_by' => $user->id,
            'purchase_date' => now(),
        ]);

        $order->attendees()->createMany($orderDTO->attendees);

        return $order;
    }

    public function getOrdersForUser($userId, OrderFilterDTO $filtersDTO)
    {
        $query = $this->model->select('id', 'order_no', 'total_price', 'ticket_id', 'quantity', 'status', 'created_at', 'purchase_date', 'order_meta_data')
            ->with(['ticket:id,event_id', 'ticket.event:id,date,time,timezone'])
            ->where('buyer_id', $userId)
            ->where('status', '!=', OrderStatus::EXPIRED)
            ->when(! empty($filtersDTO->search), function ($query) use ($filtersDTO) {
                $query->where(function ($query) use ($filtersDTO) {
                    $query->where('order_no', 'LIKE', "%{$filtersDTO->search}%");
                });
            })
            ->when(! empty($filtersDTO->status), function ($query) use ($filtersDTO) {
                $query->where('status', $filtersDTO->status);
            })
            ->when(! empty($filtersDTO->dateFrom), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '>=', $filtersDTO->dateFrom);
            })
            ->when(! empty($filtersDTO->dateTo), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '<=', $filtersDTO->dateTo);
            });

        return $query->orderBy('created_at', 'desc');
    }

    public function getSalesOrders($sellerId, MySalesFilterDTO $filtersDTO)
    {
        return $this->model->select('id', 'order_no', 'buyer_id', 'ticket_id', 'quantity', 'price', 'total_price', 'purchase_date', 'status', 'order_meta_data', 'created_at')
            ->with([
                'buyer:id,name',
                'ticket:id,event_id',
                'ticket.event:id,date',
            ])
            ->where('seller_id', $sellerId)
            ->where('status', '!=', OrderStatus::EXPIRED)
            ->when(! empty($filtersDTO->search), function ($query) use ($filtersDTO) {
                $query->where(function ($q) use ($filtersDTO) {
                    $q->where('order_no', 'like', '%'.$filtersDTO->search.'%')
                        ->orWhereHas('ticket', function ($q) use ($filtersDTO) {
                            $q->where('ticket_no', 'like', '%'.$filtersDTO->search.'%');
                        })
                        ->orWhereHas('ticket.event.translation', function ($q) use ($filtersDTO) {
                            $q->where('name', 'like', '%'.$filtersDTO->search.'%');
                        });
                });
            })
            ->when(! empty($filtersDTO->status), function ($query) use ($filtersDTO) {
                $query->where('status', $filtersDTO->status);
            })
            ->when(! empty($filtersDTO->dateFrom), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '>=', $filtersDTO->dateFrom);
            })
            ->when(! empty($filtersDTO->dateTo), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '<=', $filtersDTO->dateTo);
            })
            ->when(! empty($filtersDTO->sort), function ($query) use ($filtersDTO) {
                $sort = $filtersDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('created_at', 'desc');
            });
    }

    public function getOrderWithDetails($userId, $orderId)
    {
        $order = $this->model->with(
            [
                'ticket:id,event_id',
                'ticket.event:id,date,time,timezone',
                'buyer:id,name,email,user_name',
                'buyer.userDetail:id,user_id,address,phone',
                'transactions:id,order_id,transaction_type,currency_code,payment_method_type,total_amount,paid_at,card_brand,card_last_four,refunded_at',
                'attendees:id,order_id,name,email,gender,dob',
                'latestStatusChange:order_id,from_status,to_status,reason',
                'media',
            ]
        )
            ->select(['id', 'order_no', 'buyer_id', 'ticket_id', 'quantity', 'price', 'total_price', 'service_charge_amount', 'tax_amount', 'grand_total', 'status', 'purchase_date', 'description', 'order_meta_data', 'ticket_downloaded_at', 'created_at'])
            ->where('buyer_id', $userId)
            ->where('status', '!=', OrderStatus::EXPIRED)
            ->where('id', $orderId)
            ->first();

        return $order;
    }

    public function getSalesOrderWithDetails($orderNo, $sellerId)
    {
        $order = $this->model->select(['id', 'order_no', 'ticket_id', 'buyer_id', 'seller_id', 'quantity', 'price', 'total_price', 'grand_total', 'service_charge_amount', 'tax_amount', 'order_meta_data', 'status', 'purchase_date', 'description', 'penalty_user_id', 'penalty_amount', 'penalty_user_type', 'ticket_downloaded_at', 'parent_id'])
            ->with([
                'ticket:id,event_id',
                'ticket.event:id,date,time,timezone',
                'buyer:id,name,email,user_name',
                'buyer.userDetail:id,user_id,address,city,country_id,phone,zip',
                'buyer.userDetail.country:id,shortcode',
                'buyer.userDetail.country.translation:country_id,name',
                'transactions:id,order_id,status,transaction_type',
                'latestStatusChange:order_id,from_status,to_status,reason',
                'attendees:id,order_id,name,email,gender,dob',
                'media',
            ])
            ->where('seller_id', $sellerId)
            ->where('order_no', $orderNo)
            ->where('status', '!=', OrderStatus::EXPIRED)
            ->first();

        return $order;
    }

    public function getPastShippedOrders()
    {
        return $this->model->where('status', OrderStatus::SHIPPED)
            ->whereHas('ticket.event', function ($query) {
                $query->whereDate('date', '<=', now()->subDays(7));
            })
            ->get();
    }
}
