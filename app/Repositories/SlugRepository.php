<?php

namespace App\Repositories;

use App\Models\Slug;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SlugRepository
 *
 * Repository class for interacting with the `Slug` model.
 */
class SlugRepository extends BaseRepository
{
    /**
     * SlugRepository constructor.
     *
     * @param  Slug  $model  The underlying model for the repository.
     */
    public function __construct(Slug $model)
    {
        $this->model = $model;
    }

    /**
     * Get Slug Detail
     *
     * @param  string  $slug
     * @return Model Return cms page detail object
     */
    public function getSlugDetail($slug): ?Model
    {
        return $this->model->select('id', 'slug', 'locale', 'sluggable_id', 'sluggable_type')
            ->where('slug', $slug)
            ->where('locale', app()->getLocale())
            ->first();
    }
}
