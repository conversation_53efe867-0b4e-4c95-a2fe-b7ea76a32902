<?php

namespace App\Repositories;

use App\Models\OrderStatusHistory;

class OrderStatusHistoryRepository extends BaseRepository
{
    public function __construct(OrderStatusHistory $model)
    {
        $this->model = $model;
    }

    public function addStatusHistory($orderStatusDTO, $oldStatus)
    {
        return $this->model->create([
            'order_id' => $orderStatusDTO['order_id'],
            'user_id' => $orderStatusDTO['user_id'] ?? null,
            'from_status' => $oldStatus,
            'to_status' => $orderStatusDTO['status'],
            'reason' => $orderStatusDTO['reason'] ?? null,
        ]);
    }
}
