<?php

namespace App\Repositories;

use App\Enums\RestrictionType;
use App\Models\Restriction;
use Illuminate\Support\Collection;

class RestrictionRepository extends BaseRepository
{
    /**
     * RestrictionRepository constructor.
     *
     * @param  Restriction  $model  The underlying model for the repository.
     */
    public function __construct(Restriction $model)
    {
        $this->model = $model;
    }

    public function getTicketRestrictions(): Collection
    {
        return $this->model->where('type', RestrictionType::TICKET->value)
            ->where('is_active', 1)
            ->with(['translation'])
            ->get()
            ->pluck('translation.name', 'translation.restriction_id');
    }

    public function getTicketRestriction($name)
    {
        return $this->model->where('type', RestrictionType::TICKET->value)
            ->where('is_active', 1)
            ->whereHas('translations', function ($q) use ($name) {
                $q->where('locale', 'en')
                    ->where('name', 'LIKE', "%{$name}%");
            })
            ->first();
    }

    public function createRestriction($restrictionData)
    {
        return $this->model->create($restrictionData);
    }
}
