<?php

namespace App\Repositories;

use App\Contracts\BaseRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BaseRepository
 *
 * This class provides a base implementation for repository pattern using Eloquent models.
 * It implements common CRUD operations and provides methods for handling soft deletes.
 */
class BaseRepository implements BaseRepositoryInterface
{
    /**
     * @var Model The Eloquent model instance.
     */
    protected $model;

    /**
     * BaseRepository constructor.
     *
     * @param  Model  $model  The Eloquent model instance.
     */
    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    /**
     * Get the query builder instance.
     *
     * @param  array  $columns  The columns to be selected.
     * @param  array  $relations  The relationships to be eager loaded.
     */
    public function query(array $columns = ['*'], array $relations = []): Builder
    {
        return $this->model->query()->select($columns)->with($relations);
    }

    /**
     * Retrieve all models from the database.
     *
     * @param  array  $columns  The columns to be selected.
     * @param  array  $relations  The relationships to be eager loaded.
     */
    public function all(array $columns = ['*'], array $relations = []): Collection
    {
        return $this->query($columns, $relations)->get();
    }

    /**
     * Retrieve all trashed models from the database.
     */
    public function allTrashed(): Collection
    {
        return $this->model->onlyTrashed()->get();
    }

    /**
     * Find a model by its primary key.
     *
     * @param  int  $modelId  The ID of the model.
     * @param  array  $columns  The columns to be selected.
     * @param  array  $relations  The relationships to be eager loaded.
     * @param  array  $appends  The attributes to append to the model.
     */
    public function findById(
        int $modelId,
        array $columns = ['*'],
        array $relations = [],
        array $appends = []
    ): ?Model {
        return $this->model->select($columns)->with($relations)->findOrFail($modelId)->append($appends);
    }

    /**
     * Find a trashed model by its primary key.
     *
     * @param  int  $modelId  The ID of the trashed model.
     */
    public function findWithTrashedById(int $modelId): ?Model
    {
        return $this->model->withTrashed()->findOrFail($modelId);
    }

    /**
     * Find a trashed model by its primary key.
     *
     * @param  int  $modelId  The ID of the trashed model.
     */
    public function findOnlyTrashedById(int $modelId): ?Model
    {
        return $this->model->onlyTrashed()->findOrFail($modelId);
    }

    /**
     * Create a new model and persist it to the database.
     *
     * @param  array  $payload  The data for the new model.
     */
    public function create(array $payload): Model
    {
        return tap(
            $this->model->create($payload),
            function ($model) {
                $model->refresh();
            }
        );
    }

    /**
     * Update an existing model in the database.
     *
     * @param  int  $modelId  The ID of the model to be updated.
     * @param  array  $payload  The updated attributes.
     */
    public function update(int $modelId, array $payload): bool
    {
        $model = $this->findById($modelId);

        return $model->update($payload);
    }

    /**
     * Delete a model from the database by its primary key.
     *
     * @param  int  $modelId  The ID of the model to be deleted.
     */
    public function deleteById(int $modelId): bool
    {
        return $this->findById($modelId)->delete();
    }

    /**
     * Restore a soft-deleted model by its primary key.
     *
     * @param  int  $modelId  The ID of the model to be restored.
     */
    public function restoreById(int $modelId): bool
    {
        return $this->findOnlyTrashedById($modelId)->restore();
    }

    /**
     * Permanently delete a trashed model by its primary key.
     *
     * @param  int  $modelId  The ID of the trashed model to be permanently deleted.
     */
    public function permanentlyDeleteById(int $modelId): bool
    {
        return $this->findTrashedById($modelId)->forceDelete();
    }
}
