<?php

namespace App\Repositories;

use App\Models\PaymentLog;

class PaymentLogRepository extends BaseRepository
{
    public function __construct(PaymentLog $model)
    {
        $this->model = $model;
    }

    public function createPaymentLog($eventData)
    {
        $data = $eventData->data->object;

        $paymentLog = $this->model->create([
            'order_id' => $data->metadata->order_id ?? null,
            'stripe_event_id' => $eventData->id,
            'event_type' => $eventData->type,
            'event_data_id' => $data->id,
            'status' => $data->status,
            'webhook_data' => json_encode($eventData),
        ]);

        return $paymentLog;
    }
}
