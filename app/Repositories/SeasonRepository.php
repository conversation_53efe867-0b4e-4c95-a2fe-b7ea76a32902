<?php

namespace App\Repositories;

use App\Models\Season;
use Illuminate\Support\Collection;

/**
 * Class SeasonRepository
 *
 * Repository class for interacting with the `Season` model.
 */
class SeasonRepository extends BaseRepository
{
    /**
     * SeasonRepository constructor.
     *
     * @param  Season  $model  The underlying model for the repository.
     */
    public function __construct(Season $model)
    {
        $this->model = $model;
    }

    public function getSeasonOptionsList(): Collection
    {
        return $this->model->select('id')->with(['translation:season_id,name'])
            ->where('is_published', 1)
            ->get()
            ->pluck('translation.name', 'id');
    }
}
