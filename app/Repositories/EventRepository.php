<?php

namespace App\Repositories;

use App\DTO\EventFilterDTO;
use App\DTO\SearchFilterDTO;
use App\DTO\SellTicketsFilterDTO;
use App\Models\Event;
use App\Models\Ticket;
use App\Traits\TableNameTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * Class EventRepository
 *
 * Repository class for interacting with the `Event` model.
 */
class EventRepository extends BaseRepository
{
    use TableNameTrait;

    /**
     * EventRepository constructor.
     *
     * @param  Event  $model  The underlying model for the repository.
     */
    public function __construct(Event $model)
    {
        $this->model = $model;
    }

    protected function getEventListCommonQuery()
    {
        return $this->model->with(['media', 'stadium:id', 'league:id'])
            ->select(self::getEventTable().'.id', 'date', self::getEventTable().'.stadium_id', self::getEventTable().'.league_id', 'et.name as event_name', 'st.name as stadium_name', 'lt.name as league_name')
            ->leftJoin(self::getEventTransTable().' as et', function ($join) {
                $join->on(self::getEventTable().'.id', '=', 'et.event_id')
                    ->where('et.locale', app()->getLocale());
            })
            ->leftJoin(self::getStadiumTransTable().' as st', function ($join) {
                $join->on(self::getEventTable().'.stadium_id', '=', 'st.stadium_id')
                    ->where('st.locale', app()->getLocale());
            })
            ->leftJoin(self::getLeagueTransTable().' as lt', function ($join) {
                $join->on(self::getEventTable().'.league_id', '=', 'lt.league_id')
                    ->where('lt.locale', app()->getLocale());
            })
            ->leftJoin(self::getClubTransTable().' as hc', function ($join) {
                $join->on(self::getEventTable().'.home_club_id', '=', 'hc.club_id')
                    ->where('hc.locale', app()->getLocale());
            })
            ->leftJoin(self::getClubTransTable().' as gc', function ($join) {
                $join->on(self::getEventTable().'.guest_club_id', '=', 'gc.club_id')
                    ->where('gc.locale', app()->getLocale());
            })
            ->where('is_published', 1)
            ->where('date', '>=', date('Y-m-d'))
            ->addSelect([
                'min_price' => Ticket::select(DB::raw('MIN(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id')
                    ->where('is_active', 1),
            ]);
    }

    /**
     * Get Events Listing
     *
     * @return Builder Return events list query
     */
    public function getEventsList(EventFilterDTO $filterDTO): Builder
    {
        return $this->getEventListCommonQuery()
            ->when(! empty($filterDTO->search), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->where('et.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('st.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('lt.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('hc.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('gc.name', 'LIKE', "%{$filterDTO->search}%");
                });
            })
            ->when(! empty($filterDTO->categories), function ($query) use ($filterDTO) {
                $query->whereIn('category', $filterDTO->categories);
            })
            ->when(! empty($filterDTO->stadiums), function ($query) use ($filterDTO) {
                $query->whereIn(self::getEventTable().'.stadium_id', $filterDTO->stadiums);
            })
            ->when(! empty($filterDTO->leagues), function ($query) use ($filterDTO) {
                $query->whereIn(self::getEventTable().'.league_id', $filterDTO->leagues);
            })
            ->when(! empty($filterDTO->countries), function ($query) use ($filterDTO) {
                $query->whereIn(self::getEventTable().'.country_id', $filterDTO->countries);
            })
            ->when(! empty($filterDTO->clubs), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->whereIn('home_club_id', $filterDTO->clubs)
                        ->orWhereIn('guest_club_id', $filterDTO->clubs);
                });
            })
            ->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('date', 'asc');
            })
            ->havingNotNull('min_price');
    }

    /**
     * Get Events Listing
     *
     * @return Builder Return events list query
     */
    public function getEventsListForSellTickets(SellTicketsFilterDTO $filterDTO): Builder
    {
        return $this->getEventListCommonQuery()
            ->when(! empty($filterDTO->search), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->where('et.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('st.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('lt.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('hc.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('gc.name', 'LIKE', "%{$filterDTO->search}%");
                });
            })
            ->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('date', 'asc');
            });
    }

    /**
     * Get Home page events Listing
     *
     * @return Builder Return events list query
     */
    public function getHomePageEvents($filters = []): Builder
    {
        return $this->getEventListCommonQuery()
            ->when(! empty($filters['upcoming']), function ($query) {
                $query->where('is_feature_event', 0)
                    ->orderBy('date', 'asc');
            })
            ->when(! empty($filters['featured']), function ($query) {
                $query->where('is_feature_event', 1);
            })
            ->havingNotNull('min_price');
    }

    /**
     * Search Events
     *
     * @return Builder Return events list query with search filter
     */
    public function searchEvents(SearchFilterDTO $searchDTO): Builder
    {
        return $this->model->select(self::getEventTable().'.id', 'date', 'et.name')
            ->leftJoin(self::getEventTransTable().' as et', function ($join) {
                $join->on(self::getEventTable().'.id', '=', 'et.event_id')
                    ->where('et.locale', app()->getLocale());
            })
            ->where('et.name', 'LIKE', "%{$searchDTO->search}%")
            ->where('date', '>=', date('Y-m-d'))
            ->where('is_published', 1)
            ->addSelect(DB::raw("'event' as type"))
            ->addSelect([
                'min_price' => Ticket::select(DB::raw('MIN(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id'),
            ])
            ->orderBy('events.id', 'desc')
            ->havingNotNull('min_price');
    }

    /**
     * Get Events Detail
     *
     * @param  string  $slug
     * @return Model Return event detail object
     */
    public function getEventDetail($slug): ?Model
    {
        $relations = [
            'translation:event_id,locale,name,description,meta_title,meta_description,meta_keywords',
            'stadium:id,address_line_1,address_line_2,postcode,country_id',
            'stadium.translation:stadium_id,locale,name',
            'stadium.country:id,shortcode',
            'stadium.country.translation:country_id,locale,name',
            'league:id',
            'league.translation:league_id,locale,name',
            'homeClub:id',
            'homeClub.translation:club_id,locale,name',
            'guestClub:id',
            'guestClub.translation:club_id,locale,name',
            'stadiumSectors:id,name,parent_id',
            'stadiumSectors.parent:id,name',
            'restrictions:id',
            'restrictions.translation:restriction_id,locale,name',
            'media',
        ];

        return $this->model->select('id', 'date', 'time', 'timezone', 'category', 'stadium_id', 'league_id', 'home_club_id', 'guest_club_id', 'is_feature_event')
            ->with($relations)
            ->whereSlug($slug)
            ->addSelect([
                'min_price' => Ticket::select(DB::raw('MIN(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id')
                    ->where('is_active', 1),
            ])
            ->addSelect([
                'max_price' => Ticket::select(DB::raw('MAX(price)'))
                    ->whereColumn('event_id', self::getEventTable().'.id')
                    ->where('is_active', 1),
            ])
            ->where('is_published', 1)
            ->first();
    }

    public function getEventWithImage(int $eventId): ?Event
    {
        $model = $this->model->with([
            'media' => function ($query) {
                $query->limit(Event::FIRST_IMAGE);
            },
            'translation',
            'homeClub.translation',
            'guestClub.translation',
            'stadium.translation',
            'country.translation',
        ])
            ->select([
                'id',
                'date',
                'time',
                'home_club_id',
                'guest_club_id',
                'stadium_id',
                'country_id',
            ])
            ->findOrFail($eventId);

        $model->imageUrl = $model->media->first()?->getUrl();

        return $model;
    }

    public function fetchTixStockEventDetail($txEvent)
    {
        return $this->model
            ->where(function ($query) use ($txEvent) {
                $query->whereHas('translations', function ($q) use ($txEvent) {
                    $q->where('locale', 'en')
                        ->where(function ($q2) use ($txEvent) {
                            $q2->where('name', 'LIKE', '%'.$txEvent->name.'%')
                                ->orWhere('name', 'LIKE', '%'.$txEvent->name.' '.$txEvent->category->name.'%')
                                ->orWhere('name', 'LIKE', '%'.$txEvent->category->name.' '.$txEvent->name.'%');
                        });
                })->orWhere('tixstock_id', $txEvent->id);
            })
            ->first();
    }
}
