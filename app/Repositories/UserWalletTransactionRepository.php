<?php

namespace App\Repositories;

use App\DTO\WalletTransactionFilterDTO;
use App\Enums\WalletTransactionStatus;
use App\Models\UserWalletTransaction;

/**
 * Class UserWalletTransactionRepository
 *
 * Repository class for interacting with the `UserWalletTransaction` model.
 */
class UserWalletTransactionRepository extends BaseRepository
{
    /**
     * UserWalletTransactionRepository constructor.
     *
     * @param  UserWalletTransaction  $model  The underlying model for the repository.
     */
    public function __construct(UserWalletTransaction $model)
    {
        $this->model = $model;
    }

    public function getUserWalletTransactions($userId, WalletTransactionFilterDTO $filtersDTO)
    {
        return $this->model->select('id', 'user_id', 'transaction_no', 'transaction_type', 'entry_type', 'total_amount', 'withdrawn_amount', 'remained_amount', 'order_id', 'status', 'created_at')
            ->with([
                'order:id,order_no',
            ])
            ->where('user_id', $userId)
            ->when(! empty($filtersDTO->search), function ($query) use ($filtersDTO) {
                $query->where(function ($q) use ($filtersDTO) {
                    $q->where('transaction_no', 'like', '%'.$filtersDTO->search.'%')
                        ->orWhereHas('order', function ($q) use ($filtersDTO) {
                            $q->where('order_no', 'like', '%'.$filtersDTO->search.'%');
                        });
                });
            })
            ->when(! empty($filtersDTO->dateFrom), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '>=', $filtersDTO->dateFrom);
            })
            ->when(! empty($filtersDTO->dateTo), function ($query) use ($filtersDTO) {
                $query->whereDate('created_at', '<=', $filtersDTO->dateTo);
            })
            ->when(! empty($filtersDTO->transactionType), function ($query) use ($filtersDTO) {
                $query->where('transaction_type', $filtersDTO->transactionType);
            })
            ->when(! empty($filtersDTO->entryType), function ($query) use ($filtersDTO) {
                $query->where('entry_type', $filtersDTO->entryType);
            })
            ->when(! empty($filtersDTO->status), function ($query) use ($filtersDTO) {
                $query->where('status', $filtersDTO->status);
            })
            ->when(! empty($filtersDTO->sort), function ($query) use ($filtersDTO) {
                $sort = $filtersDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy('id', 'desc');
            });
    }

    public function getUserWalletTransactionDetail($userId, $transactionNo)
    {
        return $this->model->select('id', 'user_id', 'transaction_no', 'transaction_type', 'entry_type', 'total_amount', 'withdrawn_amount', 'remained_amount', 'order_id', 'note', 'status', 'created_at')
            ->with([
                'order:id,order_no,ticket_id,buyer_id,seller_id,quantity,price,total_price,grand_total,service_charge_amount,tax_amount,order_meta_data,status,purchase_date,penalty_user_id,penalty_amount,penalty_user_type',
            ])
            ->where('user_id', $userId)
            ->where('transaction_no', $transactionNo)
            ->first();
    }

    public function approveUserPendingTransactions($userId, $amount)
    {
        $sub = $this->model->selectRaw('
                id,
                remained_amount,
                SUM(remained_amount) OVER (PARTITION BY user_id ORDER BY id) AS running_total
            ')
            ->where('user_id', $userId)
            ->whereIn('status', [
                WalletTransactionStatus::PENDING->value,
                WalletTransactionStatus::PARTIAL_WITHDRAWN->value,
            ]);

        $transactions = $this->model->select('user_wallet_transactions.id', 'transaction_no', 'transaction_type', 'total_amount', 'entry_type', 'running_total')
            ->joinSub($sub, 'earnings', function ($join) {
                $join->on('user_wallet_transactions.id', '=', 'earnings.id');
            })
            ->where(function ($q) use ($amount) {
                $q->where('earnings.running_total', '<=', $amount)
                    ->orWhereRaw('earnings.running_total - earnings.remained_amount < ?', [$amount]);
            })
            ->orderBy('earnings.id')
            ->get();

        $ids = $transactions->pluck('id')->toArray();
        if (! empty($ids)) {
            $this->model->whereIn('id', $ids)->update(['status' => WalletTransactionStatus::APPROVED->value]);
        }

        return $ids;
    }

    public function getUserApprovedTransactions($userId, $loadOrder = false)
    {
        return $this->model->where('user_id', $userId)
            ->where('status', WalletTransactionStatus::APPROVED->value)
            ->when($loadOrder, function ($query) {
                $query->with(['order:id,order_no']);
            })
            ->get();
    }
}
