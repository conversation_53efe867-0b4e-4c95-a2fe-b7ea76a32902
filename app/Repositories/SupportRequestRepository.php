<?php

namespace App\Repositories;

use App\DTO\SupportTicketFilterDTO;
use App\DTO\SupportTicketStoreDTO;
use App\Enums\MediaLibrary;
use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestStatus;
use App\Enums\SupportRequestType;
use App\Models\SupportRequest;
use Illuminate\Support\Facades\Auth;

class SupportRequestRepository extends BaseRepository
{
    public function __construct(SupportRequest $model)
    {
        $this->model = $model;
    }

    public function getSupportRequests($userId, SupportTicketFilterDTO $dto)
    {
        return $this->model
            ->select('id', 'sr_no', 'user_id', 'subject', 'request_type', 'status', 'priority', 'created_at', 'updated_at')
            ->where('user_id', $userId)
            ->with(['latestMessage:id,support_request_id,user_id,message,updated_at'])
            ->when(! empty($dto->requestType), function ($query) use ($dto) {
                return $query->whereIn('request_type', $dto->requestType);
            })
            ->when(! empty($dto->status), function ($query) use ($dto) {
                return $query->whereIn('status', $dto->status);
            })
            ->when(! empty($dto->priority), function ($query) use ($dto) {
                return $query->whereIn('priority', $dto->priority);
            })
            ->withCount('messages')
            ->orderBy('updated_at', 'desc');
    }

    public function createSupportRequestWithInitialMessage(SupportTicketStoreDTO $dto)
    {
        $supportRequest = $this->model->create([
            'subject' => $dto->subject,
            'request_type' => $dto->request_type,
            'priority' => $dto->priority,
            'status' => SupportRequestStatus::PENDING->value,
            'user_id' => Auth::id(),
        ]);

        $supportRequestMessage = $supportRequest->latestMessage()->create([
            'user_id' => Auth::id(),
            'message' => $dto->message,
        ]);

        if ($dto->files) {
            $this->attachFiles($supportRequestMessage, $dto->files);
        }

        return $supportRequest;
    }

    public function createOrderSupportRequest($requestData)
    {
        $supportRequest = $this->model->create([
            'user_id' => $requestData['user_id'],
            'order_id' => $requestData['order_id'],
            'subject' => $requestData['subject'],
            'request_type' => SupportRequestType::ORDER_RELATED,
            'priority' => SupportRequestPriority::HIGH,
            'status' => SupportRequestStatus::PENDING,
        ]);

        $messageData = [
            'user_id' => $requestData['user_id'],
            'message' => $requestData['message'],
        ];

        $supportRequest->messages()->create($messageData);

        return $supportRequest;
    }

    public function getSupportDetail($id)
    {
        return $this->model->with(['messages' => function ($query) {
            $query->orderBy('created_at', 'asc');
        }])->findOrFail($id);
    }

    public function addReply($id, $message)
    {
        return $this->model->findOrFail($id)->messages()->create([
            'user_id' => Auth::id(),
            'message' => $message,
        ]);
    }

    public function attachFiles($supportRequestMessage, $files)
    {
        foreach ($files as $file) {
            $supportRequestMessage
                ->addMedia($file)
                ->toMediaCollection(MediaLibrary::SUPPORT_REQUEST_DOCUMENTS->value, MediaLibrary::ADMIN_DISK->value);
        }
    }

    public function closeTicket($id)
    {
        $this->model->findOrFail($id)->update([
            'status' => SupportRequestStatus::CLOSED,
        ]);
    }

    public function getSupportRequestCounts($userId, $filterRange)
    {
        return $this->model->where('user_id', $userId)
            ->when(! empty($filterRange), function ($query) use ($filterRange) {
                $query->whereBetween('created_at', $filterRange);
            })
            ->count();
    }
}
