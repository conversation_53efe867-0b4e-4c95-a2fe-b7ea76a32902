<?php

namespace App\Repositories;

use App\DTO\SearchFilterDTO;
use App\DTO\StadiumFilterDTO;
use App\Models\Stadium;
use App\Traits\TableNameTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Class StadiumRepository
 *
 * Repository class for interacting with the `Stadium` model.
 */
class StadiumRepository extends BaseRepository
{
    use TableNameTrait;

    /**
     * StadiumRepository constructor.
     *
     * @param  Stadium  $model  The underlying model for the repository.
     */
    public function __construct(Stadium $model)
    {
        $this->model = $model;
    }

    public function getStadiumOptionsList(): Collection
    {
        return $this->model->select('id')->with(['translation:stadium_id,name'])
            ->where('is_published', 1)
            ->get()
            ->pluck('translation.name', 'id');
    }

    /**
     * Get Stadiums Listing
     *
     * @return Builder Return stadiums list with Pagination
     */
    public function getStadiumsList(StadiumFilterDTO $filterDTO): Builder
    {
        return $this->model->with(['media'])
            ->select(self::getStadiumTable().'.id', 'address_line_1', 'address_line_2', 'postcode', 'st.name as stadium_name')
            ->leftJoin(self::getStadiumTransTable().' as st', function ($join) {
                $join->on(self::getStadiumTable().'.id', '=', 'st.stadium_id')
                    ->where('st.locale', app()->getLocale());
            })
            ->where('is_published', 1)
            ->when(! empty($filterDTO->search), function ($query) use ($filterDTO) {
                $query->where(function ($query) use ($filterDTO) {
                    $query->where('st.name', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('st.description', 'LIKE', "%{$filterDTO->search}%")
                        ->orWhere('address_line_1', 'LIKE', "%{$filterDTO->search}%");
                });
            })
            ->when(! empty($filterDTO->countries), function ($query) use ($filterDTO) {
                $query->whereIn(self::getStadiumTable().'.country_id', $filterDTO->countries);
            })
            ->when(! empty($filterDTO->sort), function ($query) use ($filterDTO) {
                $sort = $filterDTO->sort;
                $pos = strrpos($sort, '_');
                $column = substr($sort, 0, $pos);
                $direction = substr($sort, $pos + 1);
                $query->orderBy($column, $direction);
            }, function ($query) {
                $query->orderBy(self::getStadiumTable().'.id', 'desc');
            });
    }

    /**
     * Search Stadiums
     *
     * @return Builder Return stadiums with search filter
     */
    public function searchStadiums(SearchFilterDTO $searchDTO): Builder
    {
        return $this->model->select(self::getStadiumTable().'.id', 'st.name')
            ->leftJoin(self::getStadiumTransTable().' as st', function ($join) {
                $join->on(self::getStadiumTable().'.id', '=', 'st.stadium_id')
                    ->where('st.locale', app()->getLocale());
            })
            ->where('is_published', 1)
            ->where('st.name', 'LIKE', "%{$searchDTO->search}%")
            ->addSelect(DB::raw("'stadium' as type"))
            ->orderBy(self::getStadiumTable().'.id', 'desc');
    }

    /**
     * Get Stadium Detail
     *
     * @param  string  $slug
     * @return Model Return stadium detail object
     */
    public function getStadiumDetail($slug): ?Model
    {
        $relations = [
            'translation:id,stadium_id,locale,name,description,meta_title,meta_description,meta_keywords',
            'country:id,shortcode',
            'country.translation:country_id,locale,name',
            'media',
        ];

        return $this->model->select('id', 'address_line_1', 'address_line_2', 'postcode', 'country_id')
            ->with($relations)
            ->whereSlug($slug)
            ->where('is_published', 1)
            ->first();
    }

    public function fetchTixStockStadiumDetail($venue)
    {
        return $this->model
            ->where(function ($query) use ($venue) {
                $query->whereHas('translations', function ($q) use ($venue) {
                    $q->where('locale', 'en')
                        ->where('name', 'LIKE', "%{$venue->name}%");
                })->orWhere('tixstock_id', $venue->id);
            })
            ->first();
    }
}
