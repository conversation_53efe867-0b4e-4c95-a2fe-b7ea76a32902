<?php

namespace App\Repositories;

use App\Models\Country;
use Illuminate\Support\Collection;

/**
 * Class CountryRepository
 *
 * Repository class for interacting with the `Country` model.
 */
class CountryRepository extends BaseRepository
{
    /**
     * CountryRepository constructor.
     *
     * @param  Country  $model  The underlying model for the repository.
     */
    public function __construct(Country $model)
    {
        $this->model = $model;
    }

    public function getCountriesOptionsList(): Collection
    {
        return $this->model->select('id')->with(['translation:country_id,name'])
            ->where('is_published', 1)
            ->get()
            ->pluck('translation.name', 'id');
    }

    public function getCountriesShortCodeList(): Collection
    {
        return $this->model->select('id', 'shortcode')->with(['translation:country_id,name'])
            ->where('is_published', 1)
            ->get()
            ->pluck('translation.name', 'shortcode');
    }

    public function getCountryByCode($countryCode)
    {
        return $this->model->where('shortcode', $countryCode)->first();
    }
}
