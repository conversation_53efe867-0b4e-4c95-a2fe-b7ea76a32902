<?php

namespace App\Repositories;

use App\Models\CmsPage;
use Illuminate\Database\Eloquent\Model;

/**
 * Class CmsPageRepository
 *
 * Repository class for interacting with the `CmsPage` model.
 */
class CmsPageRepository extends BaseRepository
{
    /**
     * CmsPageRepository constructor.
     *
     * @param  CmsPage  $model  The underlying model for the repository.
     */
    public function __construct(CmsPage $model)
    {
        $this->model = $model;
    }

    /**
     * Get CmsPage Detail
     *
     * @param  string  $slug
     * @return Model Return cms page detail object
     */
    public function getCmsPageDetail($slug): ?Model
    {
        return $this->model->whereSlug($slug)
            ->with(['translation'])
            ->first();
    }
}
