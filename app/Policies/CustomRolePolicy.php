<?php

namespace App\Policies;

use App\Enums\UserType;
use App\Models\User;
use Spatie\Permission\Models\Role;

// Import the original RolePolicy

class CustomRolePolicy extends RolePolicy
{
    /**
     * Prevent deletion of the Super Admin role.
     */
    public function delete(User $user, Role $role): bool
    {
        return $role->name !== UserType::SUPERADMIN->getLabel(); // Super Admin role cannot be deleted
    }

    /**
     * Prevent bulk deletion if Super Admin exists.
     */
    public function deleteAny(User $user): bool
    {
        return Role::where('name', UserType::SUPERADMIN->getLabel())->doesntExist();
    }
}
