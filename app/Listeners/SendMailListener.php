<?php

/**
 * Listeners
 *
 * @category App\Listeners
 */

namespace App\Listeners;

use App\Events\SendMailEvent;
use Illuminate\Support\Facades\Mail;

/**
 * Class SendMailListener
 *
 * @category App\Listeners
 */
class SendMailListener
{
    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(SendMailEvent $event)
    {
        $data['html'] = $event->mailData['emailContent'];
        Mail::send('emails.default', $data, function ($message) use ($event) {
            $message->from($event->mailData['fromEmail'], $event->mailData['fromName']);
            $message->to($event->mailData['toEmail'], $event->mailData['toName']);
            $message->subject($event->mailData['emailSubject']);

            if (! empty($event->mailData['attachments'])) {
                foreach ($event->mailData['attachments'] as $attachment) {
                    $message->attach($attachment);
                }
            }
        });
    }
}
