<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum CurrencyType: string
{
    use InteractsWithEnums;

    case AED = 'AED'; // United Arab Emirates Dirham
    case AFN = 'AFN'; // Afghan Afghani
    case ALL = 'ALL'; // Albanian Lek
    case AMD = 'AMD'; // Armenian Dram
    case ANG = 'ANG'; // Netherlands Antillean Guilder
    case AOA = 'AOA'; // Angolan Kwanza
    case ARS = 'ARS'; // Argentine Peso
    case AUD = 'AUD'; // Australian Dollar
    case AWG = 'AWG'; // Aruban Florin
    case AZN = 'AZN'; // Azerbaijani Manat
    case BAM = 'BAM'; // Bosnia-Herzegovina Convertible Mark
    case BBD = 'BBD'; // Barbadian Dollar
    case BDT = 'BDT'; // Bangladeshi Taka
    case BGN = 'BGN'; // Bulgarian Lev
    case BHD = 'BHD'; // Bahraini Dinar
    case BIF = 'BIF'; // Burundian Franc
    case BMD = 'BMD'; // Bermudan Dollar
    case BND = 'BND'; // Brunei Dollar
    case BOB = 'BOB'; // Bolivian Boliviano
    case BRL = 'BRL'; // Brazilian Real
    case BSD = 'BSD'; // Bahamian Dollar
    case BTN = 'BTN'; // Bhutanese Ngultrum
    case BWP = 'BWP'; // Botswanan Pula
    case BYN = 'BYN'; // Belarusian Ruble
    case BZD = 'BZD'; // Belize Dollar
    case CAD = 'CAD'; // Canadian Dollar
    case CDF = 'CDF'; // Congolese Franc
    case CHF = 'CHF'; // Swiss Franc
    case CLP = 'CLP'; // Chilean Peso
    case CNY = 'CNY'; // Chinese Yuan
    case COP = 'COP'; // Colombian Peso
    case CRC = 'CRC'; // Costa Rican Colón
    case CUP = 'CUP'; // Cuban Peso
    case CVE = 'CVE'; // Cape Verdean Escudo
    case CZK = 'CZK'; // Czech Republic Koruna
    case DJF = 'DJF'; // Djiboutian Franc
    case DKK = 'DKK'; // Danish Krone
    case DOP = 'DOP'; // Dominican Peso
    case DZD = 'DZD'; // Algerian Dinar
    case EGP = 'EGP'; // Egyptian Pound
    case ERN = 'ERN'; // Eritrean Nakfa
    case ETB = 'ETB'; // Ethiopian Birr
    case EUR = 'EUR'; // Euro
    case FJD = 'FJD'; // Fijian Dollar
    case FKP = 'FKP'; // Falkland Islands Pound
    case GBP = 'GBP'; // British Pound Sterling
    case GEL = 'GEL'; // Georgian Lari
    case GHS = 'GHS'; // Ghanaian Cedi
    case GIP = 'GIP'; // Gibraltar Pound
    case GMD = 'GMD'; // Gambian Dalasi
    case GNF = 'GNF'; // Guinean Franc
    case GTQ = 'GTQ'; // Guatemalan Quetzal
    case GYD = 'GYD'; // Guyanaese Dollar
    case HKD = 'HKD'; // Hong Kong Dollar
    case HNL = 'HNL'; // Honduran Lempira
    case HRK = 'HRK'; // Croatian Kuna
    case HTG = 'HTG'; // Haitian Gourde
    case HUF = 'HUF'; // Hungarian Forint
    case IDR = 'IDR'; // Indonesian Rupiah
    case ILS = 'ILS'; // Israeli New Shekel
    case INR = 'INR'; // Indian Rupee
    case IQD = 'IQD'; // Iraqi Dinar
    case IRR = 'IRR'; // Iranian Rial
    case ISK = 'ISK'; // Icelandic Króna
    case JMD = 'JMD'; // Jamaican Dollar
    case JOD = 'JOD'; // Jordanian Dinar
    case JPY = 'JPY'; // Japanese Yen
    case KES = 'KES'; // Kenyan Shilling
    case KGS = 'KGS'; // Kyrgystani Som
    case KHR = 'KHR'; // Cambodian Riel
    case KMF = 'KMF'; // Comorian Franc
    case KPW = 'KPW'; // North Korean Won
    case KRW = 'KRW'; // South Korean Won
    case KWD = 'KWD'; // Kuwaiti Dinar
    case KYD = 'KYD'; // Cayman Islands Dollar
    case KZT = 'KZT'; // Kazakhstani Tenge
    case LAK = 'LAK'; // Laotian Kip
    case LBP = 'LBP'; // Lebanese Pound
    case LKR = 'LKR'; // Sri Lankan Rupee
    case LRD = 'LRD'; // Liberian Dollar
    case LSL = 'LSL'; // Lesotho Loti
    case LYD = 'LYD'; // Libyan Dinar
    case MAD = 'MAD'; // Moroccan Dirham
    case MDL = 'MDL'; // Moldovan Leu
    case MGA = 'MGA'; // Malagasy Ariary
    case MKD = 'MKD'; // Macedonian Denar
    case MMK = 'MMK'; // Myanma Kyat
    case MNT = 'MNT'; // Mongolian Tugrik
    case MOP = 'MOP'; // Macanese Pataca
    case MRU = 'MRU'; // Mauritanian Ouguiya
    case MUR = 'MUR'; // Mauritian Rupee
    case MVR = 'MVR'; // Maldivian Rufiyaa
    case MWK = 'MWK'; // Malawian Kwacha
    case MXN = 'MXN'; // Mexican Peso
    case MYR = 'MYR'; // Malaysian Ringgit
    case MZN = 'MZN'; // Mozambican Metical
    case NAD = 'NAD'; // Namibian Dollar
    case NGN = 'NGN'; // Nigerian Naira
    case NIO = 'NIO'; // Nicaraguan Córdoba
    case NOK = 'NOK'; // Norwegian Krone
    case NPR = 'NPR'; // Nepalese Rupee
    case NZD = 'NZD'; // New Zealand Dollar
    case OMR = 'OMR'; // Omani Rial
    case PAB = 'PAB'; // Panamanian Balboa
    case PEN = 'PEN'; // Peruvian Nuevo Sol
    case PGK = 'PGK'; // Papua New Guinean Kina
    case PHP = 'PHP'; // Philippine Peso
    case PKR = 'PKR'; // Pakistani Rupee
    case PLN = 'PLN'; // Polish Złoty
    case PYG = 'PYG'; // Paraguayan Guarani
    case QAR = 'QAR'; // Qatari Rial
    case RON = 'RON'; // Romanian Leu
    case RSD = 'RSD'; // Serbian Dinar
    case RUB = 'RUB'; // Russian Ruble
    case RWF = 'RWF'; // Rwandan Franc
    case SAR = 'SAR'; // Saudi Riyal
    case SBD = 'SBD'; // Solomon Islands Dollar
    case SCR = 'SCR'; // Seychellois Rupee
    case SDG = 'SDG'; // Sudanese Pound
    case SEK = 'SEK'; // Swedish Krona
    case SGD = 'SGD'; // Singapore Dollar
    case SHP = 'SHP'; // Saint Helena Pound
    case SLL = 'SLL'; // Sierra Leonean Leone
    case SOS = 'SOS'; // Somali Shilling
    case SRD = 'SRD'; // Surinamese Dollar
    case SSP = 'SSP'; // South Sudanese Pound
    case STN = 'STN'; // São Tomé and Príncipe Dobra
    case SVC = 'SVC'; // Salvadoran Colón
    case SYP = 'SYP'; // Syrian Pound
    case SZL = 'SZL'; // Swazi Lilangeni
    case THB = 'THB'; // Thai Baht
    case TJS = 'TJS'; // Tajikistani Somoni
    case TMT = 'TMT'; // Turkmenistani Manat
    case TND = 'TND'; // Tunisian Dinar
    case TOP = 'TOP'; // Tongan Paʻanga
    case TRY = 'TRY'; // Turkish Lira
    case TTD = 'TTD'; // Trinidad and Tobago Dollar
    case TWD = 'TWD'; // New Taiwan Dollar
    case TZS = 'TZS'; // Tanzanian Shilling
    case UAH = 'UAH'; // Ukrainian Hryvnia
    case UGX = 'UGX'; // Ugandan Shilling
    case USD = 'USD'; // United States Dollar
    case UYU = 'UYU'; // Uruguayan Peso
    case UZS = 'UZS'; // Uzbekistan Som
    case VES = 'VES'; // Venezuelan Bolívar
    case VND = 'VND'; // Vietnamese Dong
    case VUV = 'VUV'; // Vanuatu Vatu
    case WST = 'WST'; // Samoan Tala
    case XAF = 'XAF'; // CFA Franc BEAC
    case XCD = 'XCD'; // East Caribbean Dollar
    case XDR = 'XDR'; // Special Drawing Rights
    case XOF = 'XOF'; // CFA Franc BCEAO
    case XPF = 'XPF'; // CFP Franc
    case YER = 'YER'; // Yemeni Rial
    case ZAR = 'ZAR'; // South African Rand
    case ZMW = 'ZMW'; // Zambian Kwacha
    case ZWL = 'ZWL'; // Zimbabwean Dollar

    public function getSymbol(): string
    {
        return match ($this) {
            self::USD, self::CAD, self::AUD, self::NZD, self::MXN, self::SGD => '$',
            self::EUR => '€',
            self::GBP => '£',
            self::JPY, self::CNY => '¥',
            self::INR => '₹',
            self::RUB => '₽',
            self::KRW => '₩',
            self::TRY => '₺',
            self::NGN => '₦',
            self::THB => '฿',
            self::VND => '₫',
            self::PHP => '₱',
            self::UAH => '₴',
            self::SAR => '﷼',
            self::ILS => '₪',
            self::GEL => '₾',
            self::AFN => '؋',
            self::KHR => '៛',
            self::LAK => '₭',
            self::MNT => '₮',
            self::BYN => 'Br',
            self::CHF => 'Fr',
            default => $this->value,
        };
    }

    public function getName(): string
    {
        $currencyInfo = match ($this) {
            self::AED => ['United Arab Emirates Dirham', 'United Arab Emirates'],
            self::AFN => ['Afghan Afghani', 'Afghanistan'],
            self::ALL => ['Albanian Lek', 'Albania'],
            self::AMD => ['Armenian Dram', 'Armenia'],
            self::ANG => ['Netherlands Antillean Guilder', 'Caribbean Netherlands'],
            self::AOA => ['Angolan Kwanza', 'Angola'],
            self::ARS => ['Argentine Peso', 'Argentina'],
            self::AUD => ['Australian Dollar', 'Australia'],
            self::AWG => ['Aruban Florin', 'Aruba'],
            self::AZN => ['Azerbaijani Manat', 'Azerbaijan'],
            self::BAM => ['Bosnia-Herzegovina Convertible Mark', 'Bosnia and Herzegovina'],
            self::BBD => ['Barbadian Dollar', 'Barbados'],
            self::BDT => ['Bangladeshi Taka', 'Bangladesh'],
            self::BGN => ['Bulgarian Lev', 'Bulgaria'],
            self::BHD => ['Bahraini Dinar', 'Bahrain'],
            self::BIF => ['Burundian Franc', 'Burundi'],
            self::BMD => ['Bermudan Dollar', 'Bermuda'],
            self::BND => ['Brunei Dollar', 'Brunei'],
            self::BOB => ['Bolivian Boliviano', 'Bolivia'],
            self::BRL => ['Brazilian Real', 'Brazil'],
            self::BSD => ['Bahamian Dollar', 'Bahamas'],
            self::BTN => ['Bhutanese Ngultrum', 'Bhutan'],
            self::BWP => ['Botswanan Pula', 'Botswana'],
            self::BYN => ['Belarusian Ruble', 'Belarus'],
            self::BZD => ['Belize Dollar', 'Belize'],
            self::CAD => ['Canadian Dollar', 'Canada'],
            self::CDF => ['Congolese Franc', 'Democratic Republic of the Congo'],
            self::CHF => ['Swiss Franc', 'Switzerland'],
            self::CLP => ['Chilean Peso', 'Chile'],
            self::CNY => ['Chinese Yuan', 'China'],
            self::COP => ['Colombian Peso', 'Colombia'],
            self::CRC => ['Costa Rican Colón', 'Costa Rica'],
            self::CUP => ['Cuban Peso', 'Cuba'],
            self::CVE => ['Cape Verdean Escudo', 'Cape Verde'],
            self::CZK => ['Czech Republic Koruna', 'Czech Republic'],
            self::DJF => ['Djiboutian Franc', 'Djibouti'],
            self::DKK => ['Danish Krone', 'Denmark'],
            self::DOP => ['Dominican Peso', 'Dominican Republic'],
            self::DZD => ['Algerian Dinar', 'Algeria'],
            self::EGP => ['Egyptian Pound', 'Egypt'],
            self::ERN => ['Eritrean Nakfa', 'Eritrea'],
            self::ETB => ['Ethiopian Birr', 'Ethiopia'],
            self::EUR => ['Euro', 'European Union'],
            self::FJD => ['Fijian Dollar', 'Fiji'],
            self::FKP => ['Falkland Islands Pound', 'Falkland Islands'],
            self::GBP => ['British Pound Sterling', 'United Kingdom'],
            self::GEL => ['Georgian Lari', 'Georgia'],
            self::GHS => ['Ghanaian Cedi', 'Ghana'],
            self::GIP => ['Gibraltar Pound', 'Gibraltar'],
            self::GMD => ['Gambian Dalasi', 'Gambia'],
            self::GNF => ['Guinean Franc', 'Guinea'],
            self::GTQ => ['Guatemalan Quetzal', 'Guatemala'],
            self::GYD => ['Guyanaese Dollar', 'Guyana'],
            self::HKD => ['Hong Kong Dollar', 'Hong Kong'],
            self::HNL => ['Honduran Lempira', 'Honduras'],
            self::HRK => ['Croatian Kuna', 'Croatia'],
            self::HTG => ['Haitian Gourde', 'Haiti'],
            self::HUF => ['Hungarian Forint', 'Hungary'],
            self::IDR => ['Indonesian Rupiah', 'Indonesia'],
            self::ILS => ['Israeli New Shekel', 'Israel'],
            self::INR => ['Indian Rupee', 'India'],
            self::IQD => ['Iraqi Dinar', 'Iraq'],
            self::IRR => ['Iranian Rial', 'Iran'],
            self::ISK => ['Icelandic Króna', 'Iceland'],
            self::JMD => ['Jamaican Dollar', 'Jamaica'],
            self::JOD => ['Jordanian Dinar', 'Jordan'],
            self::JPY => ['Japanese Yen', 'Japan'],
            self::KES => ['Kenyan Shilling', 'Kenya'],
            self::KGS => ['Kyrgystani Som', 'Kyrgyzstan'],
            self::KHR => ['Cambodian Riel', 'Cambodia'],
            self::KMF => ['Comorian Franc', 'Comoros'],
            self::KPW => ['North Korean Won', 'North Korea'],
            self::KRW => ['South Korean Won', 'South Korea'],
            self::KWD => ['Kuwaiti Dinar', 'Kuwait'],
            self::KYD => ['Cayman Islands Dollar', 'Cayman Islands'],
            self::KZT => ['Kazakhstani Tenge', 'Kazakhstan'],
            self::LAK => ['Laotian Kip', 'Laos'],
            self::LBP => ['Lebanese Pound', 'Lebanon'],
            self::LKR => ['Sri Lankan Rupee', 'Sri Lanka'],
            self::LRD => ['Liberian Dollar', 'Liberia'],
            self::LSL => ['Lesotho Loti', 'Lesotho'],
            self::LYD => ['Libyan Dinar', 'Libya'],
            self::MAD => ['Moroccan Dirham', 'Morocco'],
            self::MDL => ['Moldovan Leu', 'Moldova'],
            self::MGA => ['Malagasy Ariary', 'Madagascar'],
            self::MKD => ['Macedonian Denar', 'North Macedonia'],
            self::MMK => ['Myanma Kyat', 'Myanmar'],
            self::MNT => ['Mongolian Tugrik', 'Mongolia'],
            self::MOP => ['Macanese Pataca', 'Macau'],
            self::MRU => ['Mauritanian Ouguiya', 'Mauritania'],
            self::MUR => ['Mauritian Rupee', 'Mauritius'],
            self::MVR => ['Maldivian Rufiyaa', 'Maldives'],
            self::MWK => ['Malawian Kwacha', 'Malawi'],
            self::MXN => ['Mexican Peso', 'Mexico'],
            self::MYR => ['Malaysian Ringgit', 'Malaysia'],
            self::MZN => ['Mozambican Metical', 'Mozambique'],
            self::NAD => ['Namibian Dollar', 'Namibia'],
            self::NGN => ['Nigerian Naira', 'Nigeria'],
            self::NIO => ['Nicaraguan C\u00f3rdoba', 'Nicaragua'],
            self::NOK => ['Norwegian Krone', 'Norway'],
            self::NPR => ['Nepalese Rupee', 'Nepal'],
            self::NZD => ['New Zealand Dollar', 'New Zealand'],
            self::OMR => ['Omani Rial', 'Oman'],
            self::PAB => ['Panamanian Balboa', 'Panama'],
            self::PEN => ['Peruvian Sol', 'Peru'],
            self::PGK => ['Papua New Guinean Kina', 'Papua New Guinea'],
            self::PHP => ['Philippine Peso', 'Philippines'],
            self::PKR => ['Pakistani Rupee', 'Pakistan'],
            self::PLN => ['Polish Zloty', 'Poland'],
            self::PYG => ['Paraguayan Guarani', 'Paraguay'],
            self::QAR => ['Qatari Rial', 'Qatar'],
            self::RON => ['Romanian Leu', 'Romania'],
            self::RSD => ['Serbian Dinar', 'Serbia'],
            self::RUB => ['Russian Ruble', 'Russia'],
            self::RWF => ['Rwandan Franc', 'Rwanda'],
            self::SAR => ['Saudi Riyal', 'Saudi Arabia'],
            self::SBD => ['Solomon Islands Dollar', 'Solomon Islands'],
            self::SCR => ['Seychellois Rupee', 'Seychelles'],
            self::SDG => ['Sudanese Pound', 'Sudan'],
            self::SEK => ['Swedish Krona', 'Sweden'],
            self::SGD => ['Singapore Dollar', 'Singapore'],
            self::SHP => ['Saint Helena Pound', 'Saint Helena'],
            self::SLL => ['Sierra Leonean Leone', 'Sierra Leone'],
            self::SOS => ['Somali Shilling', 'Somalia'],
            self::SRD => ['Surinamese Dollar', 'Suriname'],
            self::SSP => ['South Sudanese Pound', 'South Sudan'],
            self::STN => ['S\u00e3o Tom\u00e9 and Pr\u00edncipe Dobra', 'S\u00e3o Tom\u00e9 and Pr\u00edncipe'],
            self::SVC => ['Salvadoran Col\u00f3n', 'El Salvador'],
            self::SYP => ['Syrian Pound', 'Syria'],
            self::SZL => ['Eswatini Lilangeni', 'Eswatini'],
            self::THB => ['Thai Baht', 'Thailand'],
            self::TJS => ['Tajikistani Somoni', 'Tajikistan'],
            self::TMT => ['Turkmenistani Manat', 'Turkmenistan'],
            self::TND => ['Tunisian Dinar', 'Tunisia'],
            self::TOP => ['Tongan Pa\u02bbanga', 'Tonga'],
            self::TRY => ['Turkish Lira', 'Turkey'],
            self::TTD => ['Trinidad and Tobago Dollar', 'Trinidad and Tobago'],
            self::TWD => ['New Taiwan Dollar', 'Taiwan'],
            self::TZS => ['Tanzanian Shilling', 'Tanzania'],
            self::UAH => ['Ukrainian Hryvnia', 'Ukraine'],
            self::UGX => ['Ugandan Shilling', 'Uganda'],
            self::USD => ['United States Dollar', 'United States'],
            self::UYU => ['Uruguayan Peso', 'Uruguay'],
            self::UZS => ['Uzbekistani Som', 'Uzbekistan'],
            self::VES => ['Venezuelan Bol\u00edvar', 'Venezuela'],
            self::VND => ['Vietnamese Dong', 'Vietnam'],
            self::VUV => ['Vanuatu Vatu', 'Vanuatu'],
            self::WST => ['Samoan T\u0101l\u0101', 'Samoa'],
            self::XAF => ['Central African CFA Franc', 'Central Africa'],
            self::XCD => ['East Caribbean Dollar', 'Eastern Caribbean'],
            self::XDR => ['Special Drawing Rights', 'IMF'],
            self::XOF => ['West African CFA Franc', 'West Africa'],
            self::XPF => ['CFP Franc', 'Pacific Territories'],
            self::YER => ['Yemeni Rial', 'Yemen'],
            self::ZAR => ['South African Rand', 'South Africa'],
            self::ZMW => ['Zambian Kwacha', 'Zambia'],
            self::ZWL => ['Zimbabwean Dollar', 'Zimbabwe'],
        };

        return "{$currencyInfo[0]} - {$this->getSymbol()} ({$currencyInfo[1]})";
    }

    public function getLabel()
    {
        return $this->getName();
    }
}
