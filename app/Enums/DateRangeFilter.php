<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum DateRangeFilter: string
{
    use InteractsWithEnums;

    case ALL_TIME = 'all_time';
    case TODAY = 'today';
    case LAST_7_DAYS = 'last_7_days';
    case LAST_30_DAYS = 'last_30_days';
    case LAST_90_DAYS = 'last_90_days';
    case LAST_365_DAYS = 'last_365_days';

    public function getLabel(): string
    {
        return __("enums.date_range_filters.{$this->value}");
    }

    public function dateRange(): array
    {
        return match ($this) {
            self::ALL_TIME => [],
            self::TODAY => [now()->startOfDay(), now()->endOfDay()],
            self::LAST_7_DAYS => [now()->subDays(6)->startOfDay(), now()->endOfDay()],
            self::LAST_30_DAYS => [now()->subDays(29)->startOfDay(), now()->endOfDay()],
            self::LAST_90_DAYS => [now()->subDays(89)->startOfDay(), now()->endOfDay()],
            self::LAST_365_DAYS => [now()->subDays(364)->startOfDay(), now()->endOfDay()],
        };
    }
}
