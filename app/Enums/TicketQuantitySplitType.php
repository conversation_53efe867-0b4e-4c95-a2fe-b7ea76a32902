<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum TicketQuantitySplitType: string
{
    use InteractsWithEnums;

    case ANY = 'any';
    case SINGLE = 'single';
    case AVOID_ONE = 'avoid_one';
    case AVOID_ONE_THREE = 'avoid_one_three';
    case AVOID_ODD = 'avoid_odd';

    public function getLabel(): string
    {
        return __("enums.ticket_quantity_split_types.{$this->value}");
    }
}
