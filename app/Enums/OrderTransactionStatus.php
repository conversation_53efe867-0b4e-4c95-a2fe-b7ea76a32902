<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum OrderTransactionStatus: string
{
    use InteractsWithEnums;

    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case REFUNDED = 'refunded';
    case CANCELED = 'canceled';
    case EXPIRED = 'expired';

    public function getLabel(): string
    {
        return __("enums.transaction_statuses.{$this->value}");
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::PROCESSING => 'info',
            self::COMPLETED => 'success',
            self::FAILED => 'danger',
            self::REFUNDED => 'info',
            self::CANCELED => 'danger',
            self::EXPIRED => 'danger'
        };
    }
}
