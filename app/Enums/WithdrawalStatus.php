<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum WithdrawalStatus: string
{
    use InteractsWithEnums;

    case PENDING = 'pending';
    case APPROVED = 'approved';
    case PAID = 'paid';
    case REJECTED = 'rejected';

    public function getLabel(): string
    {
        return __("enums.withdrawal_statuses.{$this->value}");
    }

    public function getBadgeColor()
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::APPROVED => 'info',
            self::PAID => 'success',
            self::REJECTED => 'danger',
        };
    }
}
