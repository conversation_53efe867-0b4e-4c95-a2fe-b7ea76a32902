<?php

namespace App\Enums;

enum TixStockQuantitySplitType: string
{
    case NO_PREFERENCES = 'No Preferences';
    case SINGLE_SEATS = 'Single Seats';
    case AVOID_LEAVING_ONE_TICKET = 'Avoid Leaving One Ticket';
    case ALL_TOGETHER = 'All Together';
    case SELL_IN_MULTIPLES = 'Sell In Multiples';

    public function toSplitType()
    {
        return match ($this) {
            self::NO_PREFERENCES => TicketQuantitySplitType::ANY,
            self::SINGLE_SEATS => TicketQuantitySplitType::SINGLE,
            self::AVOID_LEAVING_ONE_TICKET => TicketQuantitySplitType::AVOID_ONE,
            self::ALL_TOGETHER => TicketQuantitySplitType::ALL_TOGETHER,
            self::SELL_IN_MULTIPLES => TicketQuantitySplitType::IN_MULTIPLE,
        };
    }
}
