<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum WalletTransactionType: string
{
    use InteractsWithEnums;

    case ORDER_EARNING = 'order_earning';
    case ORDER_PENALTY = 'order_penalty';
    case WITHDRAWAL = 'withdrawal';
    case ADJUSTMENT = 'adjustment';

    public function getLabel(): string
    {
        return __("enums.wallet_transaction_types.{$this->value}");
    }

    public function getBadgeColor()
    {
        return match ($this) {
            self::ORDER_EARNING => 'success',
            self::ORDER_PENALTY => 'warning',
            self::WITHDRAWAL => 'danger',
            self::ADJUSTMENT => 'info',
        };
    }
}
