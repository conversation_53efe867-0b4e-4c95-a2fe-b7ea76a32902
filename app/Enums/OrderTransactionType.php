<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum OrderTransactionType: string
{
    use InteractsWithEnums;

    case PURCHASE = 'purchase';
    case REFUND = 'refund';

    public function getLabel(): string
    {
        return __("enums.transaction_types.{$this->value}");
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::PURCHASE => 'info',
            self::REFUND => 'warning',
        };
    }
}
