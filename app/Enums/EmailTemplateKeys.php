<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum EmailTemplateKeys: string
{
    use InteractsWithEnums;

    case CUSTOMER_REGISTRATION = 'CUSTOMER_REGISTRATION';
    case CUSTOMER_EMAIL_VERIFICATION = 'CUSTOMER_EMAIL_VERIFICATION';
    case CUSTOMER_RESET_PASSWORD_LINK = 'CUSTOMER_RESET_PASSWORD_LINK';
    case SELLER_NEW_ORDER = 'SELLER_NEW_ORDER';
    case CUSTOMER_ORDER_PROCESSING = 'CUSTOMER_ORDER_PROCESSING';
    case CUSTOMER_ORDER_CONFIRMATION = 'CUSTOMER_ORDER_CONFIRMATION';
    case CUSTOMER_ORDER_CANCELLATION = 'CUSTOMER_ORDER_CANCELLATION';
    case CUSTOMER_ORDER_UNDER_REVIEW = 'CUSTOMER_ORDER_UNDER_REVIEW';
    case CUSTOMER_ORDER_SHIPPED = 'CUSTOMER_ORDER_SHIPPED';
    case CUSTOMER_ORDER_REASSIGN = 'CUSTOMER_ORDER_REASSIGN';
    case OLD_SELLER_ORDER_REASSIGN = 'OLD_SELLER_ORDER_REASSIGN';
    case NEW_SELLER_ORDER_REASSIGN = 'NEW_SELLER_ORDER_REASSIGN';
    case BUYER_ORDER_OPEN_DISPUTE_EMAIL = 'BUYER_ORDER_OPEN_DISPUTE_EMAIL';
    case ADMIN_ORDER_STATUS_UPDATE_EMAIL = 'ADMIN_ORDER_STATUS_UPDATE_EMAIL';
    case SELLER_ORDER_STATUS_UPDATE_EMAIL = 'SELLER_ORDER_STATUS_UPDATE_EMAIL';
    case SUPPORT_REQUEST_USER_EMAIL = 'SUPPORT_REQUEST_USER_EMAIL';
    case SUPPORT_REQUEST_FOR_ORDER_EMAIL = 'SUPPORT_REQUEST_FOR_ORDER_EMAIL';
    case SUPPORT_REQUEST_ADMIN_EMAIL = 'SUPPORT_REQUEST_ADMIN_EMAIL';
    case SUPPORT_REQUEST_REPLY_TO_USER = 'SUPPORT_REQUEST_REPLY_TO_USER';
    case SUPPORT_REQUEST_REPLY_TO_ADMIN = 'SUPPORT_REQUEST_REPLY_TO_ADMIN';
    case SUPPORT_REQUEST_CLOSED_EMAIL = 'SUPPORT_REQUEST_CLOSED_EMAIL';
    case WITHDRAWAL_REQUEST_USER_EMAIL = 'WITHDRAWAL_REQUEST_USER_EMAIL';
    case WITHDRAWAL_REQUEST_ADMIN_EMAIL = 'WITHDRAWAL_REQUEST_ADMIN_EMAIL';
    case WITHDRAWAL_REQUEST_STATUS_CHANGE_EMAIL = 'WITHDRAWAL_REQUEST_STATUS_CHANGE_EMAIL';
    case ADMIN_TIXSTOCK_EVENT_SYNCED_EMAIL = 'ADMIN_TIXSTOCK_EVENT_SYNCED_EMAIL';
}
