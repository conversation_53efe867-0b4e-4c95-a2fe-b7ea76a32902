<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum WalletTransactionStatus: string
{
    use InteractsWithEnums;

    case PENDING = 'pending';
    case APPROVED = 'approved';
    case PARTIAL_WITHDRAWN = 'partial_withdrawn';
    case WITHDRAWN = 'withdrawn';

    public function getLabel(): string
    {
        return __("enums.wallet_transaction_statuses.{$this->value}");
    }

    public function getBadgeColor()
    {
        return match ($this) {
            self::PENDING => 'danger',
            self::APPROVED => 'warning',
            self::PARTIAL_WITHDRAWN => 'info',
            self::WITHDRAWN => 'success',
        };
    }
}
