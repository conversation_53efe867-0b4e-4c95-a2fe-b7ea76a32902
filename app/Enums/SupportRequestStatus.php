<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum SupportRequestStatus: string
{
    use InteractsWithEnums;
    case PENDING = 'pending';
    case INPROGRESS = 'inprogress';
    case RESOLVED = 'resolved';
    case CLOSED = 'closed';

    public function getLabel(): string
    {
        return __("enums.support_request_statuses.{$this->value}");
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::PENDING => 'danger',
            self::INPROGRESS => 'info',
            self::RESOLVED => 'warning',
            self::CLOSED => 'success'
        };
    }
}
