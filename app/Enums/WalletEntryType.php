<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum WalletEntryType: string
{
    use InteractsWithEnums;

    case CREDIT = 'credit';
    case DEBIT = 'debit';

    public function getLabel(): string
    {
        return __("enums.wallet_entry_types.{$this->value}");
    }

    public function getBadgeColor()
    {
        return match ($this) {
            self::CREDIT => 'success',
            self::DEBIT => 'danger',
        };
    }
}
