<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum SupportRequestPriority: string
{
    use InteractsWithEnums;

    case LOW = 'low';
    case MEDIUM = 'medium';
    case HIGH = 'high';

    public function getLabel(): string
    {
        return __("enums.support_request_priorities.{$this->value}");
    }

    public function getBadgeColor(): string
    {
        return match ($this) {
            self::LOW => 'info',
            self::MEDIUM => 'warning',
            self::HIGH => 'danger',
        };
    }
}
