<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum RestrictionType: string
{
    use InteractsWithEnums;

    case EVENT = 'event';
    case TICKET = 'ticket';

    public function getLabel(): string
    {
        return __("enums.restriction_types.{$this->value}");
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::EVENT => 'warning',
            self::TICKET => 'success',
        };
    }
}
