<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum TicketType: string
{
    use InteractsWithEnums;

    case E_TICKET = 'e-ticket';
    case MOBILE_TICKET = 'mobile-ticket';

    public function getLabel(): string
    {
        return __("enums.ticket_types.{$this->value}");
    }

    public static function getDescriptions(): array
    {
        return [
            self::E_TICKET->value => 'Requires a Copy in Paper',
            self::MOBILE_TICKET->value => 'Requires a Copy in Mobile',
        ];
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::E_TICKET => 'warning',
            self::MOBILE_TICKET => 'success',
        };
    }
}
