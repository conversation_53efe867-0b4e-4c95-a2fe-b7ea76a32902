<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum LanguageCode: string
{
    use InteractsWithEnums;

    case ENGLISH = 'en';
    case ITALIAN = 'it';
    case SPANISH = 'es';

    public function getLabel(): string
    {
        return __("enums.languages.{$this->value}");
    }

    public function getDirection()
    {
        return match ($this) {
            self::ENGLISH => 'LTR',
            self::ITALIAN => 'LTR',
            self::SPANISH => 'LTR',
        };
    }
}
