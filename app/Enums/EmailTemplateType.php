<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum EmailTemplateType: string
{
    use InteractsWithEnums;

    case EMAIL = 'email';
    case PUSH = 'push';
    case SMS = 'sms';

    public function getLabel(): string
    {
        return match ($this) {
            self::EMAIL => 'EMAIL',
            self::PUSH => 'PUSH',
            self::SMS => 'SMS',
        };
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::EMAIL => 'success',
            self::PUSH => 'info',
            self::SMS => 'primary',
        };
    }
}
