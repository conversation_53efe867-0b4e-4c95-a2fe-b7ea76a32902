<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum SupportRequestType: string
{
    use InteractsWithEnums;

    case GENERAL = 'general';
    case TICKET_RELATED = 'ticket_related';
    case ORDER_RELATED = 'order_related';
    case OTHER = 'other';

    public function getLabel(): string
    {
        return __("enums.support_request_types.{$this->value}");
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::GENERAL => 'danger',
            self::TICKET_RELATED => 'info',
            self::ORDER_RELATED => 'warning',
            self::OTHER => 'success'
        };
    }
}
