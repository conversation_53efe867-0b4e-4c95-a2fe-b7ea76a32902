<?php

namespace App\Enums;

enum TixStockOrderStatus: string
{
    case APPROVED = 'Approved';
    case COMMISSIONABLE = 'Commissionable';
    case REJECTED = 'Rejected';
    case CANCELLED = 'Cancelled';
    case REFUNDED = 'Refunded';
    case PENDING_REVIEW = 'Pending Review';
    case SALE_ON_HOLD = 'Sale on Hold';

    public function toOrderStatus(): OrderStatus
    {
        return match ($this) {
            self::PENDING_REVIEW => OrderStatus::PROCESSING,
            self::APPROVED => OrderStatus::CONFIRMED,
            self::SALE_ON_HOLD => OrderStatus::UNDER_REVIEW,
            self::REJECTED => OrderStatus::CANCELED,
            self::CANCELLED => OrderStatus::CANCELED,
            self::REFUNDED => OrderStatus::CANCELED,
            self::SHIPPED => OrderStatus::SHIPPED,
            self::COMMISSIONABLE => OrderStatus::SHIPPED,
        };
    }

    public static function fromOrderStatus(OrderStatus $status): ?self
    {
        return match ($status) {
            OrderStatus::PROCESSING => self::PENDING_REVIEW,
            OrderStatus::CONFIRMED => self::APPROVED,
            OrderStatus::SHIPPED => self::SHIPPED,
            OrderStatus::CANCELED => self::CANCELLED,
            OrderStatus::UNDER_REVIEW => self::SALE_ON_HOLD,
            OrderStatus::ON_DISPUTE => self::SALE_ON_HOLD,
        };
    }
}
