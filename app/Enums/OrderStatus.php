<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum OrderStatus: string
{
    use InteractsWithEnums;

    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case CONFIRMED = 'confirmed';
    case UNDER_REVIEW = 'under_review';
    case SHIPPED = 'shipped';
    case COMPLETED = 'completed';
    case ON_DISPUTE = 'on_dispute';
    case CANCELED = 'canceled';
    case EXPIRED = 'expired';

    public function getLabel(): string
    {
        return __("enums.order_statuses.{$this->value}");
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::PROCESSING => 'warning',
            self::CONFIRMED => 'info',
            self::UNDER_REVIEW => 'info',
            self::SHIPPED => 'success',
            self::COMPLETED => 'success',
            self::ON_DISPUTE => 'danger',
            self::CANCELED => 'danger',
            self::EXPIRED => 'gray'
        };
    }
}
