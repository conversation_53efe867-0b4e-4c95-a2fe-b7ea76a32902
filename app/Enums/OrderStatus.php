<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum OrderStatus: string
{
    use InteractsWithEnums;

    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case CONFIRMED = 'confirmed';
    case UNDER_REVIEW = 'under_review';
    case SHIPPED = 'shipped';
    case COMPLETED = 'completed';
    case ON_DISPUTE = 'on_dispute';
    case CANCELED = 'canceled';
    case EXPIRED = 'expired';

    public function getLabel(): string
    {
        return __("enums.order_statuses.{$this->value}");
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::PROCESSING => 'warning',
            self::CONFIRMED => 'info',
            self::UNDER_REVIEW => 'info',
            self::SHIPPED => 'success',
            self::COMPLETED => 'success',
            self::ON_DISPUTE => 'danger',
            self::CANCELED => 'danger',
            self::EXPIRED => 'gray'
        };
    }

    public static function getAdminStatusActionList(?string $current): array
    {
        return match ($current) {
            self::PENDING->value => self::getOptionsWithKeyValuePair([
                self::PROCESSING->value,
                self::CONFIRMED->value,
            ]),
            self::PROCESSING->value => self::getOptionsWithKeyValuePair([
                self::PROCESSING->value,
                self::CONFIRMED->value,
                self::CANCELED->value,
                self::UNDER_REVIEW->value,
            ]),
            self::CONFIRMED->value => self::getOptionsWithKeyValuePair([
                self::CONFIRMED->value,
                self::UNDER_REVIEW->value,
                self::CANCELED->value,
                self::SHIPPED->value,
            ]),
            self::UNDER_REVIEW->value => self::getOptionsWithKeyValuePair([
                self::UNDER_REVIEW->value,
                self::CONFIRMED->value,
                self::CANCELED->value,
            ]),
            self::SHIPPED->value => self::getOptionsWithKeyValuePair([
                self::SHIPPED->value,
                self::ON_DISPUTE->value,
            ]),
            self::ON_DISPUTE->value => self::getOptionsWithKeyValuePair([
                self::ON_DISPUTE->value,
                self::SHIPPED->value,
                self::CANCELED->value,
            ]),
            self::COMPLETED->value => self::getOptionsWithKeyValuePair([
                self::COMPLETED->value,
            ]),
            self::CANCELED->value => self::getOptionsWithKeyValuePair([
                self::CANCELED->value,
            ]),
            default => self::getOptionsWithKeyValuePair([
                self::PENDING->value,
            ])
        };
    }
}
