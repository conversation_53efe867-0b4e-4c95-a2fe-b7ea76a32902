<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum UserType: string
{
    use InteractsWithEnums;

    case SUPERADMIN = 'super_admin';
    case ADMIN = 'admin';
    case BROKER = 'broker';
    case CUSTOMER = 'customer';

    public function getLabel(): string
    {
        return match ($this) {
            self::SUPERADMIN => 'Super Admin',
            self::ADMIN => 'Admin',
            self::BROKER => 'Broker',
            self::CUSTOMER => 'Customer',
        };
    }

    public function getBadgeColour()
    {
        return match ($this) {
            self::SUPERADMIN => 'success',
            self::ADMIN => 'warning',
            self::BROKER => 'primary',
            self::CUSTOMER => 'danger',
        };
    }
}
