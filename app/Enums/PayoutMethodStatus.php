<?php

namespace App\Enums;

use App\Traits\InteractsWithEnums;

enum PayoutMethodStatus: string
{
    use InteractsWithEnums;

    case PENDING = 'pending';
    case ACTIVE = 'active';
    case DISABLED = 'disabled';

    public function getLabel(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::ACTIVE => 'Active',
            self::DISABLED => 'Disabled',
        };
    }
}
