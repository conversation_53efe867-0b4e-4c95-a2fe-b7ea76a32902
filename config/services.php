<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'app' => [
        'url' => env('APP_URL'),
        'domain' => env('APP_DOMAIN'),
        'admin_domain' => env('ADMIN_DOMAIN'),
    ],

    'ticketgol' => [
        'events_per_page' => env('TICKETGOL_EVENTS_PER_PAGE', 12),
        'items_per_page' => env('TICKETGOL_ITEMS_PER_PAGE', 9),
        'tickets_per_page' => env('TICKETGOL_TICKETS_PER_PAGE', 10),
        'searches_per_page' => env('TICKETGOL_SEARCHES_PER_PAGE', 10),
        'temp_reservation_minutes' => (int) env('TICKETGOL_TEMP_RESERVATION_MINUTES', 15),
        'service_charge_rate' => (float) env('TICKETGOL_SERVICE_CHARGE_RATE', 0.20),
        'tax_rate' => (float) env('TICKETGOL_TAX_RATE', 0.18),
        'stripe_cent_unit' => (int) env('STRIPE_CENT_UNIT', 100),
        'buffer_minutes_for_checkout_expiration' => (int) env('TICKETGOL_BUFFER_MINUTES_FOR_CHECKOUT_EXPIRATION', 5),
        'buffer_minutes_for_webhook_processing' => (int) env('TICKETGOL_BUFFER_MINUTES_FOR_WEBHOOK_PROCESSING', 10),
        'max_ticket_creation_limit' => (int) env('TICKETGOL_MAX_TICKET_CREATION_LIMIT', 50),
        'max_quantity_per_ticket' => (int) env('TICKETGOL_MAX_QUANTITY_PER_TICKET', 30),
        'max_price_limit' => (int) env('TICKETGOL_MAX_PRICE_LIMIT', 10000000),
        'broker_payout_method_max_limit' => (int) env('BROKER_PAYOUT_METHOD_MAX_LIMIT', 5),
        'payout_method_max_limit' => (int) env('PAYOUT_METHOD_MAX_LIMIT', 2),
        'min_withdrawal_limit' => (int) env('MIN_WITHDRAWAL_LIMIT', 100),
        'max_withdrawal_limit' => (int) env('MAX_WITHDRAWAL_LIMIT', 10000),
    ],

    'stripe' => [
        'secret' => env('STRIPE_SECRET'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
    ],

    'tixstock' => [
        'base_url' => env('TIXSTOCK_BASE_URL', 'https://sandbox-pf.tixstock.com/v1/'),
        'bearer_token' => env('TIXSTOCK_BEARER_TOKEN'),
        'event_category' => 'Football',
        'events_per_page' => (int) env('TIXSTOCK_EVENTS_PER_PAGE', 20),
        'tickets_per_page' => (int) env('TIXSTOCK_EVENTS_PER_PAGE', 20),
        'user_email' => '<EMAIL>',
        'currency_api_url' => env('TIXSTOCK_CURRENCY_URL', 'https://api.frankfurter.app/'),
    ],
];
