<?php

use Illuminate\Support\Facades\Schedule;

Schedule::command('ticketgol:process-processing-reservations')
    ->everyThirtySeconds();

Schedule::command('ticketgol:process-active-reservations')
    ->everyThirtySeconds();

Schedule::command('ticketgol:process-completed-event-orders')
    ->daily('00:15');

Schedule::command('ticketgol:sync-tixstock-events')
    ->hourly();

// Commenting this cron as not needed now. We will use in future if needed
// Schedule::command('ticketgol:sync-tixstock-tickets')
//     ->everyFiveMinutes();

// Schedule::command('ticketgol:update-tixstock-ticket-status')
//     ->everyFifteenMinutes();

// Laravel Backup Commands
Schedule::command('backup:clean')
    ->weekly()
    ->sundays()
    ->at('01:00');

Schedule::command('backup:run')
    ->daily()
    ->at('01:30');
