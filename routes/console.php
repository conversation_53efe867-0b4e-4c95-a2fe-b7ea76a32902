<?php

use Illuminate\Support\Facades\Schedule;

Schedule::command('ticketgol:process-processing-reservations')
    ->everyMinute();

Schedule::command('ticketgol:process-active-reservations')
    ->everyMinute();

Schedule::command('ticketgol:process-completed-event-orders')
    ->daily('00:15');

Schedule::command('ticketgol:sync-tixstock-events')
    ->hourly();

// Laravel Backup Commands
Schedule::command('backup:clean')
    ->weekly()
    ->sundays()
    ->at('01:00');

Schedule::command('backup:run')
    ->daily()
    ->at('01:30');
