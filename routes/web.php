<?php

use App\Http\Controllers\ClubController;
use App\Http\Controllers\DetailPageController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LeagueController;
use App\Http\Controllers\MyAccountController;
use App\Http\Controllers\MySalesController;
use App\Http\Controllers\MyTicketsController;
use App\Http\Controllers\MyWithdrawalController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\StadiumController;
use App\Http\Controllers\SupportRequestController;
use App\Http\Controllers\TicketController;
use App\Http\Middleware\UnderConstructionMiddleware;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::domain(config('services.app.domain'))->group(function () {
    Route::get('/under-construction', function () {
        return Inertia::render('UnderConstruction');
    })->name('frontend.maintenance');

    Route::middleware([UnderConstructionMiddleware::class])->group(function () {

        Route::middleware(['detect-ssr'])->group(function () {
            Route::get('/', [HomeController::class, 'index'])->name('home');
            Route::get('/search', [SearchController::class, 'index'])->name('search');

            Route::get('/events', [EventController::class, 'index'])->name('events');

            Route::get('/selltickets', [EventController::class, 'sellTickets'])->name('selltickets');

            Route::get('/clubs', [ClubController::class, 'index'])->name('clubs');

            Route::get('/leagues', [LeagueController::class, 'index'])->name('leagues');

            Route::get('/stadiums', [StadiumController::class, 'index'])->name('stadiums');
        });

        Route::middleware(['auth'])->group(function () {
            Route::group(['prefix' => 'my-account'], function () {
                Route::get('/', [MyAccountController::class, 'index'])->name('dashboard');
                Route::get('/settings', [MyAccountController::class, 'settings'])->name('my-account.settings');

                Route::group(['prefix' => 'profile'], function () {
                    Route::get('/', [ProfileController::class, 'edit'])->name('profile.edit');
                    Route::patch('/', [ProfileController::class, 'update'])->name('profile.update');
                    Route::delete('/', [ProfileController::class, 'destroy'])->name('profile.destroy');
                });

                Route::middleware(['verified'])->group(function () {
                    Route::get('/orders', [MyAccountController::class, 'orders'])->name('my-account.orders');
                    Route::get('/orders/{id}', [MyAccountController::class, 'orderDetail'])->name('my-account.order-detail');
                    Route::get('/support', [MyAccountController::class, 'support'])->name('my-account.support');
                    Route::get('/support/{id}', [SupportRequestController::class, 'show'])->name('my-account.support.show');

                    Route::group(['prefix' => 'tickets'], function () {
                        Route::get('/', [MyTicketsController::class, 'index'])->name('my-account.tickets');
                        Route::get('/edit/{ticketNo}', [MyTicketsController::class, 'edit'])->name('my-account.tickets.edit');
                    });

                    Route::group(['prefix' => 'sales'], function () {
                        Route::get('/', [MySalesController::class, 'index'])->name('my-account.sales');
                        Route::get('/{orderNo}', [MySalesController::class, 'show'])->name('my-account.sales.detail');
                    });

                    Route::group(['prefix' => 'withdrawals'], function () {
                        Route::get('/', [MyWithdrawalController::class, 'index'])->name('my-account.withdrawals');
                        Route::get('/transaction/{transactionNo}', [MyWithdrawalController::class, 'walletTransaction'])->name('my-account.withdrawals.wallet-transaction');
                    });
                });
            });

            Route::middleware(['verified'])->group(function () {
                Route::get('/ticket/sell/{slug}', [TicketController::class, 'sell'])->name('ticket.sell')
                    ->middleware('verify-slug');

                Route::get('/ticket/checkout/{reservationId}', [TicketController::class, 'checkout'])->name('ticket.checkout');
                Route::get('/checkout/success', [PaymentController::class, 'success'])->name('checkout.success');
                Route::get('/checkout/cancel', [PaymentController::class, 'cancel'])->name('checkout.cancel');
            });
        });

        require __DIR__ . '/auth.php';

        Route::middleware(['verify-slug', 'detect-ssr'])->group(function () {
            Route::get('/{slug}', [DetailPageController::class, 'show'])
            ->where('slug', '.*')
            ->name('detail.show')
            ->middleware('verify-slug');
        });
    });
});
