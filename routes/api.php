<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\CheckoutController;
use App\Http\Controllers\Api\V1\ClubController;
use App\Http\Controllers\Api\V1\DashboardController;
use App\Http\Controllers\Api\V1\EventController;
use App\Http\Controllers\Api\V1\HomeController;
use App\Http\Controllers\Api\V1\LeagueController;
use App\Http\Controllers\Api\V1\OrderController;
use App\Http\Controllers\Api\V1\SearchController;
use App\Http\Controllers\Api\V1\StadiumController;
use App\Http\Controllers\Api\V1\StripeWebHookController;
use App\Http\Controllers\Api\V1\SupportRequestController;
use App\Http\Controllers\Api\V1\TicketController;
use App\Http\Controllers\Api\V1\TicketReservationController;
use App\Http\Controllers\Api\V1\TixStockWebhookController;
use App\Http\Controllers\Api\V1\TranslationsController;
use App\Http\Controllers\Api\V1\UserPayoutMethodController;
use App\Http\Controllers\Api\V1\UserWalletTransactionController;
use App\Http\Controllers\Api\V1\UserWithdrawalController;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Support\Facades\Route;

Route::middleware(['set-locale'])->prefix('v1')->group(function () {

    Route::prefix('home')->group(function () {
        Route::get('/', [HomeController::class, 'index'])->name('api.home.index');
    });

    Route::prefix('search')->group(function () {
        Route::get('/', [SearchController::class, 'index'])->name('api.search.index');
        Route::get('/suggestions', [SearchController::class, 'suggestions'])->name('api.search.suggestions');
    });

    Route::prefix('translations')->group(function () {
        Route::get('/', [TranslationsController::class, 'index'])->name('api.translations.index');
    });

    Route::prefix('events')->group(function () {
        Route::post('/', [EventController::class, 'index'])->name('api.events.index');
        Route::post('/selltickets', [EventController::class, 'sellTickets'])->name('api.events.selltickets');
        Route::get('/filters', [EventController::class, 'getFilters'])->name('api.events.filters');
        Route::get('/{slug}', [EventController::class, 'show'])->name('api.events.show');
    });

    Route::prefix('stadiums')->group(function () {
        Route::post('/', [StadiumController::class, 'index'])->name('api.stadiums.index');
        Route::get('/filters', [StadiumController::class, 'getFilters'])->name('api.stadiums.filters');
        Route::get('/{slug}', [StadiumController::class, 'show'])->name('api.stadiums.show');
    });

    Route::prefix('clubs')->group(function () {
        Route::post('/', [ClubController::class, 'index'])->name('api.clubs.index');
        Route::get('/filters', [ClubController::class, 'getFilters'])->name('api.clubs.filters');
        Route::get('/{slug}', [ClubController::class, 'show'])->name('api.clubs.show');
    });

    Route::prefix('leagues')->group(function () {
        Route::post('/', [LeagueController::class, 'index'])->name('api.leagues.index');
        Route::get('/filters', [LeagueController::class, 'getFilters'])->name('api.leagues.filters');
        Route::get('/{slug}', [LeagueController::class, 'show'])->name('api.leagues.show');
    });

    Route::prefix('tickets')->group(function () {
        Route::post('/', [TicketController::class, 'index'])->name('api.tickets.index');
    });

    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user', [AuthController::class, 'user'])->name('api.user');
        Route::post('/auth/logout', [AuthController::class, 'logout'])->name('api.logout');

        Route::prefix('dashboard')->group(function () {
            Route::post('/my-stats', [DashboardController::class, 'myStats'])->name('api.dashboard.my-stats');
        });

        Route::prefix('tickets')->group(function () {
            Route::post('/my-listing', [TicketController::class, 'myTickets'])->name('api.tickets.my-tickets');
            Route::post('/add', [TicketController::class, 'store'])->name('api.tickets.store');
            Route::get('/configurations', [TicketController::class, 'configurations'])->name('api.tickets.configurations');
            Route::get('/{ticketNo}', [TicketController::class, 'show'])->name('api.tickets.show');
            Route::post('/update', [TicketController::class, 'update'])->name('api.tickets.update');
            Route::delete('/delete/{ticketNo}', [TicketController::class, 'delete'])->name('api.tickets.delete');
        });

        Route::prefix('reservation')->group(function () {
            Route::post('/lock', [TicketReservationController::class, 'createPreliminaryReservation'])->name('api.reservations.create');
            Route::get('/detail/{reservationId}', [TicketReservationController::class, 'getPreliminaryReservation'])->name('api.reservations.detail');
            Route::get('/check-active', [TicketReservationController::class, 'checkActiveReservation'])->name('api.reservations.check-active');
            Route::delete('/', [TicketReservationController::class, 'cancelReservation'])->name('api.reservations.cancel');
        });

        Route::prefix('checkout')->group(function () {
            Route::post('/session', [CheckoutController::class, 'createSession'])->name('api.checkout.create');
        });

        Route::prefix('orders')->group(function () {
            Route::post('/', [OrderController::class, 'index'])->name('api.orders.index');
            Route::post('/create', [OrderController::class, 'store'])->name('api.orders.store');
            Route::post('/check-status', [OrderController::class, 'checkOrderStatus'])->name('api.orders.check-status');
            Route::get('/statuses', [OrderController::class, 'getStatuses'])->name('api.orders.statuses');
            Route::get('/invoice/{orderId}', [OrderController::class, 'downloadInvoice'])->name('api.orders.download-invoice');
            Route::get('/{id}', [OrderController::class, 'show'])->name('api.orders.show');

            Route::post('/my-sales', [OrderController::class, 'mySales'])->name('api.orders.my-sales');
            Route::get('/my-sales/{orderNo}', [OrderController::class, 'salesOrderShow'])->name('api.orders.my-sales.show');
            Route::post('/update-status', [OrderController::class, 'updateOrderStatus'])->name('api.orders.update-status');

            Route::post('/open-dispute', [OrderController::class, 'openDispute'])->name('api.orders.open-dispute');
            Route::post('/upload-tickets', [OrderController::class, 'uploadOrderTickets'])->name('api.orders.upload-tickets');
            Route::post('/mark-tickets-downloaded', [OrderController::class, 'markTicketsDownloaded'])->name('api.orders.mark-tickets-downloaded');
        });

        Route::prefix('payout-methods')->group(function () {
            Route::post('/', [UserPayoutMethodController::class, 'index'])->name('api.payout-methods.index');
            Route::post('/create', [UserPayoutMethodController::class, 'store'])->name('api.payout-methods.store');
            Route::get('/configurations', [UserPayoutMethodController::class, 'getConfigurations'])->name('api.payout-methods.configurations');
            Route::delete('/delete/{payoutMethodId}', [UserPayoutMethodController::class, 'delete'])->name('api.payout-methods.delete');
            Route::post('/mark-default', [UserPayoutMethodController::class, 'markDefaultPayoutMethod'])->name('api.payout-methods.mark-default');
        });

        Route::prefix('wallet-transactions')->group(function () {
            Route::post('/', [UserWalletTransactionController::class, 'index'])->name('api.wallet-transactions.index');
            Route::get('/{transactionNo}', [UserWalletTransactionController::class, 'show'])->name('api.wallet-transactions.show');
        });

        Route::prefix('withdrawals')->group(function () {
            Route::post('/', [UserWithdrawalController::class, 'index'])->name('api.withdrawals.index');
            Route::get('/configurations', [UserWithdrawalController::class, 'getConfigurations'])->name('api.withdrawals.configurations');
            Route::get('/check-can-withdraw', [UserWithdrawalController::class, 'checkUserCanWithdraw'])->name('api.withdrawals.check-can-withdraw');
            Route::post('/create', [UserWithdrawalController::class, 'store'])->name('api.withdrawals.store');
            Route::delete('/delete/{withdrawalId}', [UserWithdrawalController::class, 'delete'])->name('api.withdrawals.delete');

        });

        Route::prefix('support-requests')->group(function () {
            Route::get('/', [SupportRequestController::class, 'index'])->name('api.support-requests.index');
            Route::post('/', [SupportRequestController::class, 'store'])->name('api.support-requests.store');
            // Important: Route with fixed path must come BEFORE routes with parameters
            Route::get('/enums', [SupportRequestController::class, 'getEnums'])->name('api.support-requests.enums');

            Route::get('/{id}', [SupportRequestController::class, 'show'])->name('api.support-requests.show');
            Route::post('/{id}/reply', [SupportRequestController::class, 'reply'])->name('api.support-requests.reply');
        });
    });
});

Route::post('/auth/login', [AuthController::class, 'login'])->name('api.login');

Route::post('/checkout/webhook', [StripeWebHookController::class, 'handleCheckoutWebhook'])
    ->withoutMiddleware([VerifyCsrfToken::class])
    ->name('checkout.webhook');

Route::post('/tixstock/webhook', [TixStockWebhookController::class, 'handleWebhook'])
    ->withoutMiddleware([VerifyCsrfToken::class])
    ->name('tixstock.webhook');
