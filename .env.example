APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://ticketgol.test
ADMIN_URL=http://admin.ticketgol.test

APP_DOMAIN=ticketgol.test
ADMIN_DOMAIN=admin.ticketgol.test

APP_UNDER_CONSTRUCTION=false

QUERY_DETECTOR_ENABLED=true
QUERY_DETECTOR_LOG_CHANNEL=stack

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"

MAIL_MAILER=resend
RESEND_API_KEY=
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

VITE_STRIPE_PUBLIC_KEY=
STRIPE_SECRET=
STRIPE_WEBHOOK_SECRET=


TICKETGOL_EVENTS_PER_PAGE=12
TICKETGOL_ITEMS_PER_PAGE=9
TICKETGOL_TICKETS_PER_PAGE=10
TICKETGOL_TEMP_RESERVATION_MINUTES=15
TICKETGOL_SERVICE_CHARGE_RATE=0.20
TICKETGOL_TAX_RATE=0.18
TICKETGOL_BUFFER_MINUTES_FOR_CHECKOUT_EXPIRATION=5
TICKETGOL_MAX_TICKET_CREATION_LIMIT=50
TICKETGOL_MAX_QUANTITY_PER_TICKET=30
TICKETGOL_MAX_PRICE_LIMIT=10000000

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=1.0

VITE_SENTRY_REACT_DSN=
SENTRY_AUTH_TOKEN=
SENTRY_ENVIRONMENT=local

TIXSTOCK_BEARER_TOKEN=
TIXSTOCK_BASE_URL=
TIXSTOCK_EVENTS_PER_PAGE=20
