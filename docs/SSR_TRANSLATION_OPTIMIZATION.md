# SSR Translation Optimization

## Overview

This optimization reduces the API load by only loading translations for Server-Side Rendering (SSR) requests on non-authenticated pages. This prevents unnecessary translation loading on every Inertia.js request.

## How It Works

### 1. SSR Detection Middleware (`DetectSSRMiddleware`)

The middleware detects SSR requests by checking for:
- **Node.js User Agent**: Primary indicator for Inertia SSR requests
- **X-Inertia-SSR Header**: Explicit SSR header if sent by Inertia
- **X-Inertia without X-Requested-With**: SSR requests typically don't have XMLHttpRequest header
- **Localhost requests with Node.js patterns**: SSR server requests
- **Other SSR patterns**: InertiaJS, <PERSON>, headless, puppeteer, playwright in user agent

When detected, it adds an `X-SSR` header to the request.

### 2. Conditional Translation Loading (`HandleInertiaRequests`)

The Inertia middleware now only loads translations when the `X-SSR` header is present:

```php
// Only load translations for SSR requests (when X-SSR header is present)
if ($request->hasHeader('X-SSR')) {
    $translationService = app(TranslationService::class);
    $sharedData['translations'] = $translationService->getTranslationsWithLocale();
}
```

### 3. Middleware Configuration

The `DetectSSRMiddleware` is applied globally to all web routes but intelligently detects when to apply SSR optimization:

**Applied to:**
- Non-authenticated users on public pages
- Home page (`/`)
- Search page (`/search`)
- Events page (`/events`)
- Sell tickets page (`/selltickets`)
- Clubs page (`/clubs`)
- Leagues page (`/leagues`)
- Stadiums page (`/stadiums`)
- Auth routes (login, register, password reset)
- Detail pages (`/{slug}`)

**NOT applied to:**
- Authenticated users (any route)
- API routes (`/api/*`)
- Admin routes (`/admin.*`)
- Debug/utility routes (`/_debugbar/*`, `/telescope/*`)

## Benefits

1. **Reduced API Load**: Translations are only loaded for initial SSR requests, not for every Inertia navigation
2. **Better Performance**: Client-side navigation is faster without translation loading
3. **Maintained SEO**: SSR still gets translations for proper content rendering
4. **Selective Application**: Only applies to public pages where SEO matters

## Client-Side Fallback

The frontend uses `useSSRTranslations` hook which:
1. Uses server-side translations when available (SSR)
2. Falls back to client-side API calls when needed (regular navigation)

## Testing

Run the SSR detection tests:

```bash
php artisan test tests/Feature/SSRDetectionTest.php
```

## Debugging

When `APP_DEBUG=true`, the middleware logs SSR detection events to help with debugging:

```php
Log::info('SSR Request Detected', [
    'url' => $request->fullUrl(),
    'user_agent' => $request->userAgent(),
    'ip' => $request->ip(),
    'headers' => $request->headers->all()
]);
```

## Configuration

The SSR detection logic can be customized in `app/Http/Middleware/DetectSSRMiddleware.php` by modifying the `isSSRRequest()` method.

## Monitoring

Monitor your application logs to ensure SSR detection is working correctly and adjust the detection logic if needed based on your specific SSR setup.
