<?php

namespace Database\Factories;

use App\Enums\RestrictionType;
use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Restriction>
 */
class RestrictionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $restrictionTypes = RestrictionType::getValues();

        return [
            'is_active' => rand(0, 1),
            'type' => $this->faker->randomElement($restrictionTypes),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($restriction) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $translationData = [];

            foreach ($languages as $value) {
                $translationData[] = [
                    'restriction_id' => $restriction->id,
                    'locale' => $value,
                    'name' => $this->faker->name,
                ];
            }

            $restriction->translations()->createMany($translationData);
        });
    }
}
