<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Country>
 */
class CountryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $country = $this->faker->unique(true)->country();

        return [
            'name' => $country,
            'shortcode' => $this->faker->unique()->countryCode(),
            'slug' => Str::slug($country),
            'is_published' => rand(0, 1),
        ];
    }
}
