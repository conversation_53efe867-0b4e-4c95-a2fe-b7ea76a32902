<?php

namespace Database\Factories;

use App\Enums\UserType;
use App\Models\User;
use App\Models\UserDetail;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $userTypes = [UserType::BROKER->value, UserType::CUSTOMER->value];
        $name = $this->faker->name;
        $encodedName = urlencode($name);

        return [
            'name' => $name,
            'user_name' => fake()->username(),
            'email' => fake()->unique()->safeEmail(),
            'user_type' => $this->faker->randomElement($userTypes),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'avatar' => "https://ui-avatars.com/api/?name=$encodedName",
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    public function configure()
    {
        return $this->afterCreating(function (User $user) {
            UserDetail::factory()->create([
                'user_id' => $user->id,
            ]);
        });
    }
}
