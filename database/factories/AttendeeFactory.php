<?php

namespace Database\Factories;

use App\Enums\GenderType;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Attendee>
 */
class AttendeeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $genderTypes = GenderType::getValues();

        return [
            'name' => $this->faker->name,
            'email' => $this->faker->safeEmail,
            'gender' => $genderTypes[rand(0, count($genderTypes) - 1)],
            'dob' => $this->faker->date(),
            'order_id' => Order::factory(),
        ];
    }
}
