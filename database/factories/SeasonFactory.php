<?php

namespace Database\Factories;

use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Season>
 */
class SeasonFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'is_published' => rand(0, 1),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($season) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $translationData = [];
            $slugs = [];

            foreach ($languages as $value) {
                $name = $this->faker->name;
                $translationData[] = [
                    'season_id' => $season->id,
                    'locale' => $value,
                    'name' => $name,
                ];

                $slugs[$value] = Str::slug('season-'.$name.'-'.$season->id);
            }

            $season->translations()->createMany($translationData);

            $season->setSlugs($slugs);
        });
    }
}
