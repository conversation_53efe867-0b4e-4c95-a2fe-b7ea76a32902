<?php

namespace Database\Factories;

use App\Enums\EmailTemplateType;
use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailTemplate>
 */
class EmailTemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $templateTypes = EmailTemplateType::getValues();

        return [
            'template_key' => $this->faker->text(50),
            'template_purpose' => $this->faker->text(100),
            'template_type' => $this->faker->randomElement($templateTypes),
            'is_active' => rand(0, 1),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($emailTemplate) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $translationData = [];

            foreach ($languages as $value) {
                $translationData[] = [
                    'email_template_id' => $emailTemplate->id,
                    'locale' => $value,
                    'subject' => $this->faker->sentence,
                    'body' => $this->faker->paragraph,
                ];
            }

            $emailTemplate->translations()->createMany($translationData);
        });
    }
}
