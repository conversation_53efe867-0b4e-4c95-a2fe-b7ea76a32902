<?php

namespace Database\Factories;

use App\Models\Country;
use App\Models\Language;
use App\Models\StadiumSector;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Season>
 */
class StadiumFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'address_line_1' => $this->faker->address(),
            'address_line_2' => $this->faker->address(),
            'postcode' => $this->faker->postcode(),
            'country_id' => Country::whereIn('shortcode', ['IT', 'ES'])->inRandomOrder()->first(),
            'is_published' => rand(0, 1),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($stadium) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $translationData = [];
            $slugs = [];
            foreach ($languages as $value) {
                $name = $this->faker->name;
                $translationData[] = [
                    'stadium_id' => $stadium->id,
                    'locale' => $value,
                    'name' => $name,
                    'description' => $this->faker->paragraph,
                    'meta_title' => $this->faker->sentence,
                    'meta_description' => $this->faker->sentence,
                    'meta_keywords' => $this->faker->sentence,
                ];

                $slugs[$value] = Str::slug('stadium-'.$name.'-'.$stadium->id);
            }
            $stadium->translations()->createMany($translationData);

            $stadium->setSlugs($slugs);

            $parentSectors = StadiumSector::factory()
                ->count(rand(2, 4))
                ->create([
                    'stadium_id' => $stadium->id,
                    'parent_id' => null,
                ]);

            foreach ($parentSectors as $sector) {
                StadiumSector::factory()
                    ->count(rand(1, 3))
                    ->create([
                        'stadium_id' => $stadium->id,
                        'parent_id' => $sector->id,
                    ]);
            }
        });
    }
}
