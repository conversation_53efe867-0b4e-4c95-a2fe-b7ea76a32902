<?php

namespace Database\Factories;

use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CmsPage>
 */
class CmsPageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'is_active' => rand(0, 1),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($cmsPage) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $translationData = [];
            $slugs = [];

            foreach ($languages as $value) {
                $pageName = $this->faker->name;
                $translationData[] = [
                    'cms_page_id' => $cmsPage->id,
                    'locale' => $value,
                    'title' => $pageName,
                    'content' => $this->faker->paragraph,
                    'meta_title' => $this->faker->sentence,
                    'meta_description' => $this->faker->sentence,
                ];

                $slugs[$value] = Str::slug('page-'.$pageName.'-'.$cmsPage->id);
            }

            $cmsPage->translations()->createMany($translationData);

            $cmsPage->setSlugs($slugs);
        });
    }
}
