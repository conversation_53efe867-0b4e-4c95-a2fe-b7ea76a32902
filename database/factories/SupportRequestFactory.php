<?php

namespace Database\Factories;

use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestStatus;
use App\Enums\SupportRequestType;
use App\Enums\UserType;
use App\Models\SupportRequest;
use App\Models\SupportRequestMessage;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SupportRequest>
 */
class SupportRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $requestTypes = SupportRequestType::getValues();
        $status = SupportRequestStatus::getValues();
        $priorities = SupportRequestPriority::getValues();

        return [
            'user_id' => User::factory([
                'user_type' => UserType::CUSTOMER->value,
            ]),
            'subject' => $this->faker->name,
            'request_type' => $requestTypes[rand(0, count($requestTypes) - 1)],
            'status' => $status[rand(0, count($status) - 1)],
            'priority' => $priorities[rand(0, count($priorities) - 1)],
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function (SupportRequest $supportRequest) {
            $userId = $supportRequest->user_id;
            $adminId = 1;

            // Generate 4-8 messages total
            $totalMessages = rand(4, 8);

            // Start with the user who created the request
            $currentUser = $userId;

            for ($i = 0; $i < $totalMessages; $i++) {
                SupportRequestMessage::factory()->create([
                    'user_id' => $currentUser,
                    'support_request_id' => $supportRequest->id,
                ]);

                // Randomly decide if we should switch users (70% chance)
                if (rand(1, 100) <= 70) {
                    $currentUser = ($currentUser === $userId) ? $adminId : $userId;
                }
            }
        });
    }
}
