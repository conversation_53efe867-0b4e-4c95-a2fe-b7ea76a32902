<?php

namespace Database\Factories;

use App\Enums\OrderStatus;
use App\Models\Attendee;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $ticket = Ticket::factory()->create();
        $quantity = $this->faker->numberBetween(1, $ticket->quantity);
        $orderStaus = OrderStatus::getValues();

        return [
            'buyer_id' => User::factory(),
            'ticket_id' => $ticket->id,
            'quantity' => $quantity,
            'total_price' => $quantity * $ticket->price,
            'purchase_date' => $this->faker->dateTimeBetween('now', '+30 years'),
            'status' => $orderStaus[rand(0, count($orderStaus) - 1)],
            'description' => $this->faker->paragraph,
            'created_by' => User::factory(),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function (Order $order) {
            Attendee::factory()
                ->count($order->quantity)
                ->create([
                    'order_id' => $order->id,
                ]);
        });
    }
}
