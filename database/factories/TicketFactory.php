<?php

namespace Database\Factories;

use App\Enums\CurrencyType;
use App\Enums\TicketQuantitySplitType;
use App\Enums\TicketType;
use App\Models\Event;
use App\Models\Language;
use App\Models\Restriction;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ticket>
 */
class TicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $ticketTypes = TicketType::getValues();
        $splitTypes = TicketQuantitySplitType::getValues();

        $event = Event::inRandomOrder()->first();
        $sector = $event->stadiumSectors()->inRandomOrder()->first();
        $user = User::inRandomOrder()->first();

        return [
            'event_id' => $event->id,
            'seller_id' => $user->id,
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'face_value_price' => $this->faker->randomFloat(2, 10, 1000),
            'quantity' => rand(4, 15),
            'is_active' => rand(0, 1),
            'currency_code' => CurrencyType::EUR->value,
            'sector_id' => $sector->pivot->stadium_sector_id,
            'ticket_type' => $this->faker->randomElement($ticketTypes),
            'quantity_split_type' => $this->faker->randomElement($splitTypes),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($ticket) {
            $languages = Language::where('is_active', 1)->pluck('locale')->toArray();
            $translationData = [];

            foreach ($languages as $value) {
                $translationData[] = [
                    'ticket_id' => $ticket->id,
                    'locale' => $value,
                    'description' => $this->faker->paragraph(),
                ];
            }

            $ticket->translations()->createMany($translationData);

            $restrictionIds = Restriction::where('type', 'ticket')->inRandomOrder()->limit(2)->pluck('id');
            $ticket->restrictions()->sync($restrictionIds);
        });
    }
}
