<?php

namespace Database\Factories;

use App\Enums\GenderType;
use App\Models\Country;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserDetail>
 */
class UserDetailFactory extends Factory
{
    use HasFactory, SoftDeletes;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $genders = GenderType::getValues();

        return [
            'user_id' => User::factory(),
            'gender' => $genders[rand(0, 2)],
            'surname' => fake()->name(),
            'company' => fake()->name(),
            'government_id' => fake()->name(),
            'address' => fake()->address(),
            'zip' => fake()->postcode(),
            'city' => fake()->city(),
            'country_id' => Country::inRandomOrder()->first(),
            'phone' => fake()->phoneNumber(),
            'description' => fake()->realText(),
        ];
    }
}
