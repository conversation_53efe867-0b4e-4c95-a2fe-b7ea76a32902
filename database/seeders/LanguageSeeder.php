<?php

namespace Database\Seeders;

use App\Enums\LanguageCode;
use App\Models\Language;
use Illuminate\Database\Seeder;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $languages = LanguageCode::cases();

        foreach ($languages as $lang) {
            $exist = Language::where('name', $lang->getLabel())->first();
            if (! $exist) {
                $langData['name'] = $lang->getLabel();
                $langData['locale'] = $lang->value;
                $langData['direction'] = $lang->getDirection();
                Language::create($langData);
            }
        }
    }
}
