<?php

namespace Database\Seeders;

use App\Enums\UserType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'user_name' => 'super_admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('TG@dmin!23'),
                'user_type' => UserType::SUPERADMIN->value,
            ]
        );

        $adminUser->syncRoles(UserType::SUPERADMIN->getLabel());
    }
}
