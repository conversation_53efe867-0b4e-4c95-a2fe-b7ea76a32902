<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            EmailTemplate\CustomerRegistrationEmailSeeder::class,
            EmailTemplate\CustomerEmailVerificationSeeder::class,
            EmailTemplate\CustomerResetPasswordEmailSeeder::class,
            EmailTemplate\SellerNewOrderEmailSeeder::class,
            EmailTemplate\CustomerOrderProcessingEmailSeeder::class,
            EmailTemplate\CustomerOrderConfirmationEmailSeeder::class,
            EmailTemplate\CustomerOrderCancellationEmailSeeder::class,
            EmailTemplate\CustomerOrderUnderReviewEmailSeeder::class,
            EmailTemplate\CustomerOrderShippedEmailSeeder::class,
            EmailTemplate\BuyerOrderOpenDisputeEmailSeeder::class,
            EmailTemplate\SellerOrderStatusUpdateEmailSeeder::class,
            EmailTemplate\AdminOrderStatusUpdateEmailSeeder::class,
            EmailTemplate\SupportRequestUserEmailSeeder::class,
            EmailTemplate\SupportRequestAdminEmailSeeder::class,
            EmailTemplate\SupportRequestForOrderEmailSeeder::class,
            EmailTemplate\CustomerOrderReassignEmailSeeder::class,
            EmailTemplate\NewSellerOrderReassignEmailSeeder::class,
            EmailTemplate\OldSellerOrderReassignEmailSeeder::class,
            EmailTemplate\SupportRequestReplyToUserEmailSeeder::class,
            EmailTemplate\SupportRequestReplyToAdminEmailSeeder::class,
            EmailTemplate\WithdrawalRequestUserEmailSeeder::class,
            EmailTemplate\WithdrawalRequestAdminEmailSeeder::class,
            EmailTemplate\WithdrawalRequestStatusChangeEmailSeeder::class,
            EmailTemplate\AdminTixStockEventSyncedEmailSeeder::class,
        ]);
    }
}
