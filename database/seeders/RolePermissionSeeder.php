<?php

namespace Database\Seeders;

use App\Enums\UserType;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    public function run()
    {
        // Define roles
        $roles = [
            UserType::SUPERADMIN->getLabel(),
            UserType::ADMIN->getLabel(),
        ];

        // Fetch permissions from the database (created by Shield)
        $permissions = Permission::pluck('name')->toArray();

        // Create roles and assign permissions
        foreach ($roles as $roleName) {
            $role = Role::firstOrCreate(['name' => $roleName, 'guard_name' => 'web']);

            if ($roleName === UserType::SUPERADMIN->getLabel()) {
                $role->syncPermissions($permissions);
            }

            if ($roleName === UserType::ADMIN->getLabel()) {
                $role->syncPermissions(array_filter($permissions, function ($perm) {
                    return ! str_contains($perm, 'delete') && ! str_contains($perm, 'force_delete');
                }));
            }
        }
    }
}
