<?php

namespace Database\Seeders;

use App\Enums\CurrencyType;
use App\Enums\UserType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TixStockUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $txApiUser = User::updateOrCreate(
            ['email' => config('services.tixstock.user_email')],
            [
                'name' => 'TixStock',
                'user_name' => 'tixstock_api',
                'email' => config('services.tixstock.user_email'),
                'email_verified_at' => now(),
                'password' => Hash::make('TixStock@api1'),
                'user_type' => UserType::BROKER->value,
            ]
        );

        if (! $txApiUser->userDetail) {
            $txApiUser->userDetail()->create([
                'user_id' => $txApiUser->id,
                'order_auto_confirm' => 1,
            ]);
        }

        if (! $txApiUser->wallet) {
            $txApiUser->wallet()->create([
                'user_id' => $txApiUser->id,
                'balance' => 0,
                'currency_code' => CurrencyType::EUR->value,
            ]);
        }
    }
}
