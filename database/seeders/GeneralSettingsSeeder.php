<?php

namespace Database\Seeders;

use App\Enums\SettingType;
use App\Models\GeneralSetting;
use Illuminate\Database\Seeder;

class GeneralSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings = [
            [
                'setting_key' => 'contact_email',
                'setting_label' => 'Contact Email',
                'setting_type' => SettingType::EMAIL->value,
                'setting_value' => '<EMAIL>',
            ],
            [
                'setting_key' => 'admin_email',
                'setting_label' => 'Admin Email',
                'setting_type' => SettingType::EMAIL->value,
                'setting_value' => '<EMAIL>',
            ],
            [
                'setting_key' => 'contact_phone',
                'setting_label' => 'Contact Phone',
                'setting_type' => SettingType::TEXT->value,
                'setting_value' => '1234567890',
            ],
            [
                'setting_key' => 'sales_email',
                'setting_label' => 'Sales Email',
                'setting_type' => SettingType::EMAIL->value,
                'setting_value' => '<EMAIL>',
            ],
            [
                'setting_key' => 'support_email',
                'setting_label' => 'Support Email',
                'setting_type' => SettingType::EMAIL->value,
                'setting_value' => '<EMAIL>',
            ],
            [
                'setting_key' => 'contact_address',
                'setting_label' => 'Contact Address',
                'setting_type' => SettingType::TEXT->value,
                'setting_value' => '123 Stadium Street Milan, Italy 20121',
            ],
            [
                'setting_key' => 'facebook_link',
                'setting_label' => 'Facebook Link',
                'setting_type' => SettingType::TEXT->value,
                'setting_value' => 'https://www.facebook.com/',
            ],
            [
                'setting_key' => 'instagram_link',
                'setting_label' => 'Instagram Link',
                'setting_type' => SettingType::TEXT->value,
                'setting_value' => 'https://www.instagram.com/',
            ],
            [
                'setting_key' => 'x_link',
                'setting_label' => 'X Link',
                'setting_type' => SettingType::TEXT->value,
                'setting_value' => 'https://www.x.com/',
            ],
            [
                'setting_key' => 'youtube_link',
                'setting_label' => 'Youtube Link',
                'setting_type' => SettingType::TEXT->value,
                'setting_value' => 'https://www.youtube.com/',
            ],
        ];

        foreach ($settings as $setting) {
            GeneralSetting::updateOrCreate(['setting_key' => $setting['setting_key']], $setting);
        }
    }
}
