<?php

use App\Enums\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->timestamp('ticket_downloaded_at')->after('purchase_date')->nullable();
            $table->enum('status', OrderStatus::getValues())->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('ticket_downloaded_at');
            $table->enum('status', OrderStatus::getValues([OrderStatus::ON_DISPUTE->value]))->change();
        });
    }
};
