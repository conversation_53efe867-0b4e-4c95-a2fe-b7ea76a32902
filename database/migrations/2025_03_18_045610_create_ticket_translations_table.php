<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ticket_id')->constrained(table: 'tickets')->cascadeOnDelete();
            $table->string('locale', 2)->index();
            $table->text('description');
            $table->timestamps();
            $table->unique(['ticket_id', 'locale']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_translations');
    }
};
