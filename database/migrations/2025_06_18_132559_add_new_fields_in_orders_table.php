<?php

use App\Enums\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->foreignId('ticket_reservation_id')->nullable()->after('ticket_id')->constrained('ticket_reservations', 'id')->cascadeOnDelete();
            $table->decimal('price', 10, 2)->after('quantity')->nullable();
            $table->decimal('service_charge_amount', 10, 2)->after('total_price')->nullable();
            $table->decimal('tax_amount', 10, 2)->after('service_charge_amount')->nullable();
            $table->decimal('grand_total', 10, 2)->after('tax_amount')->nullable();
            $table->json('order_meta_data')->nullable()->after('grand_total');
            $table->enum('status', OrderStatus::getValues())->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['ticket_reservation_id']);
            $table->dropColumn('ticket_reservation_id');
            $table->dropColumn('price');
            $table->dropColumn('service_charge_amount');
            $table->dropColumn('tax_amount');
            $table->dropColumn('grand_total');
            $table->dropColumn('order_meta_data');
            $table->enum('status', OrderStatus::getValues([OrderStatus::SHIPPED->value, OrderStatus::COMPLETED->value, OrderStatus::UNDER_REVIEW->value]))->change();
        });
    }
};
