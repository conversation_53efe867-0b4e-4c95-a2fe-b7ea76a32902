<?php

use App\Enums\WalletEntryType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users', 'id')->cascadeOnDelete();
            $table->string('transaction_no', 100)->unique()->nullable();
            $table->enum('transaction_type', WalletTransactionType::getValues());
            $table->enum('entry_type', WalletEntryType::getValues());
            $table->decimal('total_amount', 12, 2);
            $table->decimal('withdrawn_amount', 12, 2)->default(0.00);
            $table->decimal('remained_amount', 12, 2);
            $table->string('currency_code', 3);
            $table->decimal('balance_after', 12, 2);
            $table->foreignId('order_id')->nullable()->constrained('orders', 'id')->cascadeOnDelete();
            $table->foreignId('withdrawal_id')->nullable()->constrained('user_withdrawals', 'id')->cascadeOnDelete();
            $table->text('note')->nullable();
            $table->enum('status', WalletTransactionStatus::getValues());
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_wallet_transactions');
    }
};
