<?php

use App\Enums\SupportRequestPriority;
use App\Enums\SupportRequestStatus;
use App\Enums\SupportRequestType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('support_requests', function (Blueprint $table) {
            $table->id();
            $table->string('sr_no')->unique()->nullable();
            $table->foreignId('user_id')->constrained('users', 'id')->cascadeOnDelete();
            $table->string('subject');
            $table->enum('request_type', SupportRequestType::getValues());
            $table->enum('status', SupportRequestStatus::getValues());
            $table->enum('priority', SupportRequestPriority::getValues());
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('support_requests');
    }
};
