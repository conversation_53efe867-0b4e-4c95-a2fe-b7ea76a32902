<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('event_event_restriction');
        Schema::dropIfExists('event_restrictions');

        Schema::create('event_restrictions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained(table: 'events')->cascadeOnDelete();
            $table->foreignId('restriction_id')->constrained(table: 'restrictions')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_restrictions');
    }
};
