<?php

use App\Enums\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_no')->unique()->nullable();
            $table->foreignId('seller_id')->constrained('users', 'id')->cascadeOnDelete();
            $table->foreignId('buyer_id')->constrained('users', 'id')->cascadeOnDelete()->nullable();
            $table->foreignId('ticket_id')->constrained('tickets', 'id')->cascadeOnDelete();
            $table->string('quantity');
            $table->decimal('total_price', 12, 2);
            $table->enum('status', OrderStatus::getValues());
            $table->date('purchase_date')->nullable();
            $table->text('description')->nullable();
            $table->foreignId('created_by')->constrained('users', 'id')->cascadeOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
