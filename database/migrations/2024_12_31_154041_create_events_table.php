<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->time('time');
            $table->string('timezone')->nullable();
            $table->string('address');
            $table->boolean('is_feature_event')->default(0);
            $table->string('category');
            $table->foreignId('country_id')->constrained(table: 'countries')->cascadeOnDelete();
            $table->foreignId('home_club_id')->constrained(table: 'clubs')->cascadeOnDelete();
            $table->foreignId('guest_club_id')->constrained(table: 'clubs')->cascadeOnDelete();
            $table->foreignId('stadium_id')->constrained(table: 'stadiums')->cascadeOnDelete();
            $table->foreignId('league_id')->constrained(table: 'leagues')->cascadeOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
