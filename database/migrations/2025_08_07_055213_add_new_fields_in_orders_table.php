<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('tixstock_id', 50)->nullable()->after('description');
            $table->string('tixstock_status')->nullable()->after('tixstock_id');
            $table->text('tixstock_response')->nullable()->after('tixstock_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('tixstock_id');
            $table->dropColumn('tixstock_status');
            $table->dropColumn('tixstock_response');
        });
    }
};
