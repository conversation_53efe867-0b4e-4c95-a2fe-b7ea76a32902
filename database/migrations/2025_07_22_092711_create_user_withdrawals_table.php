<?php

use App\Enums\WithdrawalStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_withdrawals', function (Blueprint $table) {
            $table->id();
            $table->string('withdraw_no', 100)->unique()->nullable();
            $table->foreignId('user_id')->constrained('users', 'id')->cascadeOnDelete();
            $table->decimal('amount', 12, 2);
            $table->decimal('previous_amount', 12, 2);
            $table->string('currency_code', 3);
            $table->foreignId('payout_method_id')->constrained('user_payout_methods', 'id')->cascadeOnDelete();
            $table->text('note')->nullable();
            $table->text('used_transactions')->nullable();
            $table->string('payment_reference_number')->nullable();
            $table->enum('status', WithdrawalStatus::getValues());
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_withdrawals');
    }
};
