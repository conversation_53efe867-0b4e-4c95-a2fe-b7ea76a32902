<?php

use App\Enums\UserType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->foreignId('seller_id')->after('buyer_id')->nullable()->constrained('users', 'id')->nullOnDelete();
            $table->string('currency_code')->after('quantity')->nullable();
            $table->decimal('penalty_amount', 10, 2)->after('grand_total')->nullable();
            $table->foreignId('penalty_user_id')->after('penalty_amount')->nullable()->constrained('users', 'id')->nullOnDelete();
            $table->enum('penalty_user_type', [UserType::BROKER->value, UserType::CUSTOMER->value])->after('penalty_user_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign('orders_seller_id_foreign');
            $table->dropColumn('seller_id');
            $table->dropColumn('currency_code');
            $table->dropColumn('penalty_amount');
            $table->dropForeign('orders_penalty_user_id_foreign');
            $table->dropColumn('penalty_user_id');
            $table->dropColumn('penalty_user_type');
        });
    }
};
