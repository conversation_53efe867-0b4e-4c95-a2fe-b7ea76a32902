<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_page_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cms_page_id')->constrained(table: 'cms_pages')->cascadeOnDelete();
            $table->string('locale', 2);
            $table->string('title');
            $table->text('content');
            $table->string('meta_title');
            $table->text('meta_description');
            $table->unique(['cms_page_id', 'locale']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms_page_translations');
    }
};
