<?php

use App\Enums\CurrencyType;
use App\Enums\TicketType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_no')->nullable()->unique();
            $table->foreignId('event_id')->constrained(table: 'events')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained(table: 'users')->cascadeOnDelete();
            $table->string('price');
            $table->string('quantity');
            $table->boolean('is_active');
            $table->enum('currency_code', CurrencyType::getValues());
            $table->enum('ticket_type', TicketType::getValues());
            $table->foreignId('sector_id')->constrained(table: 'stadium_sectors')->cascadeOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
