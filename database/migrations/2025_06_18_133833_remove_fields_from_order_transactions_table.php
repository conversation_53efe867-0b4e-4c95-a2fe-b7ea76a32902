<?php

use App\Enums\OrderTransactionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            $table->dropColumn('session_id');
            $table->dropForeign(['user_id']);
            $table->dropColumn('user_id');
            $table->dropForeign(['ticket_reservation_id']);
            $table->dropColumn('ticket_reservation_id');
            $table->enum('status', OrderTransactionStatus::getValues())->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            $table->string('session_id')->index('session_id');
            $table->foreignId('user_id')->after('order_id')->nullable()->constrained('users', 'id')->cascadeOnDelete();
            $table->foreignId('ticket_reservation_id')->after('user_id')->nullable()->constrained('ticket_reservations', 'id')->cascadeOnDelete();
            $table->enum('status', OrderTransactionStatus::getValues([OrderTransactionStatus::EXPIRED->value]))->change();
        });
    }
};
