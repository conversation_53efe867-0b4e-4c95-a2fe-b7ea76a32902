<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_stadium_sectors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained(table: 'events')->cascadeOnDelete();
            $table->foreignId('stadium_sector_id')->constrained(table: 'stadium_sectors')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_stadium_sectors');
    }
};
