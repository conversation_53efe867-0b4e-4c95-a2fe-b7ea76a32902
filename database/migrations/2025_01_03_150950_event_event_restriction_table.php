<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_event_restriction', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained(table: 'events')->cascadeOnDelete();
            $table->foreignId('event_restriction_id')->constrained(table: 'event_restrictions')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_event_restriction');
    }
};
