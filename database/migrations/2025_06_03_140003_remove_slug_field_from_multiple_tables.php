<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('seasons', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
        Schema::table('leagues', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
        Schema::table('stadiums', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
        Schema::table('clubs', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
        Schema::table('events', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
        Schema::table('cms_pages', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('seasons', function (Blueprint $table) {
            $table->string('slug')->nullable();
        });
        Schema::table('leagues', function (Blueprint $table) {
            $table->string('slug')->nullable();
        });
        Schema::table('stadiums', function (Blueprint $table) {
            $table->string('slug')->nullable();
        });
        Schema::table('clubs', function (Blueprint $table) {
            $table->string('slug')->nullable();
        });
        Schema::table('events', function (Blueprint $table) {
            $table->string('slug')->nullable();
        });
        Schema::table('cms_pages', function (Blueprint $table) {
            $table->string('slug')->nullable();
        });
    }
};
