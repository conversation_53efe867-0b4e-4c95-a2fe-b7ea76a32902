<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tixstock_api_logs', function (Blueprint $table) {
            $table->id();
            $table->enum('event_type', ['api', 'webhook']);
            $table->string('endpoint')->nullable();
            $table->string('webhook_type')->nullable();
            $table->json('request_data');
            $table->json('response_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tixstock_api_logs');
    }
};
