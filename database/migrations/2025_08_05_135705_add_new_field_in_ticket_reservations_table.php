<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ticket_reservations', function (Blueprint $table) {
            $table->string('tixstock_hold_id', 50)->nullable()->after('price');
            $table->text('tixstock_response')->nullable()->after('tixstock_hold_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ticket_reservations', function (Blueprint $table) {
            $table->dropColumn('tixstock_hold_id');
            $table->dropColumn('tixstock_response');
        });
    }
};
