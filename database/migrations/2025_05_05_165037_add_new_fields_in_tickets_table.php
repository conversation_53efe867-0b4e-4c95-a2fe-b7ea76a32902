<?php

use App\Enums\TicketQuantitySplitType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->decimal('face_value_price', 10, 2)->nullable();
            $table->enum('quantity_split_type', TicketQuantitySplitType::getValues())->default(TicketQuantitySplitType::ANY->value);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropColumn('face_value_price');
            $table->dropColumn('quantity_split_type');
        });
    }
};
