<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_restrictions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ticket_id')->constrained(table: 'tickets')->cascadeOnDelete();
            $table->foreignId('restriction_id')->constrained(table: 'restrictions')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_restrictions');
    }
};
