<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('countries', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->string('phone_code')->after('slug')->nullable();
            $table->string('currency_code')->after('phone_code')->nullable();
            $table->string('currency_name')->after('currency_code')->nullable();
            $table->string('currency_symbol')->after('currency_name')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('countries', function (Blueprint $table) {
            $table->string('name')->after('id')->nullable();
            $table->dropColumn('phone_code');
            $table->dropColumn('currency_code');
            $table->dropColumn('currency_name');
            $table->dropColumn('currency_symbol');
        });
    }
};
