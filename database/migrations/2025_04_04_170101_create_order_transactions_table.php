<?php

use App\Enums\OrderTransactionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_transactions', function (Blueprint $table) {
            $table->id();
            $table->enum('status', OrderTransactionStatus::getValues());

            $table->foreignId('order_id')->constrained('orders', 'id')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users', 'id')->cascadeOnDelete();

            $table->string('session_id')->index('session_id');
            $table->string('currency_code');

            $table->string('payment_intent_id')->index('payment_intent_id')->nullable();
            $table->string('payment_method_id')->nullable();
            $table->string('payment_method_type')->nullable();

            $table->decimal('total_amount', 12, 2)->nullable();

            $table->timestamp('paid_at')->nullable();
            $table->timestamp('refunded_at')->nullable();

            $table->string('card_brand')->nullable()->comment('Visa, Mastercard, etc.');
            $table->string('card_last_four')->nullable()->comment('Last four digits of card');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
