<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stadium_sectors', function (Blueprint $table) {
            $table->string('name')->after('stadium_id')->nullable();
            $table->foreignId('parent_id')->nullable()->after('name')->constrained(table: 'stadium_sectors')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stadium_sectors', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->dropForeign(['parent_id']);
            $table->dropColumn('parent_id');
        });
    }
};
