<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leagues', function (Blueprint $table) {
            $table->string('tixstock_id', 50)->nullable()->after('country_id');
            $table->dropForeign(['season_id']);
            $table->foreignId('season_id')->nullable()->change();

            $table->foreign('season_id')->references('id')->on('seasons')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leagues', function (Blueprint $table) {
            $table->dropColumn('tixstock_id');
            $table->dropForeign(['season_id']);
            $table->foreignId('season_id')->nullable(false)->change();
            $table->foreign('season_id')->references('id')->on('seasons')->cascadeOnDelete();
        });
    }
};
