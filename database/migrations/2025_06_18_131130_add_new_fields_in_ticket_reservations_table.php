<?php

use App\Enums\TicketReservationStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ticket_reservations', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->after('quantity')->nullable();
            $table->enum('status', TicketReservationStatus::getValues())->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ticket_reservations', function (Blueprint $table) {
            $table->dropColumn('price');
            $table->enum('status', TicketReservationStatus::getValues([TicketReservationStatus::PROCESSING->value]))->change();
        });
    }
};
