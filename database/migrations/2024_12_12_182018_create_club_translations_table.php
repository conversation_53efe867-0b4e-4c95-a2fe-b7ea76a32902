<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('club_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('club_id')->constrained(table: 'clubs')->cascadeOnDelete();
            $table->string('locale', 2)->index();
            $table->string('name');
            $table->text('description');
            $table->string('title');
            $table->text('detailed_description');
            $table->timestamps();
            $table->unique(['club_id', 'locale']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('club_translations');
    }
};
