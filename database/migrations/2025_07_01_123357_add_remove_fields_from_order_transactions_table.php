<?php

use App\Enums\OrderTransactionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            $table->enum('transaction_type', OrderTransactionType::getValues())->after('order_id');
            $table->string('stripe_charge_id')->after('payment_intent_id')->nullable();
            $table->string('stripe_refund_id')->after('stripe_charge_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_transactions', function (Blueprint $table) {
            $table->dropColumn('transaction_type');
            $table->dropColumn('stripe_charge_id');
            $table->dropColumn('stripe_refund_id');
        });
    }
};
