<?php

use App\Enums\TicketQuantitySplitType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->enum('quantity_split_type', TicketQuantitySplitType::getValues())->change();
            $table->integer('sell_in_multiples')->nullable()->after('quantity_split_type');
            $table->string('tixstock_id', 50)->nullable()->after('sector_id');
            $table->text('tixstock_data')->nullable()->after('tixstock_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->enum('status', TicketQuantitySplitType::getValues([TicketQuantitySplitType::ALL_TOGETHER->value, $TicketQuantitySplitType::IN_MULTIPLE->value]))->change();
            $table->dropColumn('sell_in_multiples');
            $table->dropColumn('tixstock_id');
            $table->dropColumn('tixstock_data');
        });
    }
};
