<?php

use App\Enums\PayoutBankAccountType;
use App\Enums\PayoutMethodStatus;
use App\Enums\PayoutMethodType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_payout_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users', 'id')->cascadeOnDelete();
            $table->enum('payout_method_type', PayoutMethodType::getValues());

            $table->string('country_code', 3);
            $table->enum('account_type', PayoutBankAccountType::getValues())->default(PayoutBankAccountType::INDIVIDUAL->value);
            $table->string('bank_name')->nullable();
            $table->string('branch_name')->nullable();
            $table->string('account_holder_name')->nullable();
            $table->string('account_number')->nullable();
            $table->string('iban')->nullable();
            $table->string('swift_code')->nullable();
            $table->string('ifsc_code')->nullable();
            $table->string('routing_number')->nullable();
            $table->string('sort_code')->nullable();
            $table->string('bsb')->nullable();
            $table->string('bank_code')->nullable();
            $table->string('branch_code')->nullable();
            $table->string('institution_number')->nullable();
            $table->string('transit_number')->nullable();

            $table->boolean('is_default')->default(false);
            $table->enum('status', PayoutMethodStatus::getValues());
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_payout_methods');
    }
};
