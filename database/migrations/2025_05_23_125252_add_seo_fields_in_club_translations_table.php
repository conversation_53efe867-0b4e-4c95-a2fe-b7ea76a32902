<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('club_translations', function (Blueprint $table) {
            $table->dropColumn('title');
            $table->string('meta_title')->after('detailed_description')->nullable();
            $table->string('meta_description')->after('meta_title')->nullable();
            $table->string('meta_keywords')->after('meta_description')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('club_translations', function (Blueprint $table) {
            $table->string('title')->after('description')->nullable();
            $table->dropColumn('meta_title');
            $table->dropColumn('meta_description');
            $table->dropColumn('meta_keywords');
        });
    }
};
