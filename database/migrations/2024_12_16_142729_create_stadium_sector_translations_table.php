<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stadium_sector_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stadium_sector_id')->constrained(table: 'stadium_sectors')->cascadeOnDelete();
            $table->string('locale', 2)->index();
            $table->string('name');
            $table->timestamps();
            $table->unique(['stadium_sector_id', 'locale']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stadium_sector_translations');
    }
};
