<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clubs', function (Blueprint $table) {
            $table->string('tixstock_id', 50)->nullable()->after('stadium_id');
            $table->dropForeign(['stadium_id']);
            $table->foreignId('stadium_id')->nullable()->change();

            $table->foreign('stadium_id')->references('id')->on('stadiums')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clubs', function (Blueprint $table) {
            $table->dropColumn('tixstock_id');
            $table->dropForeign(['stadium_id']);
            $table->foreignId('stadium_id')->nullable(false)->change();
            $table->foreign('stadium_id')->references('id')->on('stadiums')->cascadeOnDelete();
        });
    }
};
