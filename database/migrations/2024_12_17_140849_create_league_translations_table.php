<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('league_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('league_id')->constrained(table: 'leagues')->cascadeOnDelete();
            $table->string('locale', 2)->index();
            $table->string('name');
            $table->text('description');
            $table->timestamps();
            $table->unique(['league_id', 'locale']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('league_translations');
    }
};
