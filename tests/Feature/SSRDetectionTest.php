<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SSRDetectionTest extends TestCase
{
    /**
     * Test that SSR is detected for Node.js user agent
     */
    public function test_ssr_detected_for_nodejs_user_agent(): void
    {
        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (compatible; Node.js)',
        ])->get('/');

        $response->assertStatus(200);

        // Check if translations are loaded (this would be in the Inertia props)
        $response->assertInertia(fn ($page) =>
            $page->has('translations')
        );
    }

    /**
     * Test that home page loads translations for SSR requests
     */
    public function test_home_page_loads_translations_for_ssr(): void
    {
        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (compatible; Node.js)',
        ])->get('/');

        // The response should be successful
        $response->assertStatus(200);

        // Check if translations are loaded (this would be in the Inertia props)
        $response->assertInertia(fn ($page) =>
            $page->has('translations')
                ->has('translations.translations')
                ->has('translations.locale')
        );
    }

    /**
     * Test that SSR is not detected for regular browser requests
     */
    public function test_ssr_not_detected_for_browser_requests(): void
    {
        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'X-Requested-With' => 'XMLHttpRequest',
        ])->get('/');

        $response->assertStatus(200);

        // Check that translations are NOT loaded for regular requests
        $response->assertInertia(fn ($page) =>
            $page->missing('translations')
        );
    }

    /**
     * Test that home page does NOT load translations for regular browser requests
     */
    public function test_home_page_no_translations_for_browser(): void
    {
        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'X-Requested-With' => 'XMLHttpRequest',
        ])->get('/');

        $response->assertStatus(200);

        // Check that translations are NOT loaded for regular requests
        $response->assertInertia(fn ($page) =>
            $page->missing('translations')
        );
    }

    /**
     * Test that SSR is detected for requests with X-Inertia-SSR header
     */
    public function test_ssr_detected_for_inertia_ssr_header(): void
    {
        $response = $this->withHeaders([
            'X-Inertia-SSR' => 'true',
            'User-Agent' => 'Mozilla/5.0 (compatible; InertiaJS)',
        ])->get('/');

        $response->assertStatus(200);

        $response->assertInertia(fn ($page) =>
            $page->has('translations')
        );
    }



    /**
     * Test that authenticated routes don't get SSR detection
     */
    public function test_authenticated_routes_dont_get_ssr_detection(): void
    {
        // Create a user and authenticate
        $user = \App\Models\User::factory()->create();

        $response = $this->actingAs($user)
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (compatible; Node.js)',
            ])
            ->get('/my-account');

        $response->assertStatus(200);

        // Even with Node.js user agent, authenticated routes shouldn't load translations via SSR
        // because they're not in the detect-ssr middleware group
        $response->assertInertia(fn ($page) =>
            $page->missing('translations')
        );
    }
}
